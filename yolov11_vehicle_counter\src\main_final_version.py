import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import sys
import cv2
from ultralytics import YOLO
import numpy as np
from pathlib import Path
import logging

# 添加当前目录到Python路径，以便导入本地模块
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from tracker.deep_sort import DeepSort
    from utils.environment_manager import get_environment_manager
    from utils.model_manager import get_model_manager
    from utils.file_manager import get_file_manager
    from utils.performance_monitor import get_performance_monitor
    from utils.gpu_optimizer import get_gpu_optimizer
    from config.settings import settings
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在src目录下运行脚本")
    sys.exit(1)

# --- Configuration ---
track_history = {}
counted_ids = set()

def run_tracker(model_path, video_path, save_path, show_video=True):
    """Runs YOLOv11 object tracking, saves the output, and optionally displays it."""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    # 初始化GPU优化器
    gpu_optimizer = get_gpu_optimizer()
    gpu_report = gpu_optimizer.get_performance_report()
    logger.info(f"GPU状态: {gpu_report}")
    
    # 优化GPU内存
    gpu_optimizer.optimize_memory()
    
    # 使用模型管理器加载YOLO模型
    model_mgr = get_model_manager()
    print(f"Loading model from: {model_path}")
    
    # 获取备用模型路径
    env_manager = get_environment_manager()
    model_paths = env_manager.get_model_paths()
    fallback_paths = [path for key, path in model_paths.items() if path != model_path and path.exists()]
    
    try:
        model = model_mgr.load_yolo_model(Path(model_path), fallback_paths)
        
        # 将模型移动到最优设备
        optimal_device = gpu_optimizer.get_optimal_device()
        if optimal_device == 'cuda' and gpu_optimizer.gpu_available:
            model.to('cuda')
            logger.info("YOLO模型已移动到GPU")
        
        logger.info("YOLO模型加载成功")
    except Exception as e:
        logger.error(f"YOLO模型加载失败: {e}")
        return

    # --- Initialize DeepSORT ---
    # 使用模型管理器加载DeepSORT跟踪器
    reid_model_path = env_manager.get_reid_model_path()
    
    try:
        # 使用新的DeepSORT管理器
        deepsort_mgr = model_mgr.load_deepsort_tracker(reid_model_path)
        logger.info("DeepSORT管理器加载成功")
    except Exception as e:
        logger.error(f"DeepSORT管理器加载失败: {e}")
        return
    # ---

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)

    # --- Dashboard Configuration ---
    video_config = settings.get_video_config()
    dashboard_height = video_config.dashboard_height
    dashboard_color = video_config.dashboard_color
    new_h = h + dashboard_height
    # ---

    # Setup video writer with the new dimensions
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, new_h))
    print(f"Output video will be saved to: {save_path}")

    # 初始化性能监控器
    perf_monitor = get_performance_monitor()
    
    frame_idx = 0
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("Video processing completed.")
            break
        frame_idx += 1
        
        # 开始性能监控
        frame_start_time = perf_monitor.start_frame_processing()

        # --- Create the main canvas with the dashboard ---
        main_canvas = np.zeros((new_h, w, 3), dtype=np.uint8)
        main_canvas[:dashboard_height, :] = dashboard_color
        main_canvas[dashboard_height:, :] = frame
        # ---

        # --- Manual Tracking Loop ---
        # 1. Get detections from YOLO
        try:
            # 使用GPU优化参数进行预测
            yolo_optimization = gpu_optimizer.get_yolo_optimization()
            results = model.predict(
                frame, 
                conf=yolo_optimization['conf'],
                iou=yolo_optimization['iou'],
                max_det=yolo_optimization['max_det'],
                device=yolo_optimization['device'],
                half=yolo_optimization['half'],
                verbose=False
            )
        except Exception as e:
            logger.warning(f"帧 {frame_idx} 预测失败: {e}")
            continue
        detections = results[0].boxes.data.cpu().numpy()

        # 2. Format detections for DeepSORT
        # We need (x, y, w, h) and confidence
        xywhs = detections[:, :4]
        confs = detections[:, 4]
        clss = detections[:, 5]

        # 3. Update the tracker using DeepSORT manager with comprehensive error handling
        tracks = deepsort_mgr.safe_update(xywhs, confs, clss, frame, frame_idx)
        # ---

        # Draw the counting line
        cv2.line(main_canvas, (line_start[0], line_start[1] + dashboard_height), (line_end[0], line_end[1] + dashboard_height), video_config.line_color, 2)

        if len(tracks) > 0:
            for track in tracks:
                # track is now a numpy array [x1, y1, x2, y2, track_id, class_id]
                x1, y1, x2, y2, track_id, class_id = track.astype(int)

                # Draw the bounding box on the main canvas
                cv2.rectangle(main_canvas, (x1, y1 + dashboard_height), (x2, y2 + dashboard_height), video_config.bbox_color, 2)
                cv2.putText(main_canvas, f"ID: {track_id}", (x1, y1 + dashboard_height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, video_config.bbox_color, 2)

                # Counting logic
                center_x, center_y = int((x1 + x2) / 2), int((y1 + y2) / 2)
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                track_history[track_id] = (center_x, center_y)

        # --- Draw dashboard text ---
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1
        font_thickness = 2
        text_color = (255, 255, 255) # White

        cv2.putText(main_canvas, f"Left Count: {left_count}", (50, 60), font, font_scale, text_color, font_thickness)
        cv2.putText(main_canvas, f"Right Count: {right_count}", (w - 350, 60), font, font_scale, text_color, font_thickness)
        # ---

        if show_video:
            try:
                cv2.imshow("YOLOv11 Tracking and Counting", main_canvas)
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    break
            except cv2.error:
                print("(GUI not available) Cannot display video. Continuing to process and save the video.")
                show_video = False

        out.write(main_canvas)
        
        # 结束性能监控
        perf_metrics = perf_monitor.end_frame_processing(frame_start_time, frame_idx)

    cap.release()
    out.release()
    
    # 安全地关闭OpenCV窗口，避免Windows GUI错误
    try:
        cv2.destroyAllWindows()
    except Exception as e:
        logger.warning(f"关闭OpenCV窗口时出现错误（可忽略）: {e}")
        # 在Windows环境下，这个错误是常见的，不影响功能
    
    # 获取性能报告
    perf_report = perf_monitor.get_performance_report()
    
    # 记录实验结果
    file_mgr = get_file_manager()
    experiment_data = {
        'experiment_id': file_mgr.generate_experiment_id(),
        'model_path': str(model_path),
        'video_path': str(video_path),
        'output_path': str(save_path),
        'left_count': left_count,
        'right_count': right_count,
        'total_count': left_count + right_count,
        'processed_frames': frame_idx,
        'show_video': show_video,
        'performance_metrics': perf_report
    }
    file_mgr.log_experiment(experiment_data)
    
    print(f"Final Counts -> Left: {left_count}, Right: {right_count}")
    print(f"Output video saved successfully to {save_path}")
    print(f"Performance Summary - Average FPS: {perf_report['average_fps']:.1f}, Peak Memory: {perf_report['peak_memory_mb']:.1f}MB")
    logger.info(f"实验完成 - 总计数: {left_count + right_count}, 输出: {save_path}")
    logger.info(f"性能报告 - 平均FPS: {perf_report['average_fps']:.1f}, 峰值内存: {perf_report['peak_memory_mb']:.1f}MB")

if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, settings.get_logging_config().level),
        format=settings.get_logging_config().format
    )
    logger = logging.getLogger(__name__)
    
    try:
        # 使用环境管理器自动检测环境
        env_manager = get_environment_manager()
        config = env_manager.detect_environment()
        
        # 验证依赖
        missing_deps = env_manager.validate_dependencies()
        if missing_deps:
            logger.warning(f"发现缺失依赖: {missing_deps}")
        
        # 确保输出目录存在
        env_manager.ensure_output_directories()
        
        # 获取路径
        model_paths = env_manager.get_model_paths()
        video_paths = env_manager.get_video_paths()
        output_paths = env_manager.get_output_paths()
        
        # 设置路径
        model_path = model_paths['yolo_best']
        video_path = video_paths['input']
        
        # 使用文件管理器生成带时间戳的输出路径
        file_mgr = get_file_manager()
        save_path = file_mgr.create_timestamped_output_path(
            output_paths['video'], 
            "output_final", 
            config.environment_type
        )
        
        logger.info(f"环境类型: {config.environment_type}")
        logger.info(f"模型路径: {model_path}")
        logger.info(f"视频路径: {video_path}")
        logger.info(f"输出路径: {save_path}")
        
        # 运行跟踪器
        run_tracker(model_path, video_path, save_path, show_video=settings.get_video_config().show_video)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"错误: {e}")
        exit(1)