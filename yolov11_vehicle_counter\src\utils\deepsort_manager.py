"""
DeepSORT管理器 - 提供鲁棒的跟踪器管理和错误恢复
"""
import logging
import warnings
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
from .gpu_optimizer import get_gpu_optimizer

logger = logging.getLogger(__name__)

class DeepSORTManager:
    """管理DeepSORT跟踪器的初始化、配置和错误恢复"""
    
    def __init__(self):
        self.tracker = None
        self.config = None
        self.reid_model_path = None
        self.error_count = 0
        self.max_errors = 100  # 增加最大连续错误数
        self.reset_threshold = 15  # 降低重置阈值，更快响应
        self.consecutive_errors = 0  # 连续错误计数
        self.success_count = 0  # 成功处理计数
        self.current_config_level = 0  # 当前配置级别（0=安全，1=超安全，2=最小，3=紧急）
        self.frame_skip_count = 0  # 跳过的帧数统计
        
    def create_safe_config(self) -> Dict[str, Any]:
        """创建安全的DeepSORT配置，最小化Matrix错误"""
        return {
            'max_dist': 0.1,            # 适中的距离阈值
            'max_iou_distance': 0.5,    # 适中的IOU阈值
            'max_age': 8,               # 适中的跟踪生命周期
            'n_init': 2,                # 适中的初始化帧
            'nn_budget': 8,             # 适中的预算
            'device': 'cpu'             # 强制使用CPU避免GPU内存问题
        }
    
    def create_ultra_safe_config(self) -> Dict[str, Any]:
        """创建超安全配置"""
        return {
            'max_dist': 0.05,           # 严格的距离阈值
            'max_iou_distance': 0.3,    # 严格的IOU阈值
            'max_age': 5,               # 短的跟踪生命周期
            'n_init': 1,                # 最少的初始化帧
            'nn_budget': 5,             # 小的预算
            'device': 'cpu'
        }
    
    def create_minimal_config(self) -> Dict[str, Any]:
        """创建最小配置，用于错误恢复"""
        return {
            'max_dist': 0.02,           # 更严格的距离阈值
            'max_iou_distance': 0.2,    # 更严格的IOU阈值
            'max_age': 3,               # 更短的跟踪生命周期
            'n_init': 1,                # 最少的初始化帧
            'nn_budget': 3,             # 更小的预算
            'device': 'cpu'
        }
    
    def create_emergency_config(self) -> Dict[str, Any]:
        """创建紧急配置 - 最后的尝试"""
        return {
            'max_dist': 0.01,           # 几乎不允许距离变化
            'max_iou_distance': 0.1,    # 极严格的IOU
            'max_age': 1,               # 只跟踪1帧
            'n_init': 1,                # 最少初始化
            'nn_budget': 1,             # 最小预算
            'device': 'cpu'
        }
    
    def get_config_by_level(self, level: int) -> Dict[str, Any]:
        """根据级别获取配置"""
        configs = [
            self.create_safe_config(),        # 级别0：安全配置
            self.create_ultra_safe_config(),  # 级别1：超安全配置
            self.create_minimal_config(),     # 级别2：最小配置
            self.create_emergency_config()    # 级别3：紧急配置
        ]
        return configs[min(level, len(configs) - 1)]
    
    def load_tracker(self, reid_model_path: Path, custom_config: Optional[Dict] = None) -> bool:
        """
        加载DeepSORT跟踪器
        
        Args:
            reid_model_path: ReID模型路径
            custom_config: 自定义配置
            
        Returns:
            bool: 是否成功加载
        """
        try:
            # 导入DeepSORT相关模块
            from tracker.deep_sort import DeepSort
            
            self.reid_model_path = reid_model_path
            
            # 获取GPU优化器
            gpu_optimizer = get_gpu_optimizer()
            
            # 使用提供的配置或根据当前级别获取配置
            if custom_config:
                base_config = custom_config
            else:
                base_config = self.get_config_by_level(self.current_config_level)
            
            # 应用GPU优化
            self.config = gpu_optimizer.get_optimized_tracker_config(base_config)
            
            logger.info(f"使用配置加载DeepSORT: {self.config}")
            logger.info(f"GPU可用: {gpu_optimizer.gpu_available}, 设备: {self.config['device']}")
            
            # 创建跟踪器实例
            self.tracker = DeepSort(
                model_path=str(reid_model_path),
                device=self.config['device'],
                max_dist=self.config['max_dist'],
                max_iou_distance=self.config['max_iou_distance'],
                max_age=self.config['max_age'],
                n_init=self.config['n_init'],
                nn_budget=self.config['nn_budget']
            )
            
            self.error_count = 0
            logger.info("DeepSORT跟踪器加载成功")
            return True
            
        except Exception as e:
            logger.error(f"DeepSORT跟踪器加载失败: {e}")
            return False
    
    def reset_tracker(self) -> bool:
        """重置跟踪器，使用当前配置级别"""
        try:
            logger.warning("重置DeepSORT跟踪器...")
            
            # 使用当前配置级别重新创建跟踪器
            current_config = self.get_config_by_level(self.current_config_level)
            success = self.load_tracker(self.reid_model_path, current_config)
            
            if success:
                logger.info(f"跟踪器重置成功，使用配置级别 {self.current_config_level}")
                self.consecutive_errors = 0  # 重置连续错误计数
                return True
            else:
                logger.error("跟踪器重置失败")
                return False
                
        except Exception as e:
            logger.error(f"跟踪器重置异常: {e}")
            return False
    
    def safe_update(self, xywhs: np.ndarray, confs: np.ndarray, 
                   clss: np.ndarray, frame: np.ndarray, frame_idx: int) -> List:
        """
        安全的跟踪更新，包含智能错误处理和配置调整
        
        Args:
            xywhs: 检测框坐标
            confs: 置信度
            clss: 类别
            frame: 当前帧
            frame_idx: 帧索引
            
        Returns:
            List: 跟踪结果
        """
        if self.tracker is None:
            logger.error("跟踪器未初始化")
            return []
        
        try:
            # 验证输入数据
            if len(xywhs) == 0:
                return []
            
            # 动态调整置信度阈值（错误多时提高阈值）
            confidence_threshold = 0.3 + (self.consecutive_errors * 0.05)
            confidence_threshold = min(confidence_threshold, 0.8)  # 最高0.8
            
            # 过滤低置信度检测
            valid_mask = confs > confidence_threshold
            if not np.any(valid_mask):
                return []
            
            filtered_xywhs = xywhs[valid_mask]
            filtered_confs = confs[valid_mask]
            filtered_clss = clss[valid_mask]
            
            # 动态调整最大检测数量（错误多时减少数量）
            max_detections = max(5, 20 - self.consecutive_errors * 2)
            if len(filtered_xywhs) > max_detections:
                # 按置信度排序，取前N个
                sorted_indices = np.argsort(filtered_confs)[::-1][:max_detections]
                filtered_xywhs = filtered_xywhs[sorted_indices]
                filtered_confs = filtered_confs[sorted_indices]
                filtered_clss = filtered_clss[sorted_indices]
            
            # 尝试更新跟踪器
            tracks = self.tracker.update(filtered_xywhs, filtered_confs, filtered_clss, frame)
            
            # 成功更新，重置连续错误计数
            self.consecutive_errors = 0
            self.success_count += 1
            
            # 如果连续成功多次，可以尝试降低配置级别（提高性能）
            if self.success_count > 50 and self.current_config_level > 0:
                self._try_downgrade_config()
            
            return tracks
            
        except Exception as e:
            self.error_count += 1
            self.consecutive_errors += 1
            self.frame_skip_count += 1
            error_str = str(e)
            
            # 分类错误类型
            if "positive definite" in error_str or "leading minor" in error_str:
                logger.warning(f"帧 {frame_idx} DeepSORT Matrix错误，跳过跟踪: {error_str}")
            elif error_str.isdigit():
                logger.warning(f"帧 {frame_idx} DeepSORT数值错误(错误码:{error_str})，跳过跟踪")
            else:
                logger.warning(f"帧 {frame_idx} DeepSORT其他错误，跳过跟踪: {error_str}")
            
            # 智能配置调整：连续错误时升级到更保守的配置
            if self.consecutive_errors >= 5 and self.current_config_level < 3:
                self._try_upgrade_config()
            
            # 检查是否需要重置跟踪器
            if self.consecutive_errors >= self.reset_threshold:
                logger.warning(f"连续错误次数达到阈值({self.consecutive_errors})，尝试重置跟踪器")
                if self.reset_tracker():
                    logger.info("跟踪器重置成功，继续处理")
                else:
                    logger.error("跟踪器重置失败，将继续使用当前跟踪器")
            
            # 如果错误过多，返回空结果
            if self.error_count >= self.max_errors:
                logger.error(f"错误次数过多({self.error_count})，停止跟踪")
                return []
            
            return []
    
    def _try_upgrade_config(self):
        """尝试升级到更保守的配置"""
        if self.current_config_level < 3:
            old_level = self.current_config_level
            self.current_config_level += 1
            logger.info(f"升级配置级别: {old_level} -> {self.current_config_level}")
            
            # 重新加载跟踪器
            if self.reset_tracker():
                logger.info("配置升级成功")
            else:
                # 如果失败，回退配置级别
                self.current_config_level = old_level
                logger.warning("配置升级失败，回退到原配置")
    
    def _try_downgrade_config(self):
        """尝试降级到性能更好的配置"""
        if self.current_config_level > 0:
            old_level = self.current_config_level
            self.current_config_level -= 1
            logger.info(f"降级配置级别: {old_level} -> {self.current_config_level}")
            
            # 重新加载跟踪器
            if self.reset_tracker():
                logger.info("配置降级成功")
                self.success_count = 0  # 重置成功计数
            else:
                # 如果失败，回退配置级别
                self.current_config_level = old_level
                logger.warning("配置降级失败，回退到原配置")
    
    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计信息"""
        return {
            'total_errors': self.error_count,
            'consecutive_errors': self.consecutive_errors,
            'success_count': self.success_count,
            'frame_skip_count': self.frame_skip_count,
            'current_config_level': self.current_config_level,
            'max_errors': self.max_errors,
            'reset_threshold': self.reset_threshold
        }
    
    def is_healthy(self) -> bool:
        """检查跟踪器是否健康"""
        return self.tracker is not None and self.error_count < self.max_errors

# 全局实例
_deepsort_manager = None

def get_deepsort_manager() -> DeepSORTManager:
    """获取DeepSORT管理器的全局实例"""
    global _deepsort_manager
    if _deepsort_manager is None:
        _deepsort_manager = DeepSORTManager()
    return _deepsort_manager