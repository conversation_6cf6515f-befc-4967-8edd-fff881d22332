#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 黄金版Web应用后端
基于真正有效的main_golden.py，使用YOLO内置跟踪，简单直接，效果最佳
"""

import os
import sys
import uuid
import time
import json
import threading
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file, render_template
from werkzeug.utils import secure_filename
import cv2
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    print("✅ ultralytics已安装")
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️  警告: ultralytics未安装，将使用模拟模式")

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB限制

# 全局变量
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv'}

# 任务状态存储
task_status = {}
task_results = {}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class GoldenVehicleCounter:
    """黄金版车辆计数器 - 基于main_golden.py的最佳实现"""
    
    def __init__(self, model_name='yolov11m', confidence=0.5):
        self.model_name = model_name
        self.confidence = confidence
        self.model = None
        
        # 跟踪相关 - 完全按照黄金版本
        self.track_history = {}
        self.counted_ids = set()
        
        # 计数结果
        self.left_count = 0
        self.right_count = 0
        
        self.load_model()
    
    def load_model(self):
        """加载YOLO模型 - 黄金版本逻辑"""
        if not YOLO_AVAILABLE:
            print("⚠️  YOLO不可用，使用模拟模式")
            return
        
        try:
            # 尝试加载自定义训练的模型
            custom_model_paths = [
                "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                "../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                "../../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
            ]
            
            model_loaded = False
            for model_path in custom_model_paths:
                if os.path.exists(model_path):
                    self.model = YOLO(model_path)
                    print(f"✅ 成功加载自定义模型: {model_path}")
                    model_loaded = True
                    break
            
            if not model_loaded:
                # 使用预训练模型
                model_path = f"{self.model_name}.pt"
                self.model = YOLO(model_path)
                print(f"✅ 成功加载预训练模型: {model_path}")
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def process_video(self, input_path, output_path, task_id, settings):
        """处理视频文件 - 完全基于黄金版本的逻辑"""
        try:
            # 重置计数器 - 按照黄金版本
            self.track_history.clear()
            self.counted_ids.clear()
            self.left_count = 0
            self.right_count = 0
            
            print(f"🎬 开始处理视频: {input_path}")
            print(f"📊 设置: {settings}")
            
            # 更新任务状态
            task_status[task_id] = {
                'status': 'processing',
                'progress': 0,
                'message': '正在加载模型...',
                'vehicle_count': 0,
                'current_fps': 0
            }
            
            if self.model is None:
                raise Exception("模型未加载")
            
            # 打开视频 - 按照黄金版本
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {input_path}")
            
            # 获取视频属性 - 按照黄金版本
            w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            line_x = w // 2
            line_start, line_end = (line_x, 0), (line_x, h)
            
            print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
            print(f"📏 计数线位置: x={line_x}")
            
            # 设置输出视频编码器 - 按照黄金版本
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
            print(f"💾 输出视频: {output_path}")
            
            frame_count = 0
            start_time = time.time()
            
            while cap.isOpened():
                success, frame = cap.read()
                if not success:
                    print("✅ 视频处理完成")
                    break
                
                frame_count += 1
                
                # 更新进度
                progress = (frame_count / total_frames) * 100 if total_frames > 0 else 0
                current_time = time.time()
                elapsed_time = current_time - start_time
                current_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                task_status[task_id].update({
                    'progress': progress,
                    'message': f'处理第 {frame_count}/{total_frames} 帧',
                    'vehicle_count': self.left_count + self.right_count,
                    'current_fps': current_fps
                })
                
                # 使用YOLO内置跟踪 - 这是黄金版本的关键！
                results = self.model.track(frame, persist=True, verbose=False, conf=self.confidence)
                
                # 使用YOLO内置的plot方法获得最佳可视化效果 - 按照黄金版本
                annotated_frame = results[0].plot()
                
                # 绘制计数线 - 按照黄金版本
                cv2.line(annotated_frame, line_start, line_end, (0, 255, 0), 2)
                
                # 处理跟踪结果 - 完全按照黄金版本的逻辑
                if results[0].boxes is not None and results[0].boxes.id is not None:
                    boxes = results[0].boxes.xywh.cpu()
                    track_ids = results[0].boxes.id.int().cpu().tolist()

                    for box, track_id in zip(boxes, track_ids):
                        center_x, center_y = int(box[0]), int(box[1])
                        
                        # 简单直接的计数逻辑 - 完全按照黄金版本
                        if track_id in self.track_history:
                            prev_x, _ = self.track_history[track_id]
                            if track_id not in self.counted_ids:
                                # 从左到右穿越
                                if prev_x < line_x and center_x >= line_x:
                                    self.right_count += 1
                                    self.counted_ids.add(track_id)
                                    print(f"车辆 {track_id} 从左到右穿越，右侧计数: {self.right_count}")
                                # 从右到左穿越
                                elif prev_x > line_x and center_x <= line_x:
                                    self.left_count += 1
                                    self.counted_ids.add(track_id)
                                    print(f"车辆 {track_id} 从右到左穿越，左侧计数: {self.left_count}")
                        
                        # 更新历史位置 - 按照黄金版本
                        self.track_history[track_id] = (center_x, center_y)
                
                # 绘制计数信息 - 按照黄金版本
                cv2.putText(annotated_frame, f"Left Count: {self.left_count}", (50, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
                cv2.putText(annotated_frame, f"Right Count: {self.right_count}", (w - 300, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                
                # 保存帧 - 按照黄金版本
                out.write(annotated_frame)
                
                # 进度显示 - 按照黄金版本
                if frame_count % 100 == 0:
                    print(f"处理进度: {frame_count} 帧, 左侧: {self.left_count}, 右侧: {self.right_count}")
            
            # 清理资源 - 按照黄金版本
            cap.release()
            out.release()
            
            # 计算最终结果
            processing_time = time.time() - start_time
            avg_fps = frame_count / processing_time if processing_time > 0 else 0
            
            # 保存结果
            task_results[task_id] = {
                'vehicle_left': self.left_count,
                'vehicle_right': self.right_count,
                'people_total': 0,  # 黄金版本不统计人员
                'processed_frames': frame_count,
                'processing_time': f"{processing_time:.2f}s",
                'avg_fps': avg_fps,
                'avg_confidence': self.confidence,
                'completion_time': datetime.now().isoformat()
            }
            
            # 更新最终状态
            task_status[task_id] = {
                'status': 'completed',
                'progress': 100,
                'message': '处理完成',
                'vehicle_count': self.left_count + self.right_count,
                'current_fps': avg_fps
            }
            
            print("=" * 50)
            print("🎉 处理完成！")
            print(f"📊 最终计数结果:")
            print(f"   左侧: {self.left_count}")
            print(f"   右侧: {self.right_count}")
            print(f"   总计: {self.left_count + self.right_count}")
            print(f"💾 输出视频: {output_path}")
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ 视频处理失败: {e}")
            import traceback
            traceback.print_exc()
            task_status[task_id] = {
                'status': 'error',
                'progress': 0,
                'message': f'处理失败: {str(e)}',
                'vehicle_count': 0,
                'current_fps': 0
            }

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_video():
    """上传视频文件"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 生成任务ID和安全文件名
        task_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{filename}")
        
        # 保存文件
        file.save(file_path)
        print(f"📁 文件已保存: {file_path}")
        
        # 初始化任务状态
        task_status[task_id] = {
            'status': 'uploaded',
            'progress': 0,
            'message': '文件上传成功',
            'vehicle_count': 0,
            'current_fps': 0,
            'file_path': file_path
        }
        
        return jsonify({
            'task_id': task_id,
            'message': '文件上传成功'
        })
        
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/process', methods=['POST'])
def process_video():
    """开始处理视频"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        settings = data.get('settings', {})
        
        print(f"🚀 开始处理任务: {task_id}")
        print(f"⚙️  设置: {settings}")
        
        if not task_id or task_id not in task_status:
            return jsonify({'error': '无效的任务ID'}), 400
        
        if task_status[task_id]['status'] != 'uploaded':
            return jsonify({'error': '任务状态错误'}), 400
        
        # 获取输入文件路径
        input_path = task_status[task_id]['file_path']
        output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_processed.mp4")
        
        print(f"📥 输入文件: {input_path}")
        print(f"📤 输出文件: {output_path}")
        
        # 创建黄金版车辆计数器
        counter = GoldenVehicleCounter(
            model_name=settings.get('model', 'yolov11m'),
            confidence=settings.get('confidence', 0.5)
        )
        
        # 在后台线程中处理视频
        thread = threading.Thread(
            target=counter.process_video,
            args=(input_path, output_path, task_id, settings)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'message': '开始AI智能分析',
            'task_id': task_id
        })
        
    except Exception as e:
        print(f"❌ 处理启动失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """获取任务状态"""
    if task_id not in task_status:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(task_status[task_id])

@app.route('/api/results/<task_id>')
def get_results(task_id):
    """获取处理结果"""
    if task_id not in task_results:
        return jsonify({'error': '结果不存在'}), 404
    
    return jsonify(task_results[task_id])

@app.route('/api/download/<task_id>')
def download_video(task_id):
    """下载处理后的视频"""
    output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_processed.mp4")
    
    if not os.path.exists(output_path):
        return jsonify({'error': '文件不存在'}), 404
    
    return send_file(
        output_path,
        as_attachment=True,
        download_name=f"golden_processed_video_{task_id}.mp4",
        mimetype='video/mp4'
    )

if __name__ == '__main__':
    print("🏆 YOLOv11车辆计数器 - 黄金版Web服务器")
    print("=" * 70)
    print("🌟 黄金版特色:")
    print("   ✅ 基于真正有效的main_golden.py")
    print("   🎯 使用YOLO内置跟踪，简单直接")
    print("   📊 简单有效的计数逻辑")
    print("   🎨 YOLO内置plot方法，最佳可视化")
    print("   ⚡ 经过验证的稳定算法")
    print("=" * 70)
    print("🌐 访问地址: http://localhost:5000")
    print("📁 上传目录:", os.path.abspath(UPLOAD_FOLDER))
    print("📁 输出目录:", os.path.abspath(OUTPUT_FOLDER))
    
    if not YOLO_AVAILABLE:
        print("\n⚠️  注意: YOLO不可用，运行在模拟模式")
        print("💡 安装ultralytics以启用真实AI检测:")
        print("   pip install ultralytics")
    
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)