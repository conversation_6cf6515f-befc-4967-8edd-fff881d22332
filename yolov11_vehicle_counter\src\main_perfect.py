#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 完美版本
- 正确显示类别名称（car、person等）
- 计数线标签移到上方，不遮挡视野
- 分别统计车辆穿越和人员总数
- 按ID去重，避免重复计算
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import argparse
from datetime import datetime

# --- Configuration ---
track_history = {}
# 分别记录不同类型的计数
vehicle_crossed_ids = set()  # 已穿越计数线的车辆ID
person_seen_ids = set()      # 已见过的人员ID
# ID映射管理
original_to_clean_id = {}
next_clean_id = 1

# COCO类别映射
CLASS_NAMES = {
    0: 'person',
    1: 'bicycle', 
    2: 'car',
    3: 'motorcycle',
    4: 'airplane',
    5: 'bus',
    6: 'train',
    7: 'truck',
    8: 'boat'
}

def get_clean_id(original_id):
    """获取清洁的ID（从1开始按顺序分配）"""
    global next_clean_id, original_to_clean_id
    
    if original_id not in original_to_clean_id:
        original_to_clean_id[original_id] = next_clean_id
        next_clean_id += 1
    
    return original_to_clean_id[original_id]

def is_vehicle(class_id):
    """判断是否为车辆"""
    vehicle_classes = {2, 3, 5, 7}  # car, motorcycle, bus, truck
    return int(class_id) in vehicle_classes

def create_perfect_ui_overlay(frame, vehicle_left, vehicle_right, person_total, line_x):
    """创建完美的UI覆盖层"""
    h, w = frame.shape[:2]
    
    # 创建半透明覆盖层
    overlay = frame.copy()
    
    # 左上角 - 车辆穿越统计区域
    cv2.rectangle(overlay, (10, 10), (400, 140), (139, 69, 19), -1)  # 深蓝色
    # 右上角 - 人员总数统计区域  
    cv2.rectangle(overlay, (w-410, 10), (w-10, 140), (19, 139, 69), -1)  # 深绿色
    
    # 混合原图和覆盖层
    alpha = 0.85
    frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
    
    # 字体设置
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 1.5
    thickness = 3
    
    # 左上角 - 车辆穿越统计
    cv2.putText(frame, "VEHICLES CROSSED", (20, 40), font, 0.7, (255, 255, 255), 2)
    cv2.putText(frame, f"Left: {vehicle_left:03d}", (20, 75), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"Right: {vehicle_right:03d}", (20, 110), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"Total: {vehicle_left + vehicle_right:03d}", (200, 75), font, font_scale, (255, 255, 0), thickness)
    
    # 右上角 - 人员总数统计
    cv2.putText(frame, "PEOPLE DETECTED", (w-390, 40), font, 0.7, (255, 255, 255), 2)
    cv2.putText(frame, f"Total: {person_total:03d}", (w-200, 85), font, font_scale, (255, 255, 255), thickness)
    
    # 绘制计数线
    cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 4)
    
    # 计数线标签 - 移到上方，不遮挡视野
    label_y = 180  # 放在统计区域下方
    cv2.rectangle(frame, (line_x-80, label_y), (line_x+80, label_y+40), (0, 255, 0), -1)
    cv2.putText(frame, "VEHICLE", (line_x-70, label_y+15), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(frame, "COUNT LINE", (line_x-75, label_y+32), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # 添加方向箭头
    arrow_y = label_y + 20
    # 左箭头
    arrow_left = np.array([[line_x-120, arrow_y], [line_x-90, arrow_y-10], [line_x-90, arrow_y+10]], np.int32)
    cv2.fillPoly(frame, [arrow_left], (255, 255, 0))
    cv2.putText(frame, "LEFT", (line_x-140, arrow_y+5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
    
    # 右箭头  
    arrow_right = np.array([[line_x+120, arrow_y], [line_x+90, arrow_y-10], [line_x+90, arrow_y+10]], np.int32)
    cv2.fillPoly(frame, [arrow_right], (255, 255, 0))
    cv2.putText(frame, "RIGHT", (line_x+95, arrow_y+5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
    
    return frame

def draw_perfect_detections(frame, results, line_x):
    """绘制完美的检测框，正确显示类别名称"""
    if results[0].boxes is None or results[0].boxes.id is None:
        return frame
    
    boxes = results[0].boxes.xyxy.cpu().numpy()
    confidences = results[0].boxes.conf.cpu().numpy()
    class_ids = results[0].boxes.cls.cpu().numpy()
    original_track_ids = results[0].boxes.id.int().cpu().tolist()
    
    for box, conf, cls_id, original_id in zip(boxes, confidences, class_ids, original_track_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # 获取清洁的ID
        clean_id = get_clean_id(original_id)
        
        # 获取正确的类别名称
        class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
        
        # 为不同类别选择颜色
        if class_name == 'car':
            color = (0, 255, 255)  # 黄色
        elif class_name == 'truck':
            color = (255, 0, 255)  # 紫色
        elif class_name == 'bus':
            color = (255, 165, 0)  # 橙色
        elif class_name == 'motorcycle':
            color = (0, 255, 0)    # 绿色
        elif class_name == 'person':
            color = (255, 255, 255)  # 白色
        else:
            color = (128, 128, 128)  # 灰色
        
        # 绘制检测框
        thickness = 3 if is_vehicle(cls_id) else 2
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
        
        # 计算中心点
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # 绘制中心点
        cv2.circle(frame, (center_x, center_y), 5, color, -1)
        
        # 绘制轨迹（使用原始ID作为键）
        if original_id in track_history:
            prev_x, prev_y = track_history[original_id]
            cv2.line(frame, (prev_x, prev_y), (center_x, center_y), color, 2)
        
        # 绘制标签 - 正确显示类别名称
        label = f"ID:{clean_id} {class_name}"
        if conf > 0:
            label += f" {conf:.2f}"
            
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        
        # 标签背景
        cv2.rectangle(frame, (x1, y1-25), (x1 + label_size[0] + 10, y1), color, -1)
        
        # 标签文字
        cv2.putText(frame, label, (x1+5, y1-8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 如果是车辆且靠近计数线，高亮显示
        if is_vehicle(cls_id) and abs(center_x - line_x) < 40:
            cv2.rectangle(frame, (x1-5, y1-5), (x2+5, y2+5), (0, 0, 255), 3)
            # 添加"即将计数"提示
            cv2.putText(frame, "COUNTING ZONE", (x1, y2+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    return frame

def run_tracker(model_path, video_path, save_path, show_video=True):
    """运行完美版本的车辆和人员跟踪器"""
    global track_history, vehicle_crossed_ids, person_seen_ids, original_to_clean_id, next_clean_id
    
    # 重置全局变量
    vehicle_left_count, vehicle_right_count = 0, 0
    track_history.clear()
    vehicle_crossed_ids.clear()
    person_seen_ids.clear()
    original_to_clean_id.clear()
    next_clean_id = 1

    print(f"🚗 加载模型: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ 无法打开视频文件: {video_path}")
        return

    # 获取视频属性
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
    
    line_x = w // 2
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"💾 输出视频: {save_path}")

    frame_count = 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("✅ 视频处理完成")
            break

        frame_count += 1
        
        # YOLO跟踪
        results = model.track(frame, persist=True, verbose=False)
        
        # 绘制检测结果
        annotated_frame = draw_perfect_detections(frame.copy(), results, line_x)
        
        # 处理跟踪结果
        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            class_ids = results[0].boxes.cls.cpu().numpy()
            original_track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                clean_id = get_clean_id(original_id)
                class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
                
                # 人员统计 - 按ID去重
                if class_name == 'person':
                    if original_id not in person_seen_ids:
                        person_seen_ids.add(original_id)
                        print(f"👤 发现新人员 ID:{clean_id}")
                
                # 车辆穿越统计
                elif is_vehicle(cls_id):
                    if original_id in track_history:
                        prev_x, _ = track_history[original_id]
                        if original_id not in vehicle_crossed_ids:
                            # 从左到右穿越
                            if prev_x < line_x and center_x >= line_x:
                                vehicle_right_count += 1
                                vehicle_crossed_ids.add(original_id)
                                print(f"🚙 车辆 ID:{clean_id} ({class_name}) 从左到右穿越，右侧计数: {vehicle_right_count}")
                            # 从右到左穿越
                            elif prev_x > line_x and center_x <= line_x:
                                vehicle_left_count += 1
                                vehicle_crossed_ids.add(original_id)
                                print(f"🚗 车辆 ID:{clean_id} ({class_name}) 从右到左穿越，左侧计数: {vehicle_left_count}")
                
                # 更新历史位置
                track_history[original_id] = (center_x, center_y)

        # 创建完美UI覆盖层
        annotated_frame = create_perfect_ui_overlay(
            annotated_frame, 
            vehicle_left_count, 
            vehicle_right_count, 
            len(person_seen_ids), 
            line_x
        )
        
        # 添加底部状态信息
        progress = frame_count / total_frames if total_frames > 0 else 0
        status_y = h - 60
        
        # 状态背景
        cv2.rectangle(annotated_frame, (10, status_y), (w-10, h-10), (50, 50, 50), -1)
        
        # 状态信息
        status_text = f"Frame: {frame_count}/{total_frames} ({progress*100:.1f}%) | Active IDs: {len(original_to_clean_id)} | Vehicles: {vehicle_left_count + vehicle_right_count} | People: {len(person_seen_ids)}"
        cv2.putText(annotated_frame, status_text, (20, status_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 显示视频
        if show_video:
            try:
                cv2.imshow("YOLOv11 Perfect Vehicle & People Counter", annotated_frame)
                key = cv2.waitKey(1) & 0xFF
                if key == ord("q"):
                    print("🛑 用户请求退出")
                    break
                elif key == ord("r"):
                    # 重置所有计数
                    original_to_clean_id.clear()
                    next_clean_id = 1
                    vehicle_crossed_ids.clear()
                    person_seen_ids.clear()
                    vehicle_left_count = vehicle_right_count = 0
                    print("🔄 所有计数已重置")
            except cv2.error:
                print("⚠️ GUI不可用，继续处理但不显示窗口")
                show_video = False

        # 保存帧
        out.write(annotated_frame)
        
        # 进度显示
        if frame_count % 100 == 0:
            print(f"📊 进度: {frame_count}/{total_frames} ({progress*100:.1f}%) | 车辆穿越: {vehicle_left_count + vehicle_right_count} | 人员总数: {len(person_seen_ids)}")

    # 清理资源
    cap.release()
    out.release()
    
    # 安全关闭窗口
    try:
        cv2.destroyAllWindows()
    except:
        pass
    
    # 最终结果
    print("=" * 80)
    print("🎉 完美统计完成！")
    print(f"📊 车辆穿越统计:")
    print(f"   ⬅️  左侧穿越: {vehicle_left_count:3d} 辆")
    print(f"   ➡️  右侧穿越: {vehicle_right_count:3d} 辆")
    print(f"   🚗 车辆总计: {vehicle_left_count + vehicle_right_count:3d} 辆")
    print(f"👥 人员检测统计:")
    print(f"   👤 人员总数: {len(person_seen_ids):3d} 人")
    print(f"📹 处理帧数: {frame_count:,} 帧")
    print(f"🆔 最大ID号: {next_clean_id - 1}")
    print(f"💾 输出文件: {save_path}")
    print("=" * 80)
    print("💡 提示: 播放时按 'q' 退出，按 'r' 重置所有计数")

if __name__ == '__main__':
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_perfect_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - 完美版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - 完美版本")
    print("✨ 特性:")
    print("   🏷️  正确显示类别名称 (car, person等)")
    print("   📍 计数线标签移到上方，不遮挡视野")
    print("   🚗 车辆：只统计穿越计数线的")
    print("   👥 人员：按ID统计总数，不重复")
    print("   🆔 ID从1开始按顺序分配")
    print("=" * 60)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 60)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)