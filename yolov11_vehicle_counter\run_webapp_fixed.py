#!/usr/bin/env python3
"""
启动修复版YOLOv11车辆计数器Web应用
集成了main_beautiful_1080p.py的处理逻辑
"""

import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """主函数"""
    print("🚗 YOLOv11车辆计数器 - Web应用修复版")
    print("=" * 50)
    print("🔧 修复内容:")
    print("   ✅ 集成main_beautiful_1080p.py的处理逻辑")
    print("   ✅ 真正的视频处理和追踪")
    print("   ✅ 正确的撞线计数功能")
    print("   ✅ 只使用训练好的yolov11m模型")
    print("   ✅ 处理后的视频包含轨迹和计数")
    print("=" * 50)
    
    # 检查webapp目录
    webapp_dir = Path("webapp")
    if not webapp_dir.exists():
        print(f"❌ webapp目录不存在: {webapp_dir}")
        return
    
    # 检查修复版app文件
    app_fixed = webapp_dir / "app_fixed.py"
    if not app_fixed.exists():
        print(f"❌ 修复版app文件不存在: {app_fixed}")
        return
    
    print("🚀 启动修复版Web服务器...")
    print("🌐 访问地址: http://localhost:5000")
    print("📱 功能特性:")
    print("   - 真实的视频处理（不是模拟）")
    print("   - 车辆追踪和轨迹显示")
    print("   - 撞线计数功能")
    print("   - 美观的1080p优化界面")
    print("   - 处理后视频下载")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5000')
            print("🌐 浏览器已打开")
        except:
            print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:5000")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 切换到webapp目录并启动修复版
        import os
        os.chdir("webapp")
        
        # 导入并启动修复版app
        sys.path.insert(0, ".")
        from app_fixed import app
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查:")
        print("   - 依赖包是否正确安装")
        print("   - 模型文件是否存在")
        print("   - 端口5000是否被占用")

if __name__ == "__main__":
    main()