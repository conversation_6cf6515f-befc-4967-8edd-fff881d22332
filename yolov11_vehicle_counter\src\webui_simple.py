#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 简化Web界面（本地测试版）
专注于跑通基本功能
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
import gradio as gr
import tempfile
import time
from pathlib import Path
from datetime import datetime

try:
    from ultralytics import YOLO
    print("✅ ultralytics导入成功")
except ImportError as e:
    print(f"❌ ultralytics导入失败: {e}")
    print("请运行: pip install ultralytics")

# --- Global Variables ---
track_history = {}
vehicle_crossed_ids = set()
person_seen_ids = set()
original_to_clean_id = {}
next_clean_id = 1

# COCO类别映射
CLASS_NAMES = {
    0: 'person',
    1: 'bicycle', 
    2: 'car',
    3: 'motorcycle',
    4: 'airplane',
    5: 'bus',
    6: 'train',
    7: 'truck',
    8: 'boat'
}

def get_clean_id(original_id):
    """获取清洁的ID（从1开始按顺序分配）"""
    global next_clean_id, original_to_clean_id
    
    if original_id not in original_to_clean_id:
        original_to_clean_id[original_id] = next_clean_id
        next_clean_id += 1
    
    return original_to_clean_id[original_id]

def is_vehicle(class_id):
    """判断是否为车辆"""
    vehicle_classes = {1, 2, 3, 5, 7}  # bicycle, car, motorcycle, bus, truck
    return int(class_id) in vehicle_classes

def reset_counters():
    """重置所有计数器"""
    global track_history, vehicle_crossed_ids, person_seen_ids, original_to_clean_id, next_clean_id
    track_history.clear()
    vehicle_crossed_ids.clear()
    person_seen_ids.clear()
    original_to_clean_id.clear()
    next_clean_id = 1

def process_single_frame(frame, model, line_x):
    """处理单帧图像"""
    global track_history, vehicle_crossed_ids, person_seen_ids
    
    # YOLO跟踪
    results = model.track(frame, persist=True, verbose=False)
    
    # 获取带注释的帧
    annotated_frame = results[0].plot()
    
    # 绘制计数线
    h, w = annotated_frame.shape[:2]
    cv2.line(annotated_frame, (line_x, 0), (line_x, h), (0, 255, 0), 4)
    
    vehicle_left_count = len([id for id in vehicle_crossed_ids if id % 2 == 0])  # 简化计数
    vehicle_right_count = len([id for id in vehicle_crossed_ids if id % 2 == 1])
    
    # 处理跟踪结果
    if results[0].boxes is not None and results[0].boxes.id is not None:
        boxes = results[0].boxes.xywh.cpu()
        class_ids = results[0].boxes.cls.cpu().numpy()
        original_track_ids = results[0].boxes.id.int().cpu().tolist()

        for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
            center_x, center_y = int(box[0]), int(box[1])
            clean_id = get_clean_id(original_id)
            class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
            
            # 人员统计
            if class_name == 'person':
                if original_id not in person_seen_ids:
                    person_seen_ids.add(original_id)
            
            # 车辆穿越统计
            elif is_vehicle(cls_id):
                if original_id in track_history:
                    prev_x, _ = track_history[original_id]
                    if original_id not in vehicle_crossed_ids:
                        # 从左到右穿越
                        if prev_x < line_x and center_x >= line_x:
                            vehicle_crossed_ids.add(original_id)
                        # 从右到左穿越
                        elif prev_x > line_x and center_x <= line_x:
                            vehicle_crossed_ids.add(original_id)
            
            # 更新历史位置
            track_history[original_id] = (center_x, center_y)
    
    # 添加统计信息到图像
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(annotated_frame, f"Vehicles: {len(vehicle_crossed_ids)}", (10, 30), font, 1, (255, 255, 255), 2)
    cv2.putText(annotated_frame, f"People: {len(person_seen_ids)}", (10, 70), font, 1, (255, 255, 255), 2)
    
    return annotated_frame, len(vehicle_crossed_ids), len(person_seen_ids)

def process_video_simple(video_file):
    """简化的视频处理函数"""
    if video_file is None:
        return None, "请上传视频文件", 0, 0
    
    print(f"📹 开始处理视频: {video_file}")
    
    # 重置计数器
    reset_counters()
    
    try:
        # 加载模型
        model_path = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
        if not Path(model_path).exists():
            model_path = "yolov11m.pt"  # 使用预训练模型
        
        print(f"🤖 加载模型: {model_path}")
        model = YOLO(model_path)
        
        # 打开视频
        cap = cv2.VideoCapture(str(video_file))
        if not cap.isOpened():
            return None, "无法打开视频文件", 0, 0
        
        # 获取视频属性
        w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        line_x = w // 2
        frame_count = 0
        last_frame = None
        
        print(f"📊 视频信息: {w}x{h}, {total_frames}帧")
        
        # 处理视频（每10帧处理一次，加快速度）
        while cap.isOpened() and frame_count < total_frames:
            success, frame = cap.read()
            if not success:
                break
            
            frame_count += 1
            
            # 每10帧处理一次
            if frame_count % 10 == 0:
                processed_frame, vehicle_count, people_count = process_single_frame(frame, model, line_x)
                last_frame = processed_frame
                
                print(f"📈 进度: {frame_count}/{total_frames} | 车辆: {vehicle_count} | 人员: {people_count}")
        
        cap.release()
        
        if last_frame is not None:
            # 转换颜色空间
            last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)
            
            stats = f"""
            处理完成！
            
            📊 最终统计:
            🚗 车辆总数: {len(vehicle_crossed_ids)} 辆
            👥 人员总数: {len(person_seen_ids)} 人
            📹 处理帧数: {frame_count} 帧
            """
            
            return last_frame_rgb, stats, len(vehicle_crossed_ids), len(person_seen_ids)
        else:
            return None, "处理失败", 0, 0
            
    except Exception as e:
        print(f"❌ 处理错误: {e}")
        return None, f"处理错误: {str(e)}", 0, 0

def create_simple_interface():
    """创建简化的Gradio界面"""
    
    with gr.Blocks(title="YOLOv11 车辆计数器 - 本地测试版") as demo:
        gr.Markdown("""
        # 🚗 YOLOv11 车辆计数器 - 本地测试版
        
        ## 📝 使用说明
        1. 上传视频文件
        2. 点击"开始处理"
        3. 等待处理完成
        4. 查看结果
        
        **注意**: 这是简化版本，专注于测试基本功能
        """)
        
        with gr.Row():
            with gr.Column():
                # 输入
                video_input = gr.Video(
                    label="📤 上传视频文件",
                    sources=["upload"]
                )
                
                process_btn = gr.Button("🚀 开始处理", variant="primary")
                
                # 统计显示
                with gr.Row():
                    vehicle_count = gr.Number(label="🚗 车辆数量", value=0)
                    people_count = gr.Number(label="👥 人员数量", value=0)
            
            with gr.Column():
                # 输出
                output_image = gr.Image(label="📺 处理结果")
                stats_text = gr.Textbox(
                    label="📊 统计信息",
                    lines=10
                )
        
        # 绑定事件
        process_btn.click(
            fn=process_video_simple,
            inputs=[video_input],
            outputs=[output_image, stats_text, vehicle_count, people_count]
        )
    
    return demo

def main():
    """主函数"""
    print("🚗 启动YOLOv11车辆计数器Web界面（本地测试版）...")
    print("=" * 50)
    
    try:
        # 检查依赖
        print("🔍 检查依赖...")
        import cv2
        print("✅ OpenCV 可用")
        
        from ultralytics import YOLO
        print("✅ Ultralytics 可用")
        
        import gradio as gr
        print("✅ Gradio 可用")
        
        # 创建界面
        demo = create_simple_interface()
        
        # 启动服务器
        print("🌐 启动Web服务器...")
        demo.launch(
            server_name="127.0.0.1",  # 仅本地访问
            server_port=7860,
            share=False,
            debug=True,
            show_error=True
        )
        
    except ImportError as e:
        print(f"❌ 依赖缺失: {e}")
        print("💡 请安装所需依赖:")
        print("   pip install gradio ultralytics opencv-python")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()