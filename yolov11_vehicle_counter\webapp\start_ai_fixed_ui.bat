@echo off
chcp 65001 >nul
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █                YOLOv11 车辆计数器 - UI修复完整版                            █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🔧 UI修复内容:
echo    ✅ 修复文件选择按钮无响应问题
echo    ✅ 修复实时统计车辆总数显示0的问题
echo    ✅ 优化计数算法，避免ID重复计算
echo    ✅ 改善视觉效果：红色计数线，更大字体
echo    ✅ 优化检测框显示和轨迹追踪
echo.
echo 🤖 AI功能特色:
echo    ✅ 真实的YOLO深度学习模型检测
echo    ✅ 完整的目标追踪和计数
echo    ✅ 精确的车辆穿越计数（左右分别统计）
echo    ✅ 人员检测和统计（按ID去重）
echo    ✅ 高级可视化注释和调试信息
echo    ✅ 处理后视频预览和下载
echo.
echo ════════════════════════════════════════════════════════════════════════════════
echo.
echo 🔍 正在检查系统状态...
python -c "import ultralytics; print('✅ YOLO已安装')" 2>nul || (
    echo ❌ YOLO未安装，正在安装...
    pip install ultralytics
)
echo.
echo 🚀 启动UI修复版Web服务器...
echo.
echo 📖 使用说明:
echo    1. 等待服务器启动完成
echo    2. 打开浏览器访问: http://localhost:5000
echo    3. 点击"选择视频文件"按钮或拖拽上传视频
echo    4. 观察实时统计数据更新
echo    5. 查看红色计数线和大字体显示
echo    6. 下载处理后的视频
echo.
echo ⚠️  注意: 
echo    - 文件选择按钮现在可以正常点击
echo    - 实时统计会正确显示车辆数量
echo    - 计数线为红色，字体更大更清晰
echo    - ID管理已优化，避免重复计算
echo.
echo ════════════════════════════════════════════════════════════════════════════════
echo.
python app.py
echo.
echo 服务器已停止
pause