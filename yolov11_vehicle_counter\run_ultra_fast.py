#!/usr/bin/env python3
"""
超高速运行脚本 - 专门优化FPS性能
目标：达到15+ FPS的处理速度
"""

import os
import sys
from pathlib import Path
import subprocess

def detect_environment():
    """自动检测环境并返回相应的路径"""
    # 本地环境路径
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    # 服务器环境路径
    server_model = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt')
    server_video = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    if local_model.exists():
        print("✅ 检测到本地环境")
        return {
            'model': str(local_model),
            'video': str(local_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'local'
        }
    elif server_model.exists():
        print("✅ 检测到服务器环境")
        return {
            'model': str(server_model),
            'video': str(server_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'server'
        }
    else:
        print("❌ 未找到有效的环境配置")
        return None

def main():
    print("🚀 车辆计数系统 - 超高速版本")
    print("=" * 60)
    print("🎯 目标: 15+ FPS 处理速度")
    print("⚡ 优化策略: 降低精度换取速度")
    print()
    
    # 检测环境
    config = detect_environment()
    if not config:
        input("按回车键退出...")
        return
    
    print(f"🌍 环境类型: {config['env']}")
    print(f"🤖 YOLO模型: {config['model']}")
    print(f"🎥 视频文件: {config['video']}")
    print(f"🔍 ReID模型: {config['reid']}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 设置超高速环境变量
    env = os.environ.copy()
    env['ULTRA_FAST_MODE'] = '1'
    env['YOLO_CONF_THRESHOLD'] = '0.7'      # 提高置信度阈值
    env['YOLO_IOU_THRESHOLD'] = '0.8'       # 提高IOU阈值
    env['YOLO_MAX_DETECTIONS'] = '50'       # 大幅减少检测数量
    env['YOLO_IMG_SIZE'] = '416'            # 降低输入分辨率
    env['DEEPSORT_MAX_AGE'] = '5'           # 极短跟踪生命周期
    env['DEEPSORT_NN_BUDGET'] = '10'        # 最小预算
    env['SKIP_FRAMES'] = '2'                # 跳帧处理
    
    print("⚙️  超高速配置:")
    print("   • 置信度阈值: 0.7 (更严格)")
    print("   • IOU阈值: 0.8 (更严格)")
    print("   • 最大检测数: 50 (大幅减少)")
    print("   • 输入尺寸: 416x416 (降低分辨率)")
    print("   • 跟踪生命周期: 5帧 (极短)")
    print("   • 跳帧处理: 每2帧处理1帧")
    print()
    
    # 构建命令
    cmd = [sys.executable, 'main_ultra_fast.py']
    
    print("🔄 开始超高速处理...")
    print("📝 命令:", ' '.join(cmd))
    print()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, env=env, check=True)
        print()
        print("✅ 超高速处理完成！")
        print("📊 检查性能报告查看FPS结果")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()