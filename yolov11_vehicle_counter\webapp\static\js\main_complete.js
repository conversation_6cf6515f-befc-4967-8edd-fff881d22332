// YOLOv11车辆计数器 - 完整版JavaScript

class VehicleCounterApp {
    constructor() {
        this.currentTaskId = null;
        this.processingInterval = null;
        this.startTime = null;
        this.originalVideoFile = null;
        
        this.initializeElements();
        this.bindEvents();
        this.updateUI();
    }

    initializeElements() {
        // 获取DOM元素
        this.elements = {
            uploadArea: document.getElementById('uploadArea'),
            videoFile: document.getElementById('videoFile'),
            processBtn: document.getElementById('processBtn'),
            loadingIcon: document.getElementById('loadingIcon'),
            
            // 原视频元素
            originalVideoContainer: document.getElementById('originalVideoContainer'),
            originalVideoPlaceholder: document.getElementById('originalVideoPlaceholder'),
            originalVideo: document.getElementById('originalVideo'),
            
            // 统计元素
            vehicleCount: document.getElementById('vehicleCount'),
            currentFps: document.getElementById('currentFps'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            statusMessage: document.getElementById('statusMessage'),
            statusText: document.getElementById('statusText'),
            
            // 结果元素
            resultsSummary: document.getElementById('resultsSummary'),
            leftCount: document.getElementById('leftCount'),
            rightCount: document.getElementById('rightCount'),
            frameCount: document.getElementById('frameCount'),
            processTime: document.getElementById('processTime'),
            
            // 视频元素
            resultVideoContainer: document.getElementById('resultVideoContainer'),
            resultVideoPlaceholder: document.getElementById('resultVideoPlaceholder'),
            resultVideo: document.getElementById('resultVideo'),
            
            // 对比元素
            comparisonOriginal: document.getElementById('comparisonOriginal'),
            comparisonResult: document.getElementById('comparisonResult'),
            
            // 下载元素
            downloadVideo: document.getElementById('downloadVideo'),
            downloadReport: document.getElementById('downloadReport'),
            downloadData: document.getElementById('downloadData'),
            shareResults: document.getElementById('shareResults'),
            
            // 设置元素
            confidenceSlider: document.getElementById('confidenceSlider'),
            confidenceValue: document.getElementById('confidenceValue'),
            speedSelect: document.getElementById('speedSelect'),
            modelSelect: document.getElementById('modelSelect'),
            showTrajectory: document.getElementById('showTrajectory'),
            showCountLine: document.getElementById('showCountLine')
        };
    }

    bindEvents() {
        // 文件上传事件
        this.elements.videoFile.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // 拖拽上传事件
        this.elements.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.elements.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.elements.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // 处理按钮事件
        this.elements.processBtn.addEventListener('click', () => this.startProcessing());
        
        // 置信度滑块事件
        this.elements.confidenceSlider.addEventListener('input', (e) => {
            this.elements.confidenceValue.textContent = e.target.value;
        });
        
        // 下载按钮事件
        this.elements.downloadVideo.addEventListener('click', () => this.downloadVideo());
        this.elements.downloadReport.addEventListener('click', () => this.downloadReport());
        this.elements.downloadData.addEventListener('click', () => this.downloadData());
        this.elements.shareResults.addEventListener('click', () => this.shareResults());
    }

    handleDragOver(e) {
        e.preventDefault();
        this.elements.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.handleFile(file);
        }
    }

    handleFile(file) {
        // 验证文件类型
        if (!file.type.startsWith('video/')) {
            this.showMessage('请选择视频文件', 'error');
            return;
        }

        // 验证文件大小 (200MB限制)
        if (file.size > 200 * 1024 * 1024) {
            this.showMessage('文件大小不能超过200MB', 'error');
            return;
        }

        this.originalVideoFile = file;

        // 更新上传区域UI
        this.elements.uploadArea.innerHTML = `
            <div class="upload-icon">✅</div>
            <div class="upload-text">已选择: ${file.name}</div>
            <div style="font-size: 0.8em; color: #666; margin-top: 8px;">
                大小: ${this.formatFileSize(file.size)}
            </div>
        `;
        
        // 显示原视频预览
        this.showOriginalVideo(file);
        
        this.elements.processBtn.disabled = false;
        this.showMessage('文件上传成功，可以开始处理', 'success');
    }

    showOriginalVideo(file) {
        const url = URL.createObjectURL(file);
        this.elements.originalVideo.src = url;
        this.elements.originalVideo.style.display = 'block';
        this.elements.originalVideoPlaceholder.style.display = 'none';
        
        // 更新对比视图的原视频
        this.elements.comparisonOriginal.innerHTML = `
            <div class="comparison-label">原始</div>
            <video class="video-player" src="${url}" style="width: 100%; height: 100%; object-fit: contain;"></video>
        `;
    }

    async startProcessing() {
        try {
            this.elements.processBtn.disabled = true;
            this.elements.loadingIcon.style.display = 'inline-block';
            this.elements.processBtn.innerHTML = '<span class="loading"></span> 处理中...';
            
            this.startTime = Date.now();
            
            // 上传文件
            const formData = new FormData();
            formData.append('video', this.originalVideoFile);
            
            this.showMessage('正在上传文件...', 'info');
            
            const uploadResponse = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });
            
            if (!uploadResponse.ok) {
                throw new Error('文件上传失败');
            }
            
            const uploadResult = await uploadResponse.json();
            this.currentTaskId = uploadResult.task_id;
            
            // 开始处理
            const settings = {
                model: this.elements.modelSelect.value,
                confidence: parseFloat(this.elements.confidenceSlider.value),
                speed: this.elements.speedSelect.value,
                show_trajectory: this.elements.showTrajectory.checked,
                show_count_line: this.elements.showCountLine.checked
            };
            
            const processResponse = await fetch('/api/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: this.currentTaskId,
                    settings: settings
                })
            });
            
            if (!processResponse.ok) {
                throw new Error('处理启动失败');
            }
            
            this.showMessage('开始处理视频...', 'info');
            this.startStatusPolling();
            
        } catch (error) {
            this.showMessage(`错误: ${error.message}`, 'error');
            this.resetProcessButton();
        }
    }

    startStatusPolling() {
        this.processingInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/status/${this.currentTaskId}`);
                if (!response.ok) {
                    throw new Error('获取状态失败');
                }
                
                const status = await response.json();
                this.updateStatus(status);
                
                if (status.status === 'completed') {
                    this.handleProcessingComplete();
                } else if (status.status === 'error') {
                    this.handleProcessingError(status.message);
                }
                
            } catch (error) {
                console.error('状态轮询错误:', error);
            }
        }, 1000);
    }

    updateStatus(status) {
        // 更新进度条
        this.elements.progressFill.style.width = `${status.progress}%`;
        this.elements.progressText.textContent = `${status.progress.toFixed(1)}% - ${status.message}`;
        
        // 更新统计数据
        this.elements.vehicleCount.textContent = status.vehicle_count || 0;
        this.elements.currentFps.textContent = (status.current_fps || 0).toFixed(1);
        
        // 更新处理时间
        if (this.startTime) {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            this.elements.processTime.textContent = `${elapsed}s`;
        }
        
        // 显示状态消息
        this.elements.statusText.textContent = status.message;
        this.elements.statusMessage.style.display = 'block';
        
        // 如果有实时预览帧，显示在结果视频区域
        if (status.preview_frame) {
            this.updatePreviewFrame(status.preview_frame);
        }
    }

    updatePreviewFrame(frameData) {
        // 这里可以显示实时处理帧（如果后端支持）
        // 暂时跳过，等处理完成后显示完整视频
    }

    async handleProcessingComplete() {
        clearInterval(this.processingInterval);
        
        try {
            // 获取处理结果
            const response = await fetch(`/api/results/${this.currentTaskId}`);
            if (!response.ok) {
                throw new Error('获取结果失败');
            }
            
            const results = await response.json();
            this.displayResults(results);
            
            this.showMessage('处理完成！', 'success');
            this.resetProcessButton();
            
        } catch (error) {
            this.showMessage(`获取结果失败: ${error.message}`, 'error');
        }
    }

    displayResults(results) {
        // 显示结果摘要
        this.elements.leftCount.textContent = results.vehicle_left || 0;
        this.elements.rightCount.textContent = results.vehicle_right || 0;
        this.elements.frameCount.textContent = results.processed_frames || 0;
        this.elements.resultsSummary.style.display = 'block';
        
        // 显示处理后的视频
        const resultVideoUrl = `/api/download/${this.currentTaskId}`;
        this.elements.resultVideo.src = resultVideoUrl;
        this.elements.resultVideo.style.display = 'block';
        this.elements.resultVideoPlaceholder.style.display = 'none';
        
        // 更新对比视图的结果视频
        this.elements.comparisonResult.innerHTML = `
            <div class="comparison-label">处理后</div>
            <video class="video-player" src="${resultVideoUrl}" style="width: 100%; height: 100%; object-fit: contain;"></video>
        `;
        
        // 启用下载按钮
        this.elements.downloadVideo.disabled = false;
        this.elements.downloadReport.disabled = false;
        this.elements.downloadData.disabled = false;
        this.elements.shareResults.disabled = false;
        
        // 创建统计图表
        this.createChart(results);
    }

    createChart(results) {
        const chartContainer = document.getElementById('chartContainer');
        
        const vehicleTotal = (results.vehicle_left || 0) + (results.vehicle_right || 0);
        const leftPercent = vehicleTotal > 0 ? ((results.vehicle_left || 0) / vehicleTotal * 100).toFixed(1) : 0;
        const rightPercent = vehicleTotal > 0 ? ((results.vehicle_right || 0) / vehicleTotal * 100).toFixed(1) : 0;
        
        chartContainer.innerHTML = `
            <div style="width: 100%; text-align: center;">
                <h3 style="margin-bottom: 15px; color: #2c3e50; font-size: 1.1em;">统计概览</h3>
                <div style="display: flex; justify-content: space-around; align-items: center; margin-bottom: 15px;">
                    <div style="text-align: center;">
                        <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2em; font-weight: bold; margin: 0 auto 8px;">
                            ${vehicleTotal}
                        </div>
                        <div style="color: #555; font-weight: 500; font-size: 0.9em;">车辆总数</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(45deg, #28a745, #20c997); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2em; font-weight: bold; margin: 0 auto 8px;">
                            ${(results.avg_fps || 0).toFixed(1)}
                        </div>
                        <div style="color: #555; font-weight: 500; font-size: 0.9em;">平均FPS</div>
                    </div>
                </div>
                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 0.9em;">
                        <span>左侧穿越:</span>
                        <span style="font-weight: bold;">${results.vehicle_left || 0} 辆 (${leftPercent}%)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.9em;">
                        <span>右侧穿越:</span>
                        <span style="font-weight: bold;">${results.vehicle_right || 0} 辆 (${rightPercent}%)</span>
                    </div>
                </div>
                <div style="margin-top: 10px; font-size: 0.8em; color: #888;">
                    处理时长: ${results.processing_time || 'N/A'}
                </div>
            </div>
        `;
    }

    handleProcessingError(message) {
        clearInterval(this.processingInterval);
        this.showMessage(`处理失败: ${message}`, 'error');
        this.resetProcessButton();
    }

    resetProcessButton() {
        this.elements.processBtn.disabled = false;
        this.elements.loadingIcon.style.display = 'none';
        this.elements.processBtn.innerHTML = '开始处理';
    }

    async downloadVideo() {
        if (!this.currentTaskId) return;
        
        try {
            const link = document.createElement('a');
            link.href = `/api/download/${this.currentTaskId}`;
            link.download = `processed_video_${this.currentTaskId}.mp4`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showMessage('视频下载已开始', 'success');
        } catch (error) {
            this.showMessage('下载失败', 'error');
        }
    }

    async downloadReport() {
        if (!this.currentTaskId) return;
        
        try {
            const response = await fetch(`/api/results/${this.currentTaskId}`);
            const results = await response.json();
            
            const report = this.generateReport(results);
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `analysis_report_${this.currentTaskId}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            this.showMessage('报告下载已开始', 'success');
        } catch (error) {
            this.showMessage('报告生成失败', 'error');
        }
    }

    async downloadData() {
        if (!this.currentTaskId) return;
        
        try {
            const response = await fetch(`/api/results/${this.currentTaskId}`);
            const results = await response.json();
            
            const dataBlob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `raw_data_${this.currentTaskId}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            this.showMessage('数据下载已开始', 'success');
        } catch (error) {
            this.showMessage('数据下载失败', 'error');
        }
    }

    shareResults() {
        if (!this.currentTaskId) return;
        
        const shareUrl = `${window.location.origin}/results/${this.currentTaskId}`;
        
        if (navigator.share) {
            navigator.share({
                title: 'YOLOv11车辆计数结果',
                text: '查看我的车辆计数分析结果',
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showMessage('分享链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.showMessage('分享功能不可用', 'error');
            });
        }
    }

    generateReport(results) {
        const timestamp = new Date().toLocaleString('zh-CN');
        
        return `
YOLOv11车辆计数分析报告
========================

生成时间: ${timestamp}
任务ID: ${this.currentTaskId}
原始文件: ${this.originalVideoFile ? this.originalVideoFile.name : 'N/A'}

统计结果:
---------
车辆穿越总数: ${(results.vehicle_left || 0) + (results.vehicle_right || 0)} 辆
  - 左侧穿越: ${results.vehicle_left || 0} 辆
  - 右侧穿越: ${results.vehicle_right || 0} 辆

处理信息:
---------
处理帧数: ${results.processed_frames || 0} 帧
平均FPS: ${(results.avg_fps || 0).toFixed(2)} fps
处理时长: ${results.processing_time || 'N/A'}

技术参数:
---------
检测模型: ${this.elements.modelSelect.value}
置信度阈值: ${this.elements.confidenceSlider.value}
处理速度: ${this.elements.speedSelect.value}
显示轨迹: ${this.elements.showTrajectory.checked ? '是' : '否'}
显示计数线: ${this.elements.showCountLine.checked ? '是' : '否'}

说明:
-----
- 车辆计数基于穿越中央计数线的轨迹
- 所有数据均为自动检测结果
- 检测框和轨迹已标注在输出视频中

报告结束
`;
    }

    showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessages = document.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());
        
        // 创建新消息
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        
        // 插入到第一个卡片中
        const firstCard = document.querySelector('.card');
        firstCard.insertBefore(messageDiv, firstCard.firstChild.nextSibling);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateUI() {
        // 初始化UI状态
        this.elements.processBtn.disabled = true;
        this.elements.resultsSummary.style.display = 'none';
        this.elements.statusMessage.style.display = 'none';
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new VehicleCounterApp();
});