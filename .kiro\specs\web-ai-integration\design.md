# Design Document

## Overview

This design document outlines the architecture for integrating real YOLO deep learning models into the YOLOv11 vehicle counter web application. The system will provide a complete end-to-end solution for video upload, AI-powered processing, and result visualization with processed video playback.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │  Flask Backend  │    │  AI Processing  │
│                 │    │                 │    │                 │
│ - File Upload   │◄──►│ - API Routes    │◄──►│ - YOLO Model    │
│ - Progress UI   │    │ - Task Manager  │    │ - Video Proc.   │
│ - Video Player  │    │ - File Handler  │    │ - Counting Logic│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Static Files  │    │   Task Storage  │    │  Model Storage  │
│                 │    │                 │    │                 │
│ - HTML/CSS/JS   │    │ - Status Dict   │    │ - YOLO Weights  │
│ - Uploaded Vids │    │ - Results Dict  │    │ - Config Files  │
│ - Output Vids   │    │ - Progress Data │    │ - Class Names   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Interaction Flow

1. **Upload Phase**: User uploads video → Frontend validates → Backend stores file
2. **Processing Phase**: User starts processing → Backend loads AI model → Processes video frame-by-frame
3. **Monitoring Phase**: Frontend polls status → Backend returns progress → UI updates in real-time
4. **Results Phase**: Processing completes → Backend serves processed video → Frontend displays results

## Components and Interfaces

### 1. AI Model Manager

**Purpose**: Handle YOLO model loading, validation, and inference

**Key Methods**:
```python
class AIModelManager:
    def __init__(self):
        self.model = None
        self.model_loaded = False
        self.model_type = None
    
    def load_model(self) -> bool:
        """Load YOLO model with fallback strategy"""
        
    def validate_model(self) -> bool:
        """Test model with sample frame"""
        
    def get_model_info(self) -> dict:
        """Return model status and capabilities"""
```

**Model Loading Strategy**:
1. Try custom trained model (best.pt)
2. Fall back to pre-trained YOLOv11m.pt
3. Fall back to YOLOv11n.pt (fastest)
4. Report failure with installation instructions

### 2. Video Processor

**Purpose**: Core video processing with AI detection and counting

**Key Methods**:
```python
class VideoProcessor:
    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.track_history = {}
        self.counting_logic = CountingLogic()
    
    def process_video(self, input_path, output_path, task_id):
        """Main video processing pipeline"""
        
    def process_frame(self, frame, frame_number):
        """Process single frame with AI detection"""
        
    def draw_annotations(self, frame, detections, counts):
        """Draw detection boxes, IDs, and statistics"""
```

**Processing Pipeline**:
1. **Frame Extraction**: Read video frame by frame
2. **AI Inference**: Run YOLO detection and tracking
3. **Counting Logic**: Apply line-crossing algorithm from main_final_perfect.py
4. **Annotation**: Draw bounding boxes, IDs, trajectories, statistics
5. **Output**: Write annotated frame to output video

### 3. Counting Logic Engine

**Purpose**: Implement precise vehicle and person counting

**Key Features**:
- **Line-crossing detection**: Based on main_final_perfect.py algorithm
- **ID management**: Clean ID mapping for user-friendly display
- **Multi-class counting**: Separate vehicle and person statistics
- **Direction tracking**: Left-to-right vs right-to-left crossing

**Algorithm**:
```python
class CountingLogic:
    def __init__(self):
        self.line_x = None  # Set based on video width
        self.track_history = {}
        self.crossed_ids = set()
        self.counts = {'left': 0, 'right': 0, 'people': 0}
    
    def update_counts(self, detections):
        """Update counts based on new detections"""
        for detection in detections:
            if self.is_line_crossing(detection):
                self.register_crossing(detection)
```

### 4. Task Manager

**Purpose**: Handle asynchronous video processing tasks

**Key Features**:
- **Task Status Tracking**: Upload, processing, completed, error states
- **Progress Monitoring**: Frame-by-frame progress updates
- **Result Storage**: Final statistics and file paths
- **Error Handling**: Capture and report processing errors

**Data Structures**:
```python
task_status = {
    'task_id': {
        'status': 'processing',  # uploaded, processing, completed, error
        'progress': 45.2,        # percentage
        'current_frame': 1234,
        'total_frames': 2730,
        'message': 'Processing frame 1234/2730',
        'counts': {'left': 5, 'right': 8, 'people': 12}
    }
}

task_results = {
    'task_id': {
        'vehicle_left': 15,
        'vehicle_right': 23,
        'vehicle_total': 38,
        'people_total': 45,
        'processing_time': '2m 34s',
        'avg_fps': 12.5,
        'debug_info': {...}
    }
}
```

### 5. Web API Layer

**Purpose**: RESTful API for frontend-backend communication

**Endpoints**:

```python
# File Operations
POST /api/upload          # Upload video file
GET  /api/preview/{id}    # Stream processed video
GET  /api/download/{id}   # Download processed video

# Processing Control
POST /api/process         # Start video processing
GET  /api/status/{id}     # Get processing status
GET  /api/results/{id}    # Get final results

# System Information
GET  /api/model-status    # Get AI model status
GET  /api/system-info     # Get system capabilities
```

### 6. Frontend Interface

**Purpose**: Modern, responsive web interface

**Key Components**:

1. **Upload Zone**:
   - Drag-and-drop file upload
   - File format validation
   - Upload progress indicator

2. **Processing Monitor**:
   - Real-time progress bar
   - Frame counter display
   - Live counting statistics
   - Processing speed (FPS)

3. **Results Display**:
   - Embedded video player with controls
   - Statistics dashboard
   - Download button
   - Debug information panel

4. **Status Indicators**:
   - AI model status (loaded/error)
   - System health indicators
   - Error message display

## Data Models

### Detection Result
```python
@dataclass
class Detection:
    bbox: Tuple[int, int, int, int]  # x1, y1, x2, y2
    center: Tuple[int, int]          # center_x, center_y
    class_id: int
    class_name: str
    confidence: float
    track_id: int
    clean_id: int                    # User-friendly ID
```

### Processing Statistics
```python
@dataclass
class ProcessingStats:
    total_frames: int
    processed_frames: int
    processing_time: float
    avg_fps: float
    vehicle_detections: int
    person_detections: int
    line_crossings: int
```

## Error Handling

### Error Categories

1. **Model Loading Errors**:
   - YOLO not installed → Installation instructions
   - Model file not found → Download instructions
   - GPU/CUDA issues → Fallback to CPU

2. **Video Processing Errors**:
   - Unsupported format → Format conversion guidance
   - Corrupted file → Re-upload request
   - Memory issues → File size recommendations

3. **System Errors**:
   - OpenMP conflicts → Automatic environment fixes
   - Disk space issues → Cleanup recommendations
   - Network issues → Retry mechanisms

### Error Response Format
```python
{
    "error": True,
    "error_type": "model_loading",
    "message": "YOLO model failed to load",
    "details": "ultralytics package not found",
    "solution": "Run: pip install ultralytics",
    "retry_possible": True
}
```

## Testing Strategy

### Unit Tests
- Model loading and validation
- Counting logic accuracy
- API endpoint functionality
- Error handling scenarios

### Integration Tests
- End-to-end video processing
- Frontend-backend communication
- File upload and download
- Real-time status updates

### Performance Tests
- Large video file handling
- Concurrent user processing
- Memory usage monitoring
- Processing speed benchmarks

### AI Model Tests
- Detection accuracy validation
- Tracking consistency checks
- Counting precision verification
- Model inference speed tests

## Security Considerations

1. **File Upload Security**:
   - File type validation
   - File size limits
   - Virus scanning (optional)
   - Secure file storage

2. **Processing Security**:
   - Task isolation
   - Resource limits
   - Timeout mechanisms
   - Clean temporary files

3. **API Security**:
   - Rate limiting
   - Input validation
   - Error message sanitization
   - CORS configuration

## Performance Optimization

### Video Processing
- **GPU Acceleration**: Use CUDA if available
- **Batch Processing**: Process multiple frames efficiently
- **Memory Management**: Stream processing for large files
- **Caching**: Cache model weights and configurations

### Web Interface
- **Async Operations**: Non-blocking video processing
- **Progress Streaming**: Real-time status updates
- **Video Streaming**: Efficient video delivery
- **Resource Cleanup**: Automatic file cleanup

### Scalability
- **Threading**: Background processing threads
- **Queue System**: Task queue for multiple users
- **Load Balancing**: Distribute processing load
- **Caching**: Cache processed results temporarily