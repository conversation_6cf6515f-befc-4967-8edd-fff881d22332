
import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
from ultralytics import <PERSON><PERSON><PERSON>
from pathlib import Path
import argparse

# --- Configuration ---
track_history = {}
counted_ids = set()

def run_tracker(model_path, video_path, save_path, show_video=True):
    """Runs YOLOv11 object tracking, saves the output, and optionally displays it."""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    print(f"Loading model from: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)

    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"Output video will be saved to: {save_path}")

    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("Video processing completed.")
            break

        results = model.track(frame, persist=True, verbose=False)
        annotated_frame = results[0].plot()
        cv2.line(annotated_frame, line_start, line_end, (0, 255, 0), 2)

        if results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, track_id in zip(boxes, track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                track_history[track_id] = (center_x, center_y)

        cv2.putText(annotated_frame, f"Left Count: {left_count}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
        cv2.putText(annotated_frame, f"Right Count: {right_count}", (w - 300, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        if show_video:
            try:
                cv2.imshow("YOLOv11 Tracking and Counting", annotated_frame)
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    break
            except cv2.error:
                print("(GUI not available) Cannot display video. Continuing to process and save the video.")
                show_video = False # Stop trying to show video

        out.write(annotated_frame)

    cap.release()
    out.release()
    cv2.destroyAllWindows()
    print(f"Final Counts -> Left: {left_count}, Right: {right_count}")
    print(f"Output video saved successfully to {save_path}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Run YOLOv11 tracking and counting. Requires absolute paths.")
    parser.add_argument('--model', type=str, required=True, help='Absolute path to the YOLOv11 model .pt file.')
    parser.add_argument('--source', type=str, required=True, help='Absolute path to the input video file.')
    parser.add_argument('--save-path', type=str, required=True, help='Absolute path to save the output video file.')
    parser.add_argument('--no-show', action='store_true', help='Do not display the video window (recommended for servers or headless environments).')
    args = parser.parse_args()

    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)
