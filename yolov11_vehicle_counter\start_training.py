#!/usr/bin/env python3
"""
YOLOv11m训练启动脚本
针对当前数据集结构
"""

import os
import subprocess
from pathlib import Path

def check_dataset():
    """检查数据集状态"""
    print("=== 数据集检查 ===")
    
    # 检查图像
    images = list(Path("dataset").glob("*.jpg"))
    print(f"发现图像: {len(images)} 张")
    
    # 检查标签
    labels = list(Path("dataset").glob("*.txt"))
    print(f"发现标签: {len(labels)} 个")
    
    if len(images) == 0:
        print("警告: 未找到图像文件")
        return False
    
    return True

def create_dataset_structure():
    """创建数据集目录结构"""
    dirs = [
        'dataset/images/train',
        'dataset/images/val',
        'dataset/images/test',
        'dataset/labels/train',
        'dataset/labels/val',
        'dataset/labels/test'
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("已创建数据集目录结构")

def setup_dataset():
    """设置数据集"""
    print("=== 设置数据集 ===")
    
    # 获取所有文件
    images = list(Path("dataset").glob("*.jpg"))
    labels = list(Path("dataset").glob("*.txt"))
    
    if len(images) == 0:
        print("错误: 未找到图像文件")
        return False
    
    # 简单分割：70%训练，20%验证，10%测试
    total = len(images)
    train_end = int(total * 0.7)
    val_end = train_end + int(total * 0.2)
    
    # 移动文件
    for i, img in enumerate(images):
        label = img.with_suffix('.txt')
        
        if i < train_end:
            split = 'train'
        elif i < val_end:
            split = 'val'
        else:
            split = 'test'
        
        # 移动图像
        img_dest = Path(f"dataset/images/{split}") / img.name
        img.rename(img_dest)
        
        # 移动标签（如果存在）
        if label.exists():
            label_dest = Path(f"dataset/labels/{split}") / label.name
            label.rename(label_dest)
    
    print("数据集设置完成")
    return True

def train():
    """开始训练"""
    print("=== 开始YOLOv11m训练 ===")
    
    # 检查模型
    model_path = "../ultralytics/yolo11m.pt"
    if not Path(model_path).exists():
        print(f"错误: 找不到模型 {model_path}")
        return
    
    # 训练命令
    cmd = [
        "yolo", "detect", "train",
        "data=data_final.yaml",
        f"model={model_path}",
        "epochs=100",
        "imgsz=640",
        "batch=8",
        "device=0",
        "workers=4",
        "project=runs/train",
        "name=yolo11m_vehicle_detection",
        "patience=30",
        "save=True",
        "cache=False"
    ]
    
    print("执行命令:", " ".join(cmd))
    try:
        subprocess.run(cmd)
    except Exception as e:
        print(f"训练错误: {e}")
        print("请确保已安装Ultralytics YOLO: pip install ultralytics")

def main():
    print("=== YOLOv11m车辆检测训练系统 ===")
    
    if check_dataset():
        create_dataset_structure()
        if setup_dataset():
            train()
        else:
            print("数据集设置失败")
    else:
        print("请确保数据集已准备好")

if __name__ == "__main__":
    main()
