import cv2
import numpy as np

def draw_fancy_bbox_label(image, bbox, track_id, class_name, confidence, dashboard_height=0):
    """
    Draws a bounding box with a fancy label (ID, Class, Confidence) on the image.
    The label includes a semi-transparent background and graded text sizes.

    Args:
        image (np.ndarray): The image (frame) to draw on.
        bbox (list): Bounding box coordinates [x1, y1, x2, y2].
        track_id (int): The ID of the tracked object.
        class_name (str): The class name of the detected object.
        confidence (float): The confidence score of the detection.
        dashboard_height (int): Height of the dashboard area, to offset drawing on the video part.
    """
    x1, y1, x2, y2 = [int(b) for b in bbox]

    # Adjust y-coordinates for drawing on the video part of the canvas
    y1_adjusted = y1 + dashboard_height
    y2_adjusted = y2 + dashboard_height

    # Bounding box color (can be customized later)
    bbox_color = (0, 255, 0) # Green
    cv2.rectangle(image, (x1, y1_adjusted), (x2, y2_adjusted), bbox_color, 2)

    # --- Label Text Configuration ---
    # ID and Class
    id_class_text = f"ID: {track_id} {class_name}"
    id_class_font_scale = 0.7
    id_class_thickness = 2
    id_class_color = (255, 255, 255) # White

    # Confidence
    conf_text = f"{confidence:.2f}"
    conf_font_scale = 0.5
    conf_thickness = 1
    conf_color = (200, 200, 200) # Light gray

    # --- Calculate text sizes and background box dimensions ---
    (id_class_w, id_class_h), id_class_baseline = cv2.getTextSize(id_class_text, cv2.FONT_HERSHEY_SIMPLEX, id_class_font_scale, id_class_thickness)
    (conf_w, conf_h), conf_baseline = cv2.getTextSize(conf_text, cv2.FONT_HERSHEY_SIMPLEX, conf_font_scale, conf_thickness)

    # Label background padding
    padding = 5
    label_height = id_class_h + conf_h + id_class_baseline + conf_baseline + padding * 2
    label_width = max(id_class_w, conf_w) + padding * 2

    # Label position (top-left of the bounding box)
    label_x = x1
    label_y = y1_adjusted - label_height - padding # Position above the bbox
    if label_y < dashboard_height: # Ensure label is not drawn on dashboard area
        label_y = y1_adjusted + padding # Draw below if no space above

    # --- Draw semi-transparent background ---
    alpha = 0.6 # Transparency factor
    overlay = image.copy()
    cv2.rectangle(overlay, (label_x, label_y), (label_x + label_width, label_y + label_height), (0, 0, 0), -1) # Black background
    image = cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0)

    # --- Draw text on the image ---
    # ID and Class text
    cv2.putText(image, id_class_text, (label_x + padding, label_y + id_class_h + padding), 
                cv2.FONT_HERSHEY_SIMPLEX, id_class_font_scale, id_class_color, id_class_thickness, cv2.LINE_AA)

    # Confidence text
    cv2.putText(image, conf_text, (label_x + padding, label_y + id_class_h + conf_h + padding + id_class_baseline), 
                cv2.FONT_HERSHEY_SIMPLEX, conf_font_scale, conf_color, conf_thickness, cv2.LINE_AA)

    return image
