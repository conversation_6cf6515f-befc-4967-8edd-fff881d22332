#!/usr/bin/env python3
"""
启动YOLOv11车辆计数器Web界面
"""

import sys
from pathlib import Path

# 添加src目录到路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

if __name__ == "__main__":
    print("🚗 YOLOv11车辆计数器 - Web界面启动器")
    print("=" * 50)
    
    try:
        from webui_modern import main
        main()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保安装了所需依赖:")
        print("   pip install gradio")
        print("   pip install ultralytics")
        print("   pip install opencv-python")
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        print("💡 请检查模型文件路径和视频文件是否存在")