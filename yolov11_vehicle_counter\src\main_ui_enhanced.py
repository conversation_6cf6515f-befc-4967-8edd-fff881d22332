#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - UI增强版本
- 优化UI显示，大字体清晰计数
- 只检测车辆，过滤人员
- 减少重叠框，优化可视化
- 增强计数线显示
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import argparse
from datetime import datetime

# --- Configuration ---
track_history = {}
counted_ids = set()

# 车辆类别ID (COCO数据集)
VEHICLE_CLASSES = {
    2: 'car',
    3: 'motorcycle', 
    5: 'bus',
    7: 'truck'
}

def create_enhanced_ui_overlay(frame, left_count, right_count, line_x):
    """创建增强的UI覆盖层"""
    h, w = frame.shape[:2]
    
    # 创建半透明覆盖层用于显示计数信息
    overlay = frame.copy()
    
    # 左侧计数区域 - 深蓝色背景
    cv2.rectangle(overlay, (10, 10), (300, 120), (139, 69, 19), -1)  # 深蓝色
    # 右侧计数区域 - 深红色背景  
    cv2.rectangle(overlay, (w-310, 10), (w-10, 120), (19, 69, 139), -1)  # 深红色
    
    # 混合原图和覆盖层
    alpha = 0.7
    frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
    
    # 绘制大字体计数信息
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 1.2
    thickness = 2
    
    # 左侧计数 - 白色大字
    cv2.putText(frame, "LEFT", (20, 40), font, 0.8, (255, 255, 255), thickness)
    cv2.putText(frame, f"{left_count:03d}", (20, 80), font, font_scale, (255, 255, 255), thickness+1)
    
    # 右侧计数 - 白色大字
    cv2.putText(frame, "RIGHT", (w-150, 40), font, 0.8, (255, 255, 255), thickness)
    cv2.putText(frame, f"{right_count:03d}", (w-150, 80), font, font_scale, (255, 255, 255), thickness+1)
    
    # 绘制增强的计数线
    # 主线 - 亮绿色粗线
    cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 4)
    
    # 添加计数线标签
    cv2.rectangle(frame, (line_x-60, h//2-20), (line_x+60, h//2+20), (0, 255, 0), -1)
    cv2.putText(frame, "COUNT LINE", (line_x-55, h//2+5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    # 添加方向箭头
    # 左箭头
    arrow_left = np.array([[line_x-100, h//2], [line_x-80, h//2-10], [line_x-80, h//2+10]], np.int32)
    cv2.fillPoly(frame, [arrow_left], (255, 255, 0))
    
    # 右箭头  
    arrow_right = np.array([[line_x+100, h//2], [line_x+80, h//2-10], [line_x+80, h//2+10]], np.int32)
    cv2.fillPoly(frame, [arrow_right], (255, 255, 0))
    
    return frame

def filter_vehicle_detections(results):
    """过滤只保留车辆检测结果"""
    if results[0].boxes is None:
        return results
    
    # 获取所有检测结果
    boxes = results[0].boxes
    
    # 过滤车辆类别
    vehicle_mask = []
    for cls_id in boxes.cls:
        vehicle_mask.append(int(cls_id) in VEHICLE_CLASSES)
    
    if not any(vehicle_mask):
        # 如果没有车辆，返回空结果
        results[0].boxes = None
        return results
    
    # 只保留车辆检测
    vehicle_mask = np.array(vehicle_mask)
    
    # 过滤boxes
    if hasattr(boxes, 'xyxy'):
        boxes.xyxy = boxes.xyxy[vehicle_mask]
    if hasattr(boxes, 'xywh'):  
        boxes.xywh = boxes.xywh[vehicle_mask]
    if hasattr(boxes, 'conf'):
        boxes.conf = boxes.conf[vehicle_mask]
    if hasattr(boxes, 'cls'):
        boxes.cls = boxes.cls[vehicle_mask]
    if hasattr(boxes, 'id') and boxes.id is not None:
        boxes.id = boxes.id[vehicle_mask]
    
    return results

def draw_enhanced_detections(frame, results, line_x):
    """绘制增强的检测框"""
    if results[0].boxes is None or results[0].boxes.id is None:
        return frame
    
    boxes = results[0].boxes.xyxy.cpu().numpy()
    confidences = results[0].boxes.conf.cpu().numpy()
    class_ids = results[0].boxes.cls.cpu().numpy()
    track_ids = results[0].boxes.id.int().cpu().tolist()
    
    for box, conf, cls_id, track_id in zip(boxes, confidences, class_ids, track_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # 根据车辆类型选择颜色
        vehicle_type = VEHICLE_CLASSES.get(int(cls_id), 'vehicle')
        if vehicle_type == 'car':
            color = (0, 255, 255)  # 黄色
        elif vehicle_type == 'truck':
            color = (255, 0, 255)  # 紫色
        elif vehicle_type == 'bus':
            color = (255, 165, 0)  # 橙色
        else:
            color = (0, 255, 0)    # 绿色
        
        # 绘制检测框 - 更粗的线条
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 3)
        
        # 计算中心点
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # 绘制中心点
        cv2.circle(frame, (center_x, center_y), 5, color, -1)
        
        # 绘制跟踪轨迹
        if track_id in track_history:
            prev_x, prev_y = track_history[track_id]
            cv2.line(frame, (prev_x, prev_y), (center_x, center_y), color, 2)
        
        # 绘制标签背景
        label = f"ID:{track_id} {vehicle_type} {conf:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        
        # 标签背景
        cv2.rectangle(frame, (x1, y1-30), (x1 + label_size[0] + 10, y1), color, -1)
        
        # 标签文字
        cv2.putText(frame, label, (x1+5, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 如果车辆靠近计数线，高亮显示
        if abs(center_x - line_x) < 50:
            cv2.rectangle(frame, (x1-5, y1-5), (x2+5, y2+5), (0, 0, 255), 3)
    
    return frame

def run_tracker(model_path, video_path, save_path, show_video=True):
    """运行增强UI的车辆跟踪器"""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    print(f"🚗 加载车辆检测模型: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ 无法打开视频文件: {video_path}")
        return

    # 获取视频属性
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
    
    line_x = w // 2
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"💾 输出视频: {save_path}")

    frame_count = 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("✅ 视频处理完成")
            break

        frame_count += 1
        
        # YOLO跟踪 - 优化参数，提高车辆检测精度
        results = model.track(
            frame, 
            persist=True, 
            verbose=False,
            conf=0.3,      # 降低置信度阈值，检测更多车辆
            iou=0.5,       # 降低IoU阈值，减少重叠
            classes=[2, 3, 5, 7]  # 只检测车辆类别
        )
        
        # 过滤车辆检测结果
        results = filter_vehicle_detections(results)
        
        # 绘制增强的检测框
        annotated_frame = draw_enhanced_detections(frame.copy(), results, line_x)
        
        # 处理跟踪结果
        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, track_id in zip(boxes, track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                
                # 计数逻辑
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        # 从左到右穿越
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                            print(f"🚙 车辆 {track_id} 从左到右穿越，右侧计数: {right_count}")
                        # 从右到左穿越
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                            print(f"🚗 车辆 {track_id} 从右到左穿越，左侧计数: {left_count}")
                
                # 更新历史位置
                track_history[track_id] = (center_x, center_y)

        # 创建增强UI覆盖层
        annotated_frame = create_enhanced_ui_overlay(annotated_frame, left_count, right_count, line_x)
        
        # 添加进度条
        progress = frame_count / total_frames if total_frames > 0 else 0
        progress_width = int(w * 0.8)
        progress_x = int(w * 0.1)
        progress_y = h - 30
        
        # 进度条背景
        cv2.rectangle(annotated_frame, (progress_x, progress_y), (progress_x + progress_width, progress_y + 20), (50, 50, 50), -1)
        # 进度条填充
        cv2.rectangle(annotated_frame, (progress_x, progress_y), (progress_x + int(progress_width * progress), progress_y + 20), (0, 255, 0), -1)
        # 进度文字
        progress_text = f"{frame_count}/{total_frames} ({progress*100:.1f}%)"
        cv2.putText(annotated_frame, progress_text, (progress_x + 10, progress_y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 显示视频
        if show_video:
            try:
                # 调整窗口大小以便更好显示
                cv2.namedWindow("YOLOv11 Enhanced Vehicle Counter", cv2.WINDOW_NORMAL)
                cv2.resizeWindow("YOLOv11 Enhanced Vehicle Counter", 1200, 800)
                cv2.imshow("YOLOv11 Enhanced Vehicle Counter", annotated_frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord("q"):
                    print("🛑 用户请求退出")
                    break
                elif key == ord("s"):
                    # 保存当前帧截图
                    screenshot_path = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                    cv2.imwrite(screenshot_path, annotated_frame)
                    print(f"📸 截图已保存: {screenshot_path}")
                    
            except cv2.error as e:
                print(f"⚠️ GUI显示错误: {e}")
                show_video = False

        # 保存帧
        out.write(annotated_frame)
        
        # 进度显示
        if frame_count % 50 == 0:  # 更频繁的进度更新
            print(f"📊 进度: {frame_count}/{total_frames} ({progress*100:.1f}%) | 左侧: {left_count} | 右侧: {right_count}")

    # 清理资源
    cap.release()
    out.release()
    
    # 安全关闭窗口
    try:
        cv2.destroyAllWindows()
    except:
        pass
    
    # 最终结果
    print("=" * 70)
    print("🎉 车辆计数完成！")
    print(f"📊 最终统计:")
    print(f"   ⬅️  左侧通过: {left_count:3d} 辆")
    print(f"   ➡️  右侧通过: {right_count:3d} 辆")
    print(f"   🚗 总计车辆: {left_count + right_count:3d} 辆")
    print(f"📹 处理帧数: {frame_count:,} 帧")
    print(f"💾 输出文件: {save_path}")
    print("=" * 70)
    print("💡 提示: 播放时按 'q' 退出，按 's' 截图")

if __name__ == '__main__':
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_ui_enhanced_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - UI增强版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - UI增强版本")
    print("✨ 特性:")
    print("   📱 大字体清晰计数显示")
    print("   🚙 只检测车辆，过滤行人")
    print("   🎨 增强可视化效果")
    print("   📊 实时进度显示")
    print("=" * 50)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 50)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)