#!/usr/bin/env python3
"""
基于webui.py的工作版本，适配YOLOv11
这是真正工作良好的代码逻辑
"""

# 设置环境变量，限制线程数为1
import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"

import copy
import sys
import argparse
import shutil
import time
from pathlib import Path
import numpy as np
import cv2
import torch
import torch.backends.cudnn as cudnn

# 使用YOLOv11
from ultralytics import YOLO
from tracker.utils.parser import get_config
from tracker.deep_sort import DeepSort

# 全局计数变量
count = 0
count2 = 0
data = []

def count_obj(box, w, h, id):
    """计数函数 - 与原版完全相同"""
    global count, count2, data
    center_coor = (int(box[0] + (box[2] - box[0]) / 2), int(box[1] + (box[3] - box[1]) / 2))
    if int(box[1] + (box[3] - box[1]) / 2) > h - 350 and id not in data:
        if int(box[0] + (box[2] - box[0]) / 2) > int(w / 2):
            count2 += 1
        else:
            count += 1
        data.append(id)

def detect(opt):
    """主检测函数 - 基于webui.py的逻辑，适配YOLOv11"""
    global count, count2, data
    
    # 重置计数
    count = 0
    count2 = 0
    data = []
    
    out, source, yolo_model, deep_sort_model, show_vid, save_vid, save_txt, imgsz, evaluate, half, \
        project, exist_ok, update, save_crop = \
        opt.output, opt.source, opt.yolo_model, opt.deep_sort_model, opt.show_vid, opt.save_vid, \
        opt.save_txt, opt.imgsz, opt.evaluate, opt.half, opt.project, opt.exist_ok, opt.update, opt.save_crop

    # 设备选择
    device = torch.device('cuda:0' if torch.cuda.is_available() and opt.device != 'cpu' else 'cpu')
    print(f"使用设备: {device}")

    # 创建输出目录
    if not evaluate:
        if os.path.exists(out):
            shutil.rmtree(out)
        os.makedirs(out)

    # 目录设置
    if type(yolo_model) is str:
        exp_name = Path(yolo_model).stem
    else:
        exp_name = "ensemble"
    exp_name = exp_name + "_" + Path(deep_sort_model).stem
    
    save_dir = Path(project) / exp_name
    save_dir.mkdir(parents=True, exist_ok=True)
    if save_txt:
        (save_dir / 'tracks').mkdir(exist_ok=True)

    # 加载YOLOv11模型
    print(f"加载YOLO模型: {yolo_model}")
    model = YOLO(yolo_model)
    names = model.names
    print(f"模型类别: {names}")

    # 初始化DeepSORT
    print(f"加载DeepSORT模型: {deep_sort_model}")
    cfg = get_config()
    cfg.merge_from_file(opt.config_deepsort)
    
    deepsort = DeepSort(
        deep_sort_model,
        device,
        max_dist=cfg.DEEPSORT.MAX_DIST,
        max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
        max_age=cfg.DEEPSORT.MAX_AGE,
        n_init=cfg.DEEPSORT.N_INIT,
        nn_budget=cfg.DEEPSORT.NN_BUDGET,
    )

    # 打开视频
    print(f"打开视频: {source}")
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {source}")
        return

    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {w}x{h} @ {fps}fps, 总帧数: {total_frames}")

    # 设置视频写入器
    vid_writer = None
    if save_vid:
        save_path = str(save_dir / f"{Path(source).stem}_output.mp4")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        vid_writer = cv2.VideoWriter(save_path, fourcc, fps, (w, h))
        print(f"输出视频: {save_path}")

    # 处理视频帧
    frame_idx = 0
    dt = [0.0, 0.0, 0.0, 0.0]
    
    print("开始处理视频...")
    
    while True:
        ret, im0 = cap.read()
        if not ret:
            break
            
        frame_idx += 1
        t1 = time.time()

        # YOLO检测
        results = model.predict(
            im0,
            conf=opt.conf_thres,
            iou=opt.iou_thres,
            max_det=opt.max_det,
            verbose=False
        )
        
        t2 = time.time()
        dt[1] += t2 - t1

        # 处理检测结果
        if results and len(results) > 0:
            result = results[0]
            if result.boxes is not None and len(result.boxes) > 0:
                # 获取检测框
                det = result.boxes.data.cpu().numpy()
                
                if len(det) > 0:
                    # 转换为DeepSORT需要的格式
                    xywhs = det[:, :4].copy()
                    # 转换xyxy到xywh
                    xywhs[:, 2] = xywhs[:, 2] - xywhs[:, 0]  # width
                    xywhs[:, 3] = xywhs[:, 3] - xywhs[:, 1]  # height
                    xywhs[:, 0] = xywhs[:, 0] + xywhs[:, 2] / 2  # center x
                    xywhs[:, 1] = xywhs[:, 1] + xywhs[:, 3] / 2  # center y
                    
                    confs = det[:, 4]
                    clss = det[:, 5]
                    
                    # DeepSORT跟踪
                    t3 = time.time()
                    try:
                        outputs = deepsort.update(xywhs, confs, clss, im0)
                        t4 = time.time()
                        dt[3] += t4 - t3
                        
                        # 处理跟踪结果
                        if len(outputs) > 0:
                            for j, (output, conf) in enumerate(zip(outputs, confs)):
                                bboxes = output[0:4]
                                track_id = int(output[4])
                                cls_id = int(output[5])
                                
                                # 计数 - 使用原版逻辑
                                count_obj(bboxes, w, h, track_id)
                                
                                # 保存跟踪结果
                                if save_txt:
                                    txt_path = save_dir / 'tracks' / f"{Path(source).stem}.txt"
                                    bbox_left = output[0]
                                    bbox_top = output[1]
                                    bbox_w = output[2] - output[0]
                                    bbox_h = output[3] - output[1]
                                    with open(txt_path, 'a') as f:
                                        f.write(('%g ' * 10 + '\n') % (frame_idx, track_id, bbox_left,
                                                                       bbox_top, bbox_w, bbox_h, -1, -1, -1, 0))
                                
                                # 绘制边界框
                                if save_vid or show_vid:
                                    x1, y1, x2, y2 = map(int, bboxes)
                                    label = f'{track_id} {names[cls_id]} {conf:.2f}'
                                    cv2.rectangle(im0, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                    cv2.putText(im0, label, (x1, y1-10), 
                                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    
                    except Exception as e:
                        print(f"帧 {frame_idx} DeepSORT错误: {e}")
                        deepsort.increment_ages()
                else:
                    deepsort.increment_ages()
            else:
                deepsort.increment_ages()
        else:
            deepsort.increment_ages()

        # 绘制计数线和计数 - 与原版完全相同
        # 左侧线（绿色）
        color = (0, 255, 0)
        start_point = (0, h - 350)
        end_point = (int(w/2) - 50, h - 350)
        cv2.line(im0, start_point, end_point, color, thickness=2)
        
        # 右侧线（红色）
        color = (255, 0, 0)
        start_point = (int(w/2) + 50, h - 350)
        end_point = (w, h - 350)
        cv2.line(im0, start_point, end_point, color, thickness=2)
        
        # 显示计数
        cv2.putText(im0, str(count), (150, 150), cv2.FONT_HERSHEY_COMPLEX, 3, (0, 255, 0), 3)
        cv2.putText(im0, str(count2), (w - 150, 150), cv2.FONT_HERSHEY_COMPLEX, 3, (255, 0, 0), 3)
        
        # 显示视频
        if show_vid:
            cv2.imshow('YOLOv11 Vehicle Counter', im0)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # 保存视频
        if save_vid and vid_writer:
            vid_writer.write(im0)
        
        # 显示进度
        if frame_idx % 100 == 0:
            progress = (frame_idx / total_frames) * 100 if total_frames > 0 else 0
            print(f"进度: {progress:.1f}% ({frame_idx}/{total_frames}), 左侧: {count}, 右侧: {count2}")

    # 清理资源
    cap.release()
    if vid_writer:
        vid_writer.release()
    cv2.destroyAllWindows()
    
    # 打印最终结果
    print("=" * 50)
    print("处理完成！")
    print(f"最终计数结果:")
    print(f"  左侧: {count}")
    print(f"  右侧: {count2}")
    print(f"  总计: {count + count2}")
    print(f"  处理帧数: {frame_idx}")
    if save_vid:
        print(f"  输出视频: {save_path}")
    print("=" * 50)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--yolo_model', type=str, 
                       default='D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt', 
                       help='YOLOv11 model path')
    parser.add_argument('--deep_sort_model', type=str, 
                       default='tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
                       help='DeepSORT model path')
    parser.add_argument('--source', type=str, 
                       default='D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4',
                       help='video source')
    parser.add_argument('--output', type=str, default='inference/output', help='output folder')
    parser.add_argument('--imgsz', type=int, default=640, help='inference size')
    parser.add_argument('--conf-thres', type=float, default=0.6, help='confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.7, help='IOU threshold for NMS')
    parser.add_argument('--max-det', type=int, default=100, help='maximum detections per image')
    parser.add_argument('--device', default='0', help='cuda device, i.e. 0 or cpu')
    parser.add_argument('--show-vid', action='store_true', help='display video results')
    parser.add_argument('--save-vid', action='store_true', default=True, help='save video results')
    parser.add_argument('--save-txt', action='store_true', help='save results to *.txt')
    parser.add_argument('--classes', nargs='+', type=int, help='filter by class')
    parser.add_argument('--agnostic-nms', action='store_true', help='class-agnostic NMS')
    parser.add_argument('--augment', action='store_true', help='augmented inference')
    parser.add_argument('--update', action='store_true', help='update all models')
    parser.add_argument('--evaluate', action='store_true', help='evaluate mode')
    parser.add_argument("--config_deepsort", type=str, default="tracker/configs/deep_sort.yaml")
    parser.add_argument("--half", action="store_true", help="use FP16 half-precision inference")
    parser.add_argument('--visualize', action='store_true', help='visualize features')
    parser.add_argument('--save-crop', action='store_true', help='save cropped prediction boxes')
    parser.add_argument('--dnn', action='store_true', help='use OpenCV DNN for ONNX inference')
    parser.add_argument('--project', default='runs/track', help='save results to project/name')
    parser.add_argument('--name', default='exp', help='save results to project/name')
    parser.add_argument('--exist-ok', action='store_true', help='existing project/name ok, do not increment')
    
    opt = parser.parse_args()
    
    print("🎯 YOLOv11 车辆计数器 - 基于工作版本")
    print("=" * 50)
    print(f"YOLO模型: {opt.yolo_model}")
    print(f"DeepSORT模型: {opt.deep_sort_model}")
    print(f"视频源: {opt.source}")
    print(f"置信度阈值: {opt.conf_thres}")
    print(f"IOU阈值: {opt.iou_thres}")
    print(f"最大检测数: {opt.max_det}")
    print("=" * 50)
    
    with torch.no_grad():
        detect(opt)