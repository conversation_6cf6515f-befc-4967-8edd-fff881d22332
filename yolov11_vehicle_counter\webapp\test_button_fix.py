#!/usr/bin/env python3
"""
按钮修复测试脚本
"""

import os
import webbrowser
import time
import subprocess
import sys

def test_button_fixes():
    print("🔧 按钮修复测试")
    print("=" * 50)
    
    # 检查文件是否存在
    files = [
        "index_gorgeous.html",
        "static/js/main_gorgeous.js",
        "app.py"
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            return False
    
    print("\n🚀 启动测试:")
    print("1. 启动服务器...")
    
    # 启动服务器
    try:
        # 在后台启动服务器
        server_process = subprocess.Popen([sys.executable, "app.py"], 
                                        stdout=subprocess.PIPE, 
                                        stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(3)
        
        print("2. 打开浏览器...")
        webbrowser.open("http://localhost:5000")
        
        print("\n📋 测试清单:")
        print("□ 点击'选择视频文件'按钮 - 应该打开文件选择对话框")
        print("□ 选择一个视频文件")
        print("□ 观察'开始智能分析'按钮是否变为可点击状态")
        print("□ 如果按钮仍然禁用，点击'强制启用分析按钮'")
        print("□ 点击'开始智能分析'测试处理功能")
        
        print("\n🔍 调试信息:")
        print("- 打开浏览器开发者工具(F12)")
        print("- 查看Console标签页的日志输出")
        print("- 应该看到'点击选择文件'和'文件选择变化'等日志")
        
        input("\n按回车键停止服务器...")
        server_process.terminate()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_button_fixes()