#!/usr/bin/env python3
"""
启动完整版YOLOv11车辆计数器Web应用
5块精美布局，真实视频处理，带检测框输出
"""

import sys
import shutil
import webbrowser
import time
from pathlib import Path

def setup_complete_webapp():
    """设置完整版Web应用"""
    print("🎯 设置完整版Web应用...")
    
    webapp_dir = Path("webapp")
    
    # 备份原始文件
    if (webapp_dir / "index.html").exists():
        shutil.copy(webapp_dir / "index.html", webapp_dir / "index_backup.html")
        print("✅ 已备份原始index.html")
    
    if (webapp_dir / "static/js/main.js").exists():
        shutil.copy(webapp_dir / "static/js/main.js", webapp_dir / "static/js/main_backup.js")
        print("✅ 已备份原始main.js")
    
    if (webapp_dir / "app.py").exists():
        shutil.copy(webapp_dir / "app.py", webapp_dir / "app_backup.py")
        print("✅ 已备份原始app.py")
    
    # 使用完整版文件
    if (webapp_dir / "index_complete.html").exists():
        shutil.copy(webapp_dir / "index_complete.html", webapp_dir / "index.html")
        print("✅ 已应用完整版index.html")
    
    if (webapp_dir / "static/js/main_complete.js").exists():
        shutil.copy(webapp_dir / "static/js/main_complete.js", webapp_dir / "static/js/main.js")
        print("✅ 已应用完整版main.js")
    
    if (webapp_dir / "app_complete.py").exists():
        shutil.copy(webapp_dir / "app_complete.py", webapp_dir / "app.py")
        print("✅ 已应用完整版app.py")
    
    return True

def main():
    """主函数"""
    print("🚗 YOLOv11车辆计数器 - 完整版Web应用")
    print("=" * 60)
    print("🎯 完整版特色:")
    print("   ✨ 5块精美布局设计")
    print("   🎬 原视频预览 + 处理结果对比")
    print("   🎯 真实YOLO检测和跟踪")
    print("   📹 输出带检测框和计数线的视频")
    print("   📊 实时统计和进度显示")
    print("   📈 详细数据分析和下载")
    print("   ⚖️  原视频vs处理结果对比视图")
    print("=" * 60)
    
    # 检查webapp目录
    webapp_dir = Path("webapp")
    if not webapp_dir.exists():
        print(f"❌ webapp目录不存在: {webapp_dir}")
        return
    
    # 设置完整版
    if not setup_complete_webapp():
        print("❌ 完整版设置失败")
        return
    
    print("\\n🚀 启动完整版Web服务器...")
    print("🌐 访问地址: http://localhost:5000")
    print("\\n📱 界面布局:")
    print("   📤 左上角: 视频上传与设置")
    print("   🎬 中上角: 原始视频预览")
    print("   📊 右上角: 实时统计与进度")
    print("   🎯 左下角: 处理结果视频")
    print("   ⚖️  中下角: 原视频vs结果对比")
    print("   📈 右下角: 数据分析与下载")
    
    print("\\n🎯 处理特色:")
    print("   🔍 真实YOLO目标检测")
    print("   🚗 车辆识别和分类")
    print("   📏 智能目标跟踪")
    print("   📊 穿越计数统计")
    print("   🎨 检测框和轨迹可视化")
    print("   📹 高质量视频输出")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5000')
            print("\\n🌐 浏览器已打开")
        except:
            print("\\n⚠️  无法自动打开浏览器，请手动访问 http://localhost:5000")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 切换到webapp目录并启动
        import os
        os.chdir("webapp")
        
        # 导入并启动完整版app
        sys.path.insert(0, ".")
        from app import app
        
        print("\\n" + "="*60)
        print("🎯 完整版Web服务器已启动")
        print("按 Ctrl+C 停止服务器")
        print("="*60)
        
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        print("\\n🛑 服务器已停止")
    except Exception as e:
        print(f"\\n❌ 启动失败: {e}")
        print("💡 请检查:")
        print("   - 依赖包是否正确安装 (pip install ultralytics opencv-python flask)")
        print("   - 端口5000是否被占用")
        print("   - 完整版文件是否存在")

if __name__ == "__main__":
    main()