#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 稳定化版本
修复OpenCV GUI错误，增强错误处理和环境兼容性
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import sys
import logging
from datetime import datetime
from pathlib import Path
import argparse
from ultralytics import YOLO

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from utils.environment_manager import EnvironmentManager
from utils.model_manager import ModelManager
from config.settings import Settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vehicle_counter.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Global Variables ---
track_history = {}
counted_ids = set()

class StabilizedVehicleCounter:
    """稳定化的车辆计数器"""
    
    def __init__(self):
        self.env_manager = EnvironmentManager()
        self.config = self.env_manager.detect_environment()
        self.model_manager = ModelManager(self.config.base_path)
        self.track_history = {}
        self.counted_ids = set()
        self.consecutive_errors = 0
        
    def run_tracker(self, model_path=None, video_path=None, save_path=None, show_video=True):
        """运行车辆跟踪和计数"""
        try:
            # 使用环境管理器配置路径
            if not model_path:
                model_path = self._get_best_model_path()
            if not video_path:
                video_path = self._get_default_video_path()
            if not save_path:
                save_path = self._generate_output_path()
                
            logger.info(f"开始处理 - 模型: {model_path}")
            logger.info(f"输入视频: {video_path}")
            logger.info(f"输出路径: {save_path}")
            logger.info(f"GUI支持: {self.config.has_gui}")
            
            return self._process_video(model_path, video_path, save_path, show_video)
            
        except Exception as e:
            logger.error(f"运行跟踪器时发生错误: {e}")
            return False
    
    def _get_best_model_path(self):
        """获取最佳可用模型路径"""
        if self.config.model_paths:
            if "main" in self.config.model_paths:
                return str(self.config.model_paths["main"])
            elif "backup" in self.config.model_paths:
                return str(self.config.model_paths["backup"])
            else:
                return str(list(self.config.model_paths.values())[0])
        else:
            return "yolov11m.pt"  # 使用预训练模型
    
    def _get_default_video_path(self):
        """获取默认视频路径"""
        if self.config.video_paths and "default" in self.config.video_paths:
            return str(self.config.video_paths["default"])
        else:
            # 返回一个默认路径
            return str(self.config.base_path / "video" / "test_video.mp4")
    
    def _generate_output_path(self):
        """生成带时间戳的输出路径"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = self.config.output_paths.get("video", self.config.base_path / "video")
        output_path = output_dir / f"output_stabilized_{timestamp}.mp4"
        return str(output_path)
    
    def _process_video(self, model_path, video_path, save_path, show_video):
        """处理视频的核心逻辑"""
        left_count, right_count = 0, 0
        self.track_history.clear()
        self.counted_ids.clear()
        
        try:
            # 使用模型管理器加载模型
            logger.info(f"使用模型管理器加载模型: {model_path}")
            model, actual_model_path = self.model_manager.load_yolo_model(model_path)
            
            # 优化模型性能
            model = self.model_manager.optimize_model_for_inference(model)
            
            # 记录模型信息
            model_info = self.model_manager.get_model_info(model)
            logger.info(f"实际使用模型: {actual_model_path}")
            logger.info(f"模型信息: {model_info}")
            
            # 打开视频
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return False
            
            # 获取视频属性
            w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            logger.info(f"视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
            
            # 计算计数线位置
            line_x = int(w * Settings.COUNTING_LINE_CONFIG['position_ratio'])
            line_start = (line_x, 0)
            line_end = (line_x, h)
            
            # 设置视频写入器
            fourcc = cv2.VideoWriter_fourcc(*Settings.VIDEO_CONFIG['fourcc'])
            out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
            
            # 调整显示窗口设置（如果支持GUI）
            window_created = False
            if show_video and self.config.has_gui:
                try:
                    cv2.namedWindow("YOLOv11 Tracking and Counting", cv2.WINDOW_NORMAL)
                    window_created = True
                except Exception as e:
                    logger.warning(f"无法创建显示窗口: {e}")
                    show_video = False
            
            frame_count = 0
            
            # 主处理循环
            while cap.isOpened():
                success, frame = cap.read()
                if not success:
                    logger.info("视频处理完成")
                    break
                
                frame_count += 1
                
                try:
                    # 使用优化的YOLO推理参数
                    results = model.track(
                        frame,
                        **Settings.YOLO_INFERENCE_PARAMS
                    )
                    
                    # 获取带注释的帧
                    annotated_frame = results[0].plot()
                    
                    # 绘制计数线
                    cv2.line(
                        annotated_frame, 
                        line_start, 
                        line_end, 
                        Settings.COUNTING_LINE_CONFIG['color'], 
                        Settings.COUNTING_LINE_CONFIG['thickness']
                    )
                    
                    # 处理跟踪结果并计数
                    left_count, right_count = self._process_tracking_results(
                        results[0], line_x, left_count, right_count
                    )
                    
                    # 绘制计数信息
                    self._draw_count_info(annotated_frame, left_count, right_count, w)
                    
                    # 显示视频（如果支持）
                    if show_video and window_created:
                        try:
                            cv2.imshow("YOLOv11 Tracking and Counting", annotated_frame)
                            if cv2.waitKey(1) & 0xFF == ord("q"):
                                logger.info("用户请求退出")
                                break
                        except Exception as e:
                            logger.warning(f"显示帧时出错: {e}")
                            show_video = False
                            window_created = False
                    
                    # 保存帧
                    out.write(annotated_frame)
                    
                    # 显示进度
                    if frame_count % Settings.VIDEO_CONFIG['progress_interval'] == 0:
                        progress = (frame_count / total_frames) * 100 if total_frames > 0 else 0
                        logger.info(f"处理进度: {frame_count}/{total_frames} 帧 ({progress:.1f}%), 左侧: {left_count}, 右侧: {right_count}")
                    
                    # 重置连续错误计数
                    self.consecutive_errors = 0
                    
                except Exception as e:
                    self.consecutive_errors += 1
                    logger.warning(f"处理第{frame_count}帧时出错: {e}")
                    
                    if self.consecutive_errors >= Settings.ERROR_HANDLING_CONFIG['max_consecutive_errors']:
                        logger.error("连续错误过多，停止处理")
                        break
                    
                    # 继续处理下一帧
                    continue
            
            # 清理资源
            cap.release()
            out.release()
            
            # 安全地销毁窗口
            self._safe_destroy_windows()
            
            # 输出最终结果
            self._print_final_results(left_count, right_count, save_path, frame_count)
            
            return True
            
        except Exception as e:
            logger.error(f"视频处理过程中发生严重错误: {e}")
            return False
    
    def _process_tracking_results(self, result, line_x, left_count, right_count):
        """处理跟踪结果并更新计数"""
        if result.boxes.id is not None:
            boxes = result.boxes.xywh.cpu()
            track_ids = result.boxes.id.int().cpu().tolist()
            
            for box, track_id in zip(boxes, track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                
                # 计数逻辑
                if track_id in self.track_history:
                    prev_x, _ = self.track_history[track_id]
                    if track_id not in self.counted_ids:
                        # 从左到右穿越
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            self.counted_ids.add(track_id)
                            logger.info(f"车辆 {track_id} 从左到右穿越，右侧计数: {right_count}")
                        # 从右到左穿越
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            self.counted_ids.add(track_id)
                            logger.info(f"车辆 {track_id} 从右到左穿越，左侧计数: {left_count}")
                
                # 更新历史位置
                self.track_history[track_id] = (center_x, center_y)
        
        return left_count, right_count
    
    def _draw_count_info(self, frame, left_count, right_count, width):
        """绘制计数信息"""
        # 左侧计数
        cv2.putText(
            frame, 
            f"Left Count: {left_count}", 
            Settings.DISPLAY_CONFIG['left_count_pos'], 
            cv2.FONT_HERSHEY_SIMPLEX, 
            Settings.DISPLAY_CONFIG['font_scale'], 
            Settings.DISPLAY_CONFIG['left_count_color'], 
            Settings.DISPLAY_CONFIG['font_thickness']
        )
        
        # 右侧计数
        cv2.putText(
            frame, 
            f"Right Count: {right_count}", 
            (width - 300, 50), 
            cv2.FONT_HERSHEY_SIMPLEX, 
            Settings.DISPLAY_CONFIG['font_scale'], 
            Settings.DISPLAY_CONFIG['right_count_color'], 
            Settings.DISPLAY_CONFIG['font_thickness']
        )
    
    def _safe_destroy_windows(self):
        """安全地销毁OpenCV窗口"""
        try:
            self.env_manager.safe_cv2_destroy_windows()
        except Exception as e:
            logger.warning(f"销毁窗口时出现警告（可忽略）: {e}")
    
    def _print_final_results(self, left_count, right_count, save_path, frame_count):
        """打印最终结果"""
        print("=" * 60)
        print("🎉 视频处理完成！")
        print(f"📊 最终计数结果:")
        print(f"   左侧通过: {left_count}")
        print(f"   右侧通过: {right_count}")
        print(f"   总计车辆: {left_count + right_count}")
        print(f"📹 处理帧数: {frame_count}")
        print(f"💾 输出视频: {save_path}")
        print(f"🖥️  GUI支持: {'是' if self.config.has_gui else '否'}")
        print("=" * 60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - 稳定化版本")
    parser.add_argument('--model', type=str, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, help='输入视频路径')
    parser.add_argument('--save-path', type=str, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - 稳定化版本")
    print("✅ 修复OpenCV GUI错误")
    print("🔧 增强环境兼容性")
    print("=" * 60)
    
    # 创建计数器实例
    counter = StabilizedVehicleCounter()
    
    # 运行跟踪
    success = counter.run_tracker(
        model_path=args.model,
        video_path=args.source,
        save_path=args.save_path,
        show_video=not args.no_show
    )
    
    if success:
        logger.info("程序成功完成")
        sys.exit(0)
    else:
        logger.error("程序执行失败")
        sys.exit(1)

if __name__ == '__main__':
    main()