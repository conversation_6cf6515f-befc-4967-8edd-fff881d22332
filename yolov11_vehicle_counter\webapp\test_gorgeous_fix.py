#!/usr/bin/env python3
"""
测试华丽版界面修复
验证JavaScript文件引用和按钮功能
"""

import os
import sys
import time
import webbrowser
import threading
from pathlib import Path

def check_file_references():
    """检查文件引用是否正确"""
    print("🔍 检查文件引用...")
    
    webapp_dir = Path(".")
    
    # 检查关键文件是否存在
    files_to_check = [
        "index.html",
        "templates/index.html", 
        "static/js/main.js",
        "app.py"
    ]
    
    for file_path in files_to_check:
        full_path = webapp_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
    
    # 检查HTML中的JavaScript引用
    html_file = webapp_dir / "templates/index.html"
    if html_file.exists():
        content = html_file.read_text(encoding='utf-8')
        if 'static/js/main.js' in content:
            print("✅ HTML正确引用 main.js")
        elif 'static/js/main_gorgeous.js' in content:
            print("⚠️  HTML引用 main_gorgeous.js (需要修复)")
        else:
            print("❌ HTML没有找到JavaScript引用")
    
    return True

def test_javascript_functionality():
    """测试JavaScript功能"""
    print("🧪 测试JavaScript功能...")
    
    js_file = Path("static/js/main.js")
    if not js_file.exists():
        print("❌ main.js 文件不存在")
        return False
    
    content = js_file.read_text(encoding='utf-8')
    
    # 检查关键函数
    required_functions = [
        'handleFileSelect',
        'handleFile',
        'startProcessing',
        'GorgeousVehicleCounter'
    ]
    
    for func in required_functions:
        if func in content:
            print(f"✅ 找到函数: {func}")
        else:
            print(f"❌ 缺少函数: {func}")
    
    # 检查事件绑定
    if "addEventListener('change'" in content:
        print("✅ 文件选择事件绑定正常")
    else:
        print("❌ 文件选择事件绑定缺失")
    
    return True

def start_test_server():
    """启动测试服务器"""
    print("🚀 启动测试服务器...")
    
    try:
        # 导入Flask应用
        from app import app
        
        # 在新线程中打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 浏览器已打开")
            except:
                print("⚠️  无法自动打开浏览器")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("✨ 华丽版测试服务器启动")
        print("📱 请在浏览器中测试:")
        print("   1. 点击'选择视频文件'按钮")
        print("   2. 选择一个视频文件")
        print("   3. 检查文件是否正确显示")
        print("   4. 点击'开始智能分析'按钮")
        print("\n按 Ctrl+C 停止测试")
        
        app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🌟 华丽版界面修复测试")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("app.py").exists():
        print("❌ 请在webapp目录中运行此脚本")
        return
    
    # 执行检查
    print("\n📋 第一步: 检查文件引用")
    check_file_references()
    
    print("\n📋 第二步: 检查JavaScript功能")
    test_javascript_functionality()
    
    print("\n📋 第三步: 启动测试服务器")
    start_test_server()

if __name__ == "__main__":
    main()
