# 清晰部署指南 - 本地 vs 服务器操作

## 🖥️ 第一部分：本地操作 (在你的Windows电脑上)

### 1. 上传文件到服务器

#### 方法A：使用WinSCP (推荐)
```
本地操作：
1. 下载并安装WinSCP: https://winscp.net/eng/download.php
2. 打开WinSCP，连接服务器：
   - 主机名: ************
   - 用户名: admin
   - 密码: [你的服务器密码]
   - 端口: 22
3. 连接成功后，上传以下文件：
   从本地: D:\lkr_yolo\yolov11_vehicle_counter\webapp\
   到服务器: /home/<USER>/ (先传到用户目录)
   
   需要上传的文件：
   - app.py
   - index.html  
   - requirements.txt
   - static文件夹 (整个文件夹)
```

#### 方法B：使用命令行
```cmd
本地命令行操作 (在 D:\lkr_yolo 目录下执行)：

scp yolov11_vehicle_counter\webapp\app.py admin@************:~/
scp yolov11_vehicle_counter\webapp\index.html admin@************:~/
scp yolov11_vehicle_counter\webapp\requirements.txt admin@************:~/
scp -r yolov11_vehicle_counter\webapp\static admin@************:~/

注意：每个命令都需要输入服务器密码
```

---

## 🖥️ 第二部分：服务器操作 (SSH登录服务器后执行)

### 1. 登录服务器
```cmd
本地命令行执行：
ssh admin@************
```
输入密码后，你就进入了服务器的命令行界面。

### 2. 在服务器上执行以下命令

#### 步骤1：创建项目目录并移动文件
```bash
# 服务器命令 - 创建项目目录
sudo mkdir -p /var/www/vehicle-counter
sudo chown admin:admin /var/www/vehicle-counter

# 服务器命令 - 移动上传的文件到项目目录
mv ~/app.py /var/www/vehicle-counter/
mv ~/index.html /var/www/vehicle-counter/
mv ~/requirements.txt /var/www/vehicle-counter/
mv ~/static /var/www/vehicle-counter/

# 服务器命令 - 验证文件是否移动成功
ls -la /var/www/vehicle-counter/
```

#### 步骤2：安装系统依赖
```bash
# 服务器命令 - 更新系统
sudo apt update

# 服务器命令 - 安装Python环境
sudo apt install -y python3-pip python3-venv python3-dev build-essential

# 服务器命令 - 安装OpenCV依赖
sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg

# 服务器命令 - 安装Web服务器和进程管理
sudo apt install -y supervisor apache2

# 服务器命令 - 安装SSL证书工具
sudo apt install -y certbot python3-certbot-apache
```

#### 步骤3：设置Python虚拟环境
```bash
# 服务器命令 - 进入项目目录
cd /var/www/vehicle-counter

# 服务器命令 - 创建虚拟环境
python3 -m venv venv

# 服务器命令 - 激活虚拟环境
source venv/bin/activate

# 服务器命令 - 升级pip
pip install --upgrade pip

# 服务器命令 - 安装项目依赖
pip install -r requirements.txt

# 服务器命令 - 安装生产环境依赖
pip install gunicorn
```

#### 步骤4：创建应用配置文件
```bash
# 服务器命令 - 创建Gunicorn配置文件
cat > /var/www/vehicle-counter/gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF

# 服务器命令 - 创建启动脚本
cat > /var/www/vehicle-counter/start.sh << 'EOF'
#!/bin/bash
cd /var/www/vehicle-counter
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF

# 服务器命令 - 设置执行权限
chmod +x /var/www/vehicle-counter/start.sh
```

#### 步骤5：配置Apache虚拟主机
```bash
# 服务器命令 - 启用Apache模块
sudo a2enmod rewrite proxy proxy_http headers ssl

# 服务器命令 - 创建虚拟主机配置
sudo tee /etc/apache2/sites-available/vehicle-counter.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    ServerAlias www.vehicle.smart-traffic.top
    
    DocumentRoot /var/www/vehicle-counter
    
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    LimitRequestBody 104857600
    
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${APACHE_LOG_DIR}/vehicle-counter_error.log
    CustomLog ${APACHE_LOG_DIR}/vehicle-counter_access.log combined
</VirtualHost>
EOF

# 服务器命令 - 启用站点
sudo a2ensite vehicle-counter.conf

# 服务器命令 - 重新加载Apache配置
sudo systemctl reload apache2
```

#### 步骤6：配置Supervisor进程管理
```bash
# 服务器命令 - 创建Supervisor配置
sudo tee /etc/supervisor/conf.d/vehicle-counter.conf > /dev/null << 'EOF'
[program:vehicle-counter]
command=/var/www/vehicle-counter/start.sh
directory=/var/www/vehicle-counter
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/var/www/vehicle-counter/venv/bin"
EOF

# 服务器命令 - 重新加载Supervisor配置
sudo supervisorctl reread
sudo supervisorctl update

# 服务器命令 - 启动应用
sudo supervisorctl start vehicle-counter
```

#### 步骤7：测试部署
```bash
# 服务器命令 - 检查应用状态
sudo supervisorctl status vehicle-counter

# 服务器命令 - 测试本地API
curl http://localhost:5000/api/health

# 服务器命令 - 查看应用日志
sudo tail -f /var/log/vehicle-counter.log
```

---

## 🌐 第三部分：域名配置 (在域名管理面板操作)

### 配置DNS解析
```
在你的域名管理面板添加A记录：
记录类型: A
主机记录: vehicle
记录值: ************
TTL: 600
```

---

## 🔒 第四部分：SSL证书配置 (服务器操作)

```bash
# 服务器命令 - 获取SSL证书
sudo certbot --apache -d vehicle.smart-traffic.top

# 服务器命令 - 测试自动续期
sudo certbot renew --dry-run
```

---

## 🎯 操作总结

### 本地操作 (Windows电脑)：
1. 使用WinSCP或scp命令上传文件
2. 使用ssh命令登录服务器

### 服务器操作 (SSH登录后)：
1. 创建目录和移动文件
2. 安装系统依赖
3. 设置Python环境
4. 配置Apache和Supervisor
5. 启动服务

### 域名管理面板操作：
1. 添加DNS A记录

### 最终访问地址：
- HTTP: http://vehicle.smart-traffic.top
- HTTPS: https://vehicle.smart-traffic.top (配置SSL后)

现在操作就很清楚了！你可以按照这个指南一步一步执行。
