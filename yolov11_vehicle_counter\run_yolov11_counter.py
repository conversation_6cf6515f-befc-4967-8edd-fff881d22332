#!/usr/bin/env python3
"""
YOLOv11车辆计数器运行脚本
基于备份文件夹webui.py改写的YOLOv11版本
"""

import os
import sys
from pathlib import Path
import subprocess

def main():
    print("🚗 YOLOv11车辆计数器 - 撞线计数版本")
    print("=" * 60)
    print("📝 基于备份文件夹webui.py改写")
    print("✅ 包含完整的撞线计数功能")
    print("🎯 使用YOLOv11 + DeepSORT")
    print()
    
    # 检测环境和文件
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    reid_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/src/tracker/deep/osnet模型/osnet_x1_0_imagenet.pth')
    
    print("🔍 检查文件...")
    if not local_model.exists():
        print(f"❌ YOLO模型文件不存在: {local_model}")
        input("按回车键退出...")
        return
        
    if not local_video.exists():
        print(f"❌ 视频文件不存在: {local_video}")
        input("按回车键退出...")
        return
        
    if not reid_model.exists():
        print(f"❌ ReID模型文件不存在: {reid_model}")
        input("按回车键退出...")
        return
    
    print("✅ 所有文件检查通过")
    print(f"🤖 YOLO模型: {local_model}")
    print(f"🎥 视频文件: {local_video}")
    print(f"🔍 ReID模型: {reid_model}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 构建命令参数
    cmd = [
        sys.executable, 'main_yolov11_counter.py',
        '--yolo_model', str(local_model),
        '--deep_sort_model', str(reid_model),
        '--source', str(local_video),
        '--save-vid',                    # 保存输出视频
        '--conf-thres', '0.6',          # 置信度阈值
        '--iou-thres', '0.7',           # IOU阈值
        '--max-det', '100',             # 最大检测数量
        '--imgsz', '640',               # 输入图像尺寸
        '--device', '0',                # 使用GPU
        '--half',                       # 使用FP16半精度
        '--project', '../runs/track',   # 输出目录
        '--name', 'yolov11_counter',    # 运行名称
        '--exist-ok'                    # 允许覆盖现有结果
    ]
    
    print("⚙️  运行配置:")
    print("   • 置信度阈值: 0.6 (过滤低置信度检测)")
    print("   • IOU阈值: 0.7 (NMS去重)")
    print("   • 最大检测数: 100 (提升处理速度)")
    print("   • 输入尺寸: 640x640 (标准分辨率)")
    print("   • GPU加速: 启用")
    print("   • FP16半精度: 启用 (提升速度)")
    print("   • 撞线计数: 启用 (Y坐标 > height-350)")
    print()
    
    print("🔄 开始处理...")
    print("📝 执行命令:")
    print("   " + " ".join(cmd))
    print()
    print("💡 提示:")
    print("   • 左侧绿线: 统计从右向左的车辆")
    print("   • 右侧红线: 统计从左向右的车辆")
    print("   • 撞线位置: 距离底部350像素")
    print("   • 按 'q' 键可提前退出")
    print()
    print("=" * 60)
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True)
        print()
        print("✅ 处理完成！")
        print("📁 检查 runs/track/yolov11_counter 目录查看结果")
        
        # 显示输出路径
        output_dir = Path("../runs/track/yolov11_counter")
        if output_dir.exists():
            print(f"📊 输出目录: {output_dir.absolute()}")
            
            # 查找输出视频
            for video_file in output_dir.glob("*.mp4"):
                print(f"🎥 输出视频: {video_file}")
                
            # 查找跟踪文件
            tracks_dir = output_dir / 'tracks'
            if tracks_dir.exists():
                track_files = list(tracks_dir.glob("*.txt"))
                if track_files:
                    print(f"📝 跟踪文件: {len(track_files)} 个文件")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
        print()
        print("💡 可能的解决方案:")
        print("   1. 检查CUDA是否正常工作")
        print("   2. 尝试使用CPU: 将 --device 0 改为 --device cpu")
        print("   3. 移除半精度: 删除 --half 参数")
        print("   4. 检查DeepSORT配置文件是否存在")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()