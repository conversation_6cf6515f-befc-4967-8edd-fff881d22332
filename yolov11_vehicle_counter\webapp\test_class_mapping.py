#!/usr/bin/env python3
"""
测试YOLO类别映射是否正确
"""

import cv2
import numpy as np
from ultralytics import YOLO

# COCO类别映射 - 与app.py保持一致
CLASS_NAMES = {
    0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane',
    5: 'bus', 6: 'train', 7: 'truck', 8: 'boat'
}

def test_class_mapping():
    """测试类别映射"""
    print("🧪 测试YOLO类别映射...")
    
    try:
        # 加载YOLO模型
        model = YOLO('../yolo11n.pt')
        print("✅ YOLO模型加载成功")
        
        # 创建测试图像
        test_image = np.zeros((640, 640, 3), dtype=np.uint8)
        
        # 添加一些简单的形状来模拟检测
        cv2.rectangle(test_image, (100, 100), (200, 200), (255, 255, 255), -1)
        cv2.circle(test_image, (400, 400), 50, (128, 128, 128), -1)
        
        # 进行检测
        results = model.predict(source=test_image, verbose=False)
        
        print(f"📊 检测结果: {len(results)} 个结果")
        
        if results and results[0].boxes is not None:
            boxes = results[0].boxes
            print(f"🎯 检测到 {len(boxes)} 个目标")
            
            for i, box in enumerate(boxes):
                cls_id = int(box.cls.cpu().numpy())
                class_name = CLASS_NAMES.get(cls_id, f'unknown_{cls_id}')
                confidence = float(box.conf.cpu().numpy())
                
                print(f"   目标 {i+1}: 类别ID={cls_id}, 类别名={class_name}, 置信度={confidence:.3f}")
        else:
            print("❌ 没有检测到任何目标")
            
        # 测试类别映射字典
        print("\n📋 类别映射表:")
        for cls_id, class_name in CLASS_NAMES.items():
            print(f"   {cls_id}: {class_name}")
            
        print("\n✅ 类别映射测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_class_mapping()
