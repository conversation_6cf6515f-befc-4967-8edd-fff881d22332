#!/usr/bin/env python3
"""
启动稳定修复版Web应用
直接在webapp目录下运行
"""

import os
import shutil
from pathlib import Path

def main():
    print("🛡️  启动稳定修复版Web应用")
    print("=" * 50)
    
    # 复制稳定修复版到app.py
    current_dir = Path(__file__).parent
    stable_fix_file = current_dir / "app_stable_fix.py"
    app_file = current_dir / "app.py"
    
    if stable_fix_file.exists():
        shutil.copy2(stable_fix_file, app_file)
        print("✅ 已复制稳定修复版到app.py")
    else:
        print("❌ 稳定修复版文件不存在")
        return
    
    print("🚀 启动Web服务器...")
    print("🌐 访问地址: http://localhost:5000")
    print("💡 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 启动Flask应用
    try:
        os.system("python app.py")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()