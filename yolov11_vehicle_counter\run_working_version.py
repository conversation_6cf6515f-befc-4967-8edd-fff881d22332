#!/usr/bin/env python3
"""
使用工作版本的运行脚本
基于备份文件夹中效果良好的代码
"""

import os
import sys
from pathlib import Path
import subprocess

def main():
    print("🎯 车辆计数系统 - 工作版本")
    print("=" * 50)
    print("📝 基于备份文件夹中效果良好的代码")
    print("✅ 简单直接，无复杂管理器")
    print()
    
    # 检测环境
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    if not local_model.exists():
        print("❌ 未找到模型文件")
        input("按回车键退出...")
        return
        
    if not local_video.exists():
        print("❌ 未找到视频文件")
        input("按回车键退出...")
        return
    
    print("✅ 检测到本地环境")
    print(f"🤖 YOLO模型: {local_model}")
    print(f"🎥 视频文件: {local_video}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 构建命令参数 - 使用最佳性能配置
    cmd = [
        sys.executable, 'main_working.py',
        '--yolo_model', str(local_model),
        '--deep_sort_model', 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
        '--source', str(local_video),
        '--save-vid',                    # 保存视频
        '--conf-thres', '0.6',          # 提高置信度阈值
        '--iou-thres', '0.7',           # IOU阈值
        '--max-det', '100',             # 减少最大检测数量
        '--imgsz', '640',               # 输入图像尺寸
        '--device', '0',                # 使用GPU
        '--half',                       # 使用FP16半精度
        '--project', '../runs/track',   # 输出目录
        '--name', 'working_run',        # 运行名称
        '--exist-ok'                    # 允许覆盖现有结果
    ]
    
    print("⚙️  配置参数:")
    print("   • 置信度阈值: 0.6")
    print("   • IOU阈值: 0.7")
    print("   • 最大检测数: 100")
    print("   • 输入尺寸: 640x640")
    print("   • GPU加速: 启用")
    print("   • FP16半精度: 启用")
    print()
    
    print("🔄 开始处理...")
    print("📝 命令:", ' '.join(cmd))
    print()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True)
        print()
        print("✅ 处理完成！")
        print("📁 检查 runs/track/working_run 目录查看结果")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
        print()
        print("💡 可能的解决方案:")
        print("   1. 尝试使用CPU: 将 --device 0 改为 --device cpu")
        print("   2. 移除半精度: 删除 --half 参数")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()