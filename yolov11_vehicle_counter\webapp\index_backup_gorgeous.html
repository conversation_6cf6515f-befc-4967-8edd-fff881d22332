<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLOv11 车辆计数器 - 完整版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1em;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .grid-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 15px;
            height: calc(100vh - 150px);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        /* 左上角 - 上传区域 */
        .upload-card {
            grid-row: span 2;
        }

        .upload-area {
            border: 3px dashed #bdc3c7;
            border-radius: 12px;
            padding: 30px 15px;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            margin-bottom: 20px;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: linear-gradient(45deg, #e8f5e8, #f0f8ff);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
        }

        .upload-text {
            font-size: 1em;
            color: #555;
            margin-bottom: 10px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .settings-panel {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .setting-item {
            margin-bottom: 12px;
        }

        .setting-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
            font-size: 0.9em;
        }

        .setting-item select,
        .setting-item input {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .setting-item select:focus,
        .setting-item input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .process-btn {
            width: 100%;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 12px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }

        .process-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(40, 167, 69, 0.4);
        }

        .process-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 中上角 - 原视频预览 */
        .original-video-card {
            display: flex;
            flex-direction: column;
        }

        .video-container {
            flex: 1;
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .video-placeholder {
            color: #666;
            text-align: center;
            font-size: 1em;
        }

        .video-player {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 右上角 - 实时统计 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8em;
            opacity: 0.9;
        }

        .progress-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 15px;
            background: #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #28a745, #20c997);
            border-radius: 8px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            animation: progress-shine 2s infinite;
        }

        @keyframes progress-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            font-weight: 500;
            color: #555;
            font-size: 0.9em;
        }

        .status-message {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 12px;
            border-radius: 0 8px 8px 0;
            margin-top: 10px;
            font-size: 0.9em;
        }

        /* 左下角 - 处理结果视频 */
        .result-video-card {
            display: flex;
            flex-direction: column;
        }

        /* 中下角 - 对比视图 */
        .comparison-card {
            display: flex;
            flex-direction: column;
        }

        .comparison-container {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            min-height: 200px;
        }

        .comparison-video {
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .comparison-label {
            position: absolute;
            top: 5px;
            left: 5px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        /* 右下角 - 分析和下载 */
        .analysis-section {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .chart-container {
            flex: 1;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 120px;
        }

        .download-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .download-btn {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 10px;
            font-size: 0.9em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(108, 117, 125, 0.4);
        }

        .download-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-summary {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 500;
            color: #555;
        }

        .summary-value {
            font-weight: 600;
            color: #2c3e50;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .grid-container {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto auto;
            }
            
            .upload-card {
                grid-row: span 1;
            }
        }

        @media (max-width: 900px) {
            .grid-container {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(5, auto);
            }
            
            .upload-card {
                grid-row: span 1;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .download-section {
                grid-template-columns: 1fr;
            }
            
            .comparison-container {
                grid-template-columns: 1fr;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 成功/错误消息 */
        .message {
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
            font-size: 0.9em;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🚗 YOLOv11 车辆计数器</h1>
            <p>智能视频分析 · 实时追踪 · 精准计数</p>
        </div>
    </div>

    <div class="container">
        <div class="grid-container">
            <!-- 左上角 - 上传和设置 -->
            <div class="card upload-card">
                <div class="card-title">
                    <div class="card-icon">📤</div>
                    视频上传与设置
                </div>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📹</div>
                    <div class="upload-text">拖拽视频文件到此处或点击上传</div>
                    <button class="upload-btn" onclick="document.getElementById('videoFile').click()">
                        选择视频文件
                    </button>
                    <input type="file" id="videoFile" class="file-input" accept="video/*">
                </div>

                <div class="settings-panel">
                    <div class="setting-item">
                        <label for="modelSelect">检测模型:</label>
                        <select id="modelSelect">
                            <option value="yolov11n">YOLOv11n (快速)</option>
                            <option value="yolov11s">YOLOv11s (平衡)</option>
                            <option value="yolov11m" selected>YOLOv11m (推荐)</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label for="confidenceSlider">置信度阈值:</label>
                        <input type="range" id="confidenceSlider" min="0.1" max="0.9" step="0.1" value="0.5">
                        <small>当前值: <span id="confidenceValue">0.5</span></small>
                    </div>

                    <div class="setting-item">
                        <label for="speedSelect">处理速度:</label>
                        <select id="speedSelect">
                            <option value="fast">快速 (跳帧处理)</option>
                            <option value="normal" selected>标准 (全帧处理)</option>
                            <option value="accurate">精确 (高质量)</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="showTrajectory" checked>
                            显示运动轨迹
                        </label>
                    </div>

                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="showCountLine" checked>
                            显示计数线
                        </label>
                    </div>

                    <button class="process-btn" id="processBtn" disabled>
                        <span class="loading" id="loadingIcon" style="display: none;"></span>
                        开始处理
                    </button>
                </div>
            </div>

            <!-- 中上角 - 原视频预览 -->
            <div class="card original-video-card">
                <div class="card-title">
                    <div class="card-icon">🎬</div>
                    原始视频预览
                </div>

                <div class="video-container" id="originalVideoContainer">
                    <div class="video-placeholder" id="originalVideoPlaceholder">
                        <div>📺</div>
                        <div>上传视频后将在此显示</div>
                    </div>
                    <video class="video-player" id="originalVideo" controls style="display: none;"></video>
                </div>
            </div>

            <!-- 右上角 - 实时统计 -->
            <div class="card">
                <div class="card-title">
                    <div class="card-icon">📊</div>
                    实时统计与进度
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="vehicleCount">0</div>
                        <div class="stat-label">车辆总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="currentFps">0</div>
                        <div class="stat-label">处理FPS</div>
                    </div>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="progressText">等待开始处理...</div>
                </div>

                <div class="status-message" id="statusMessage" style="display: none;">
                    <div id="statusText">准备就绪</div>
                </div>

                <div class="results-summary" id="resultsSummary" style="display: none;">
                    <div class="summary-item">
                        <span class="summary-label">左侧穿越:</span>
                        <span class="summary-value" id="leftCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">右侧穿越:</span>
                        <span class="summary-value" id="rightCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">处理帧数:</span>
                        <span class="summary-value" id="frameCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">处理时间:</span>
                        <span class="summary-value" id="processTime">0s</span>
                    </div>
                </div>
            </div>

            <!-- 左下角 - 处理结果视频 -->
            <div class="card result-video-card">
                <div class="card-title">
                    <div class="card-icon">🎯</div>
                    处理结果视频
                </div>

                <div class="video-container" id="resultVideoContainer">
                    <div class="video-placeholder" id="resultVideoPlaceholder">
                        <div>🎯</div>
                        <div>处理完成后将显示带检测框的结果视频</div>
                    </div>
                    <video class="video-player" id="resultVideo" controls style="display: none;"></video>
                </div>
            </div>

            <!-- 中下角 - 对比视图 -->
            <div class="card comparison-card">
                <div class="card-title">
                    <div class="card-icon">⚖️</div>
                    对比视图
                </div>

                <div class="comparison-container">
                    <div class="comparison-video" id="comparisonOriginal">
                        <div class="comparison-label">原始</div>
                        <div class="video-placeholder">
                            <div>📺</div>
                            <div>原始视频</div>
                        </div>
                    </div>
                    <div class="comparison-video" id="comparisonResult">
                        <div class="comparison-label">处理后</div>
                        <div class="video-placeholder">
                            <div>🎯</div>
                            <div>检测结果</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右下角 - 分析和下载 -->
            <div class="card">
                <div class="card-title">
                    <div class="card-icon">📈</div>
                    数据分析与下载
                </div>

                <div class="analysis-section">
                    <div class="chart-container" id="chartContainer">
                        <div style="text-align: center; color: #666;">
                            <div style="font-size: 2.5em; margin-bottom: 8px;">📊</div>
                            <div>处理完成后将显示统计图表</div>
                        </div>
                    </div>

                    <div class="download-section">
                        <button class="download-btn" id="downloadVideo" disabled>
                            <span>📹</span>
                            下载处理视频
                        </button>
                        <button class="download-btn" id="downloadReport" disabled>
                            <span>📄</span>
                            下载分析报告
                        </button>
                        <button class="download-btn" id="downloadData" disabled>
                            <span>📊</span>
                            下载原始数据
                        </button>
                        <button class="download-btn" id="shareResults" disabled>
                            <span>🔗</span>
                            分享结果
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/main_complete.js"></script>
</body>
</html>