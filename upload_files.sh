#!/bin/bash

# YOLOv11车辆计数系统文件上传脚本
# 适用于Linux/Mac系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SERVER_IP="************"  # 你的阿里云服务器公网IP
SERVER_USER="admin"
PROJECT_DIR="/var/www/vehicle-counter"
LOCAL_DIR="yolov11_vehicle_counter/webapp"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示欢迎信息
show_welcome() {
    clear
    echo "=================================================="
    echo "🚗 YOLOv11车辆计数系统 - 文件上传"
    echo "=================================================="
    echo "服务器IP: $SERVER_IP"
    echo "用户名: $SERVER_USER"
    echo "远程目录: $PROJECT_DIR"
    echo "本地目录: $LOCAL_DIR"
    echo "=================================================="
    echo
}

# 检查本地文件
check_local_files() {
    log_info "检查本地文件..."
    
    if [ ! -d "$LOCAL_DIR" ]; then
        log_error "本地目录 $LOCAL_DIR 不存在"
        log_error "请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查关键文件
    local required_files=("app.py" "requirements.txt" "index.html")
    for file in "${required_files[@]}"; do
        if [ ! -f "$LOCAL_DIR/$file" ]; then
            log_error "文件 $file 不存在"
            exit 1
        fi
    done
    
    # 检查static目录
    if [ ! -d "$LOCAL_DIR/static" ]; then
        log_warning "static目录不存在"
    fi
    
    log_success "本地文件检查通过"
}

# 测试服务器连接
test_server_connection() {
    log_info "测试服务器连接..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $SERVER_USER@$SERVER_IP exit 2>/dev/null; then
        log_success "服务器连接测试通过 (使用密钥认证)"
    else
        log_warning "无法使用密钥连接，将使用密码认证"
        log_info "请准备输入服务器密码"
    fi
}

# 创建远程目录
create_remote_directory() {
    log_info "创建远程项目目录..."
    
    ssh $SERVER_USER@$SERVER_IP "sudo mkdir -p $PROJECT_DIR && sudo chown \$USER:\$USER $PROJECT_DIR"
    
    if [ $? -eq 0 ]; then
        log_success "远程目录创建成功"
    else
        log_error "远程目录创建失败"
        exit 1
    fi
}

# 上传文件
upload_files() {
    log_info "开始上传文件..."
    
    # 上传主要文件
    local main_files=("app.py" "requirements.txt" "index.html")
    for file in "${main_files[@]}"; do
        if [ -f "$LOCAL_DIR/$file" ]; then
            log_info "上传 $file..."
            scp "$LOCAL_DIR/$file" $SERVER_USER@$SERVER_IP:$PROJECT_DIR/
            if [ $? -eq 0 ]; then
                log_success "$file 上传成功"
            else
                log_error "$file 上传失败"
                exit 1
            fi
        fi
    done
    
    # 上传static目录
    if [ -d "$LOCAL_DIR/static" ]; then
        log_info "上传 static 目录..."
        scp -r "$LOCAL_DIR/static" $SERVER_USER@$SERVER_IP:$PROJECT_DIR/
        if [ $? -eq 0 ]; then
            log_success "static目录上传成功"
        else
            log_error "static目录上传失败"
            exit 1
        fi
    fi
    
    # 上传可选文件
    local optional_files=("start_webapp.py" "README.md" "start_webapp.bat")
    for file in "${optional_files[@]}"; do
        if [ -f "$LOCAL_DIR/$file" ]; then
            log_info "上传 $file..."
            scp "$LOCAL_DIR/$file" $SERVER_USER@$SERVER_IP:$PROJECT_DIR/
            if [ $? -eq 0 ]; then
                log_success "$file 上传成功"
            else
                log_warning "$file 上传失败 (可选文件)"
            fi
        fi
    done
}

# 验证上传结果
verify_upload() {
    log_info "验证上传结果..."
    
    # 检查远程文件
    local remote_files=$(ssh $SERVER_USER@$SERVER_IP "ls -la $PROJECT_DIR/")
    
    if echo "$remote_files" | grep -q "app.py"; then
        log_success "app.py 验证通过"
    else
        log_error "app.py 验证失败"
        exit 1
    fi
    
    if echo "$remote_files" | grep -q "requirements.txt"; then
        log_success "requirements.txt 验证通过"
    else
        log_error "requirements.txt 验证失败"
        exit 1
    fi
    
    if echo "$remote_files" | grep -q "static"; then
        log_success "static目录 验证通过"
    else
        log_warning "static目录 验证失败"
    fi
    
    log_success "文件上传验证完成"
}

# 设置文件权限
set_file_permissions() {
    log_info "设置文件权限..."
    
    ssh $SERVER_USER@$SERVER_IP "
        chmod 644 $PROJECT_DIR/*.py $PROJECT_DIR/*.txt $PROJECT_DIR/*.html 2>/dev/null || true
        chmod 755 $PROJECT_DIR/ 2>/dev/null || true
        if [ -d $PROJECT_DIR/static ]; then
            chmod -R 644 $PROJECT_DIR/static/* 2>/dev/null || true
            find $PROJECT_DIR/static -type d -exec chmod 755 {} \; 2>/dev/null || true
        fi
    "
    
    log_success "文件权限设置完成"
}

# 显示下一步操作
show_next_steps() {
    echo
    echo "=================================================="
    echo "🎉 文件上传完成！"
    echo "=================================================="
    echo
    echo "下一步操作:"
    echo "1. 登录服务器:"
    echo "   ssh $SERVER_USER@$SERVER_IP"
    echo
    echo "2. 运行部署脚本:"
    echo "   chmod +x quick_deploy.sh"
    echo "   ./quick_deploy.sh"
    echo
    echo "3. 配置域名解析:"
    echo "   vehicle.lkr666.online -> $SERVER_IP"
    echo
    echo "4. 配置SSL证书 (可选):"
    echo "   ./ssl_setup.sh"
    echo
    echo "=================================================="
    echo "如果遇到问题，请检查:"
    echo "- 网络连接是否正常"
    echo "- SSH密钥或密码是否正确"
    echo "- 服务器磁盘空间是否充足"
    echo "=================================================="
}

# 主函数
main() {
    show_welcome
    check_local_files
    test_server_connection
    create_remote_directory
    upload_files
    verify_upload
    set_file_permissions
    show_next_steps
}

# 运行主函数
main "$@"
