#!/usr/bin/env python3
"""
完整的AI集成测试脚本
测试Web应用是否真正使用了深度学习模型
"""

import os
import sys
import time
import requests
import json
from pathlib import Path

def test_server_status():
    """测试服务器状态"""
    print("🧪 测试服务器状态...")
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器运行正常")
            return True
        else:
            print(f"❌ Web服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到Web服务器: {e}")
        return False

def test_ai_model_status():
    """测试AI模型状态"""
    print("🧪 测试AI模型状态...")
    try:
        response = requests.get("http://localhost:5000/api/model-status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ AI模型状态API响应正常")
            print(f"   模型已加载: {data['model_info']['loaded']}")
            print(f"   模型类型: {data['model_info']['type']}")
            print(f"   模型路径: {data['model_info']['path']}")
            return data['model_info']['loaded']
        else:
            print(f"❌ AI模型状态API响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ AI模型状态检查失败: {e}")
        return False

def test_file_upload():
    """测试文件上传功能"""
    print("🧪 测试文件上传功能...")
    
    # 创建一个测试视频文件（实际上是空文件，仅用于测试上传）
    test_file_path = "test_video.mp4"
    try:
        with open(test_file_path, 'wb') as f:
            f.write(b'fake video content for testing')
        
        with open(test_file_path, 'rb') as f:
            files = {'video': ('test_video.mp4', f, 'video/mp4')}
            response = requests.post("http://localhost:5000/api/upload", files=files, timeout=10)
        
        os.remove(test_file_path)  # 清理测试文件
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 文件上传功能正常")
            print(f"   任务ID: {data.get('task_id', 'N/A')}")
            return data.get('task_id')
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 文件上传测试失败: {e}")
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        return None

def test_api_endpoints():
    """测试所有API端点"""
    print("🧪 测试API端点...")
    
    endpoints = [
        ("GET", "/api/model-status", "AI模型状态"),
        ("GET", "/", "主页面"),
    ]
    
    passed = 0
    for method, endpoint, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"http://localhost:5000{endpoint}", timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {description}: 正常")
                passed += 1
            else:
                print(f"❌ {description}: 异常 ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {description}: 失败 ({e})")
    
    print(f"📊 API端点测试: {passed}/{len(endpoints)} 通过")
    return passed == len(endpoints)

def main():
    """主测试函数"""
    print("🔍 YOLOv11车辆计数器 - 完整AI集成测试")
    print("=" * 60)
    print("📋 测试项目:")
    print("   1. Web服务器状态")
    print("   2. AI模型加载状态")
    print("   3. API端点功能")
    print("   4. 文件上传功能")
    print("=" * 60)
    
    tests = [
        ("Web服务器状态", test_server_status),
        ("AI模型状态", test_ai_model_status),
        ("API端点功能", test_api_endpoints),
        ("文件上传功能", lambda: test_file_upload() is not None),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 ({e})")
        print("-" * 40)
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 确认事项:")
        print("   🌐 Web服务器运行正常")
        print("   🤖 AI模型已成功加载")
        print("   🔗 所有API端点正常工作")
        print("   📤 文件上传功能正常")
        print("\n🚀 系统已准备就绪，可以进行真实的AI视频处理！")
        print("\n📖 使用说明:")
        print("   1. 打开浏览器访问: http://localhost:5000")
        print("   2. 上传视频文件（支持mp4, avi, mov, mkv格式）")
        print("   3. 点击开始处理，等待AI分析完成")
        print("   4. 查看处理后的视频和统计结果")
        print("   5. 下载处理后的视频文件")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        print("\n💡 故障排除:")
        print("   1. 确保Web服务器已启动: python app.py")
        print("   2. 检查YOLO模型是否正确安装: pip install ultralytics")
        print("   3. 确保端口5000未被占用")
        print("   4. 检查网络连接和防火墙设置")

if __name__ == "__main__":
    main()