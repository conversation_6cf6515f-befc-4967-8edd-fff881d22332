import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
from ultralytics import Y<PERSON><PERSON>

if __name__ == '__main__':
    # Load a model
    # Using yolo11x.pt as initial weights
    model = YOLO('C:\lkr_yolo\lkr_yolo\yolov11_vehicle_counter\models\weights\yolov12x.pt')  # load a pretrained model (recommended for training)

    # Use the model
    results = model.train(
        data='C:\lkr_yolo\lkr_yolo\yolov11_vehicle_counter\dataset\split_dataset\data.yaml',
        epochs=1000,
        patience=200,
        batch=8, # Reduced batch size to prevent OOM
        imgsz=1024,
        project='runs/train',
        name='yolov12x_vehicle_detection', # Changed name for the new experiment
        device=0, # Assuming GPU 0, change if needed
        workers=8, # Adjust based on your system
        cache=False # Disabled cache to reduce memory usage
    )
