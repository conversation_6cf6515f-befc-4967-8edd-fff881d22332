#!/usr/bin/env python3
"""
基于main_final_perfect.py的Web版本 - 直接移植完美功能
"""

import os
# 关键修复：OpenMP冲突解决（完全复制main_final_perfect.py第9行）
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import sys
import uuid
import time
import threading
import logging
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import queue
from flask import Flask, request, jsonify, send_file, render_template
from werkzeug.utils import secure_filename
from deploy_config import deploy_config
import cv2
import numpy as np
from ultralytics import YOLO

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('webapp.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 使用部署配置初始化Flask应用
app = Flask(__name__,
           static_folder=deploy_config.get_static_folder(),
           template_folder=deploy_config.get_template_folder())
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024

# 全局变量 - 使用部署配置
UPLOAD_FOLDER = deploy_config.get_upload_folder()
OUTPUT_FOLDER = deploy_config.get_output_folder()
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv'}
task_status = {}
task_results = {}

# 并发处理管理
MAX_CONCURRENT_TASKS = 2  # 最大并发任务数
task_executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_TASKS)
processing_queue = queue.Queue()

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 完全复制main_final_perfect.py的CLASS_NAMES
CLASS_NAMES = {
    0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane',
    5: 'bus', 6: 'train', 7: 'truck', 8: 'boat'
}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def is_vehicle(class_id):
    """完全复制main_final_perfect.py的is_vehicle函数"""
    vehicle_classes = {1, 2, 3, 5, 7}  # bicycle, car, motorcycle, bus, truck
    return int(class_id) in vehicle_classes

class AIModelManager:
    """AI模型管理器 - 确保真正使用深度学习模型，支持GPU加速"""
    
    def __init__(self):
        self.model = None
        self.model_loaded = False
        self.model_type = None
        self.model_path = None
        self.device = self.detect_device()
        self.load_model()
    
    def detect_device(self):
        """检测可用的计算设备"""
        try:
            import torch
            if torch.cuda.is_available():
                device = 'cuda'
                print(f"🚀 检测到CUDA GPU: {torch.cuda.get_device_name(0)}")
                print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
            else:
                device = 'cpu'
                print("💻 使用CPU计算")
        except ImportError:
            device = 'cpu'
            print("💻 PyTorch未安装，使用CPU计算")
        return device
    
    def load_model(self) -> bool:
        """加载YOLO模型，带有完整的验证和回退策略"""
        print("🤖 正在初始化AI模型管理器...")
        
        try:
            # 使用部署配置获取模型路径
            custom_paths = deploy_config.get_model_paths()
            
            for path in custom_paths:
                if os.path.exists(path):
                    try:
                        print(f"🔍 尝试加载自定义模型: {path}")
                        self.model = YOLO(path)
                        # 验证模型是否可用
                        if self.validate_model():
                            self.model_loaded = True
                            self.model_type = "custom"
                            self.model_path = path
                            print(f"✅ 成功加载自定义模型: {path}")
                            return True
                    except Exception as e:
                        print(f"⚠️  自定义模型加载失败 {path}: {e}")
                        continue
            
            # 回退到预训练模型
            pretrained_models = ['yolov11m.pt', 'yolov11s.pt', 'yolov11n.pt']
            for model_name in pretrained_models:
                try:
                    print(f"🔍 尝试加载预训练模型: {model_name}")
                    self.model = YOLO(model_name)
                    if self.validate_model():
                        self.model_loaded = True
                        self.model_type = "pretrained"
                        self.model_path = model_name
                        print(f"✅ 成功加载预训练模型: {model_name}")
                        return True
                except Exception as e:
                    print(f"⚠️  预训练模型加载失败 {model_name}: {e}")
                    continue
            
            print("❌ 所有模型加载失败")
            return False
            
        except Exception as e:
            print(f"❌ AI模型管理器初始化失败: {e}")
            return False
    
    def validate_model(self) -> bool:
        """验证模型是否真正可用"""
        if self.model is None:
            return False
        
        try:
            # 创建测试图像
            test_image = np.zeros((640, 640, 3), dtype=np.uint8)
            # 测试检测功能
            results = self.model.predict(source=test_image, verbose=False, device=self.device)
            # 测试跟踪功能
            results = self.model.track(source=test_image, persist=True, verbose=False)
            print("✅ AI模型验证成功")
            return True
        except Exception as e:
            print(f"❌ AI模型验证失败: {e}")
            return False
    
    def get_model_info(self) -> dict:
        """获取模型状态信息"""
        return {
            'loaded': self.model_loaded,
            'type': self.model_type,
            'path': self.model_path,
            'available': self.model is not None
        }

class PerfectVehicleCounter:
    """增强的车辆计数器 - 集成AI模型管理器"""
    
    def __init__(self):
        # 完全复制main_final_perfect.py的全局变量
        self.track_history = {}
        self.vehicle_crossed_ids = set()
        self.person_seen_ids = set()
        self.original_to_clean_id = {}
        self.next_clean_id = 1
        self.vehicle_left_count = 0
        self.vehicle_right_count = 0
        
        # 使用AI模型管理器
        print("🤖 初始化AI模型管理器...")
        self.ai_manager = AIModelManager()
        self.model = self.ai_manager.model
        print(f"🎯 模型状态: {'已加载' if self.model else '未加载'}")
        if self.model:
            print(f"📁 模型类型: {self.ai_manager.model_type}")
            print(f"📂 模型路径: {self.ai_manager.model_path}")
        
        # 性能优化设置
        self.batch_size = 1  # 批处理大小
        self.frame_skip = 1  # 跳帧处理（1=不跳帧）
        self.memory_limit_gb = 4  # 内存限制
    

    
    def get_clean_id(self, original_id):
        """优化的ID管理 - 避免ID号过大"""
        if original_id not in self.original_to_clean_id:
            # 重用已释放的ID号
            if hasattr(self, 'released_ids') and self.released_ids:
                clean_id = self.released_ids.pop(0)
            else:
                clean_id = self.next_clean_id
                self.next_clean_id += 1
            self.original_to_clean_id[original_id] = clean_id
        return self.original_to_clean_id[original_id]
    
    def release_id(self, original_id):
        """释放不再使用的ID"""
        if original_id in self.original_to_clean_id:
            clean_id = self.original_to_clean_id[original_id]
            if not hasattr(self, 'released_ids'):
                self.released_ids = []
            self.released_ids.append(clean_id)
            del self.original_to_clean_id[original_id]
    
    def check_memory_usage(self):
        """检查内存使用情况"""
        try:
            import psutil
            memory_usage = psutil.virtual_memory().used / 1024**3  # GB
            if memory_usage > self.memory_limit_gb:
                print(f"⚠️  内存使用过高: {memory_usage:.2f}GB")
                return False
            return True
        except ImportError:
            return True  # 如果没有psutil，假设内存充足
    
    def process_video(self, input_path, output_path, task_id):
        """完全基于main_final_perfect.py的run_tracker函数逻辑"""
        try:
            # 重置计数器
            self.track_history.clear()
            self.vehicle_crossed_ids.clear()
            self.person_seen_ids.clear()
            self.original_to_clean_id.clear()
            self.next_clean_id = 1
            self.vehicle_left_count = 0
            self.vehicle_right_count = 0
            
            print(f"🚗 开始处理视频: {input_path}")
            
            task_status[task_id] = {'status': 'processing', 'progress': 0, 'message': '正在处理...'}
            
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频: {input_path}")
            
            w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            line_x = w // 2
            
            print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧, 计数线: x={line_x}")
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
            
            frame_count = 0
            start_time = time.time()
            last_fps_time = start_time
            
            while cap.isOpened():
                success, frame = cap.read()
                if not success:
                    break
                
                frame_count += 1
                progress = (frame_count / total_frames) * 100 if total_frames > 0 else 0
                
                task_status[task_id].update({
                    'progress': progress,
                    'message': f'处理第 {frame_count}/{total_frames} 帧',
                    'vehicle_count': self.vehicle_left_count + self.vehicle_right_count,
                    'vehicle_left': self.vehicle_left_count,
                    'vehicle_right': self.vehicle_right_count,
                    'people_count': len(self.person_seen_ids),
                    'current_frame': frame_count,
                    'total_frames': total_frames
                })
                
                # 使用AI模型进行检测和跟踪 - 确保真正使用深度学习
                if self.model is not None:
                    try:
                        results = self.model.track(frame, persist=True, verbose=False)
                        # 记录AI检测结果
                        if results[0].boxes is not None:
                            detection_count = len(results[0].boxes)
                            if frame_count % 30 == 0:  # 每30帧记录一次
                                print(f"🔍 帧 {frame_count}: AI检测到 {detection_count} 个目标")
                    except Exception as e:
                        print(f"⚠️  AI检测失败 (帧 {frame_count}): {e}")
                        results = None
                else:
                    print("❌ AI模型未加载，无法进行检测")
                    results = None
                
                # 绘制处理后的帧
                annotated_frame = frame.copy()
                
                # 绘制计数线 - 使用更醒目的颜色（红色）
                cv2.line(annotated_frame, (line_x, 0), (line_x, h), (0, 0, 255), 6)
                cv2.putText(annotated_frame, "COUNTING LINE", (line_x-120, h-20), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 255), 3)
                
                # 绘制统计信息背景 - 更大的区域
                cv2.rectangle(annotated_frame, (10, 10), (600, 160), (0, 0, 0), -1)
                cv2.rectangle(annotated_frame, (10, 10), (600, 160), (255, 255, 255), 3)
                
                # 绘制详细统计信息 - 更大的字体
                cv2.putText(annotated_frame, f"Left: {self.vehicle_left_count}", (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 0), 3)
                cv2.putText(annotated_frame, f"Right: {self.vehicle_right_count}", (20, 90), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 0, 0), 3)
                cv2.putText(annotated_frame, f"Car: {self.vehicle_left_count + self.vehicle_right_count}", (20, 130), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 255), 3)
                cv2.putText(annotated_frame, f"People: {len(self.person_seen_ids)}", (300, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 0), 3)
                cv2.putText(annotated_frame, f"Frame: {frame_count}", (300, 90), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (200, 200, 200), 2)
                cv2.putText(annotated_frame, f"AI: {'ON' if self.model else 'OFF'}", (300, 130), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0) if self.model else (0, 0, 255), 3)
                
                # 完全复制main_final_perfect.py的计数逻辑
                if results is not None and results[0].boxes is not None and results[0].boxes.id is not None:
                    boxes = results[0].boxes.xywh.cpu()
                    class_ids = results[0].boxes.cls.cpu().numpy()
                    original_track_ids = results[0].boxes.id.int().cpu().tolist()

                    for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
                        center_x, center_y = int(box[0]), int(box[1])
                        clean_id = self.get_clean_id(original_id)
                        class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
                        
                        # 人员统计 - 完全复制main_final_perfect.py逻辑
                        if class_name == 'person':
                            if original_id not in self.person_seen_ids:
                                self.person_seen_ids.add(original_id)
                                print(f"👤 发现新人员 ID:{clean_id}")
                        
                        # 车辆穿越统计 - 优化的计数逻辑
                        elif is_vehicle(cls_id):
                            if original_id in self.track_history:
                                prev_x, _ = self.track_history[original_id]
                                # 确保每个ID只计数一次
                                if original_id not in self.vehicle_crossed_ids:
                                    # 从左到右穿越（需要明显的位置变化）
                                    if prev_x < line_x - 10 and center_x >= line_x + 10:
                                        self.vehicle_right_count += 1
                                        self.vehicle_crossed_ids.add(original_id)
                                        print(f"🚙 车辆 ID:{clean_id} ({class_name}) 从左到右穿越，右侧计数: {self.vehicle_right_count}")
                                        # 立即更新任务状态
                                        task_status[task_id].update({
                                            'vehicle_right': self.vehicle_right_count,
                                            'vehicle_left': self.vehicle_left_count,
                                            'vehicle_count': self.vehicle_left_count + self.vehicle_right_count
                                        })
                                    # 从右到左穿越（需要明显的位置变化）
                                    elif prev_x > line_x + 10 and center_x <= line_x - 10:
                                        self.vehicle_left_count += 1
                                        self.vehicle_crossed_ids.add(original_id)
                                        print(f"🚗 车辆 ID:{clean_id} ({class_name}) 从右到左穿越，左侧计数: {self.vehicle_left_count}")
                                        # 立即更新任务状态
                                        task_status[task_id].update({
                                            'vehicle_right': self.vehicle_right_count,
                                            'vehicle_left': self.vehicle_left_count,
                                            'vehicle_count': self.vehicle_left_count + self.vehicle_right_count
                                        })
                            else:
                                print(f"🔍 发现新车辆 ID:{clean_id} ({class_name}) 位置: x={center_x}")
                        
                        # 绘制检测框和ID - 更清晰的显示
                        x1, y1, x2, y2 = int(box[0] - box[2]/2), int(box[1] - box[3]/2), int(box[0] + box[2]/2), int(box[1] + box[3]/2)
                        
                        # 根据类别和状态选择颜色
                        if is_vehicle(cls_id):
                            if original_id in self.vehicle_crossed_ids:
                                color = (0, 255, 0)  # 已计数的车辆用绿色
                            else:
                                color = (255, 0, 255)  # 未计数的车辆用紫色
                        else:
                            color = (255, 255, 0)  # 人员用黄色
                        
                        # 绘制边界框 - 更粗的线条
                        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 3)
                        
                        # 绘制ID和类别标签 - 更大的字体
                        label = f"ID:{clean_id} {class_name}"
                        # 添加标签背景
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                        cv2.rectangle(annotated_frame, (x1, y1-30), (x1+label_size[0]+10, y1), color, -1)
                        cv2.putText(annotated_frame, label, (x1+5, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
                        
                        # 绘制中心点 - 更大的点
                        cv2.circle(annotated_frame, (center_x, center_y), 6, color, -1)
                        
                        # 绘制轨迹（如果有历史位置）- 更粗的轨迹线
                        if original_id in self.track_history:
                            prev_x, prev_y = self.track_history[original_id]
                            cv2.line(annotated_frame, (prev_x, prev_y), (center_x, center_y), color, 3)
                        
                        # 更新历史位置
                        self.track_history[original_id] = (center_x, center_y)
                
                out.write(annotated_frame)
                
                # 性能监控和内存管理 - 优化更新频率
                if frame_count % 5 == 0:  # 每5帧更新一次，降低显示延时
                    current_time = time.time()
                    fps = 100 / (current_time - last_fps_time) if frame_count > 0 else 0
                    last_fps_time = current_time
                    print(f"📊 进度: {frame_count}/{total_frames}, 车辆: {self.vehicle_left_count + self.vehicle_right_count}, 人员: {len(self.person_seen_ids)}, FPS: {fps:.1f}")
                    
                    # GPU内存管理
                    if self.ai_manager.device == 'cuda':
                        try:
                            import torch
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()  # 清理GPU缓存
                                gpu_memory = torch.cuda.memory_allocated() / 1024**3
                                print(f"🔥 GPU内存使用: {gpu_memory:.2f}GB")
                        except:
                            pass
                    
                    # 更新任务状态包含FPS信息
                    task_status[task_id].update({
                        'current_fps': fps,
                        'ai_detections': len(results[0].boxes) if results and results[0].boxes is not None else 0,
                        'vehicle_count': self.vehicle_left_count + self.vehicle_right_count,
                        'vehicle_left': self.vehicle_left_count,
                        'vehicle_right': self.vehicle_right_count,
                        'people_count': len(self.person_seen_ids),
                        'gpu_device': self.ai_manager.device,
                        'memory_optimized': True
                    })
            
            cap.release()
            out.release()
            
            # 保存结果
            processing_time = time.time() - start_time
            avg_fps = frame_count / processing_time if processing_time > 0 else 0
            task_results[task_id] = {
                'vehicle_left': self.vehicle_left_count,
                'vehicle_right': self.vehicle_right_count,
                'vehicle_total': self.vehicle_left_count + self.vehicle_right_count,
                'people_total': len(self.person_seen_ids),
                'processed_frames': frame_count,
                'processing_time': f"{processing_time:.2f}s",
                'avg_fps': avg_fps,
                'completion_time': datetime.now().isoformat()
            }
            
            task_status[task_id] = {'status': 'completed', 'progress': 100, 'message': '处理完成'}
            
            print("=" * 50)
            print("🎉 处理完成！")
            print(f"📊 最终结果: 左侧{self.vehicle_left_count}, 右侧{self.vehicle_right_count}, 总计{self.vehicle_left_count + self.vehicle_right_count}")
            print(f"👥 人员总数: {len(self.person_seen_ids)}")
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            task_status[task_id] = {'status': 'error', 'message': f'处理失败: {str(e)}'}

# Flask路由
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_video():
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['video']
        if file.filename == '' or not allowed_file(file.filename):
            return jsonify({'error': '文件格式不支持'}), 400
        
        task_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{filename}")
        file.save(file_path)
        
        task_status[task_id] = {
            'status': 'uploaded',
            'progress': 0,
            'message': '文件上传成功',
            'file_path': file_path
        }
        
        return jsonify({'task_id': task_id, 'message': '文件上传成功'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/process', methods=['POST'])
def process_video():
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id or task_id not in task_status:
            return jsonify({'error': '无效的任务ID'}), 400
        
        input_path = task_status[task_id]['file_path']
        output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_gorgeous.mp4")
        
        counter = PerfectVehicleCounter()
        
        # 使用线程池进行并发处理
        future = task_executor.submit(counter.process_video, input_path, output_path, task_id)
        
        # 可选：存储future以便后续取消任务
        task_status[task_id]['future'] = future
        
        return jsonify({'message': '开始处理', 'task_id': task_id})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    if task_id not in task_status:
        return jsonify({'error': '任务不存在'}), 404
    return jsonify(task_status[task_id])

@app.route('/api/results/<task_id>')
def get_results(task_id):
    if task_id not in task_results:
        return jsonify({'error': '结果不存在'}), 404
    return jsonify(task_results[task_id])

@app.route('/api/download/<task_id>')
def download_video(task_id):
    output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_gorgeous.mp4")
    if not os.path.exists(output_path):
        return jsonify({'error': '文件不存在'}), 404
    return send_file(output_path, as_attachment=True, download_name=f"perfect_video_{task_id}.mp4")

@app.route('/api/preview/<task_id>')
def preview_video(task_id):
    """预览处理后的视频"""
    output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_gorgeous.mp4")
    print(f"🎬 预览视频请求: {task_id}")
    print(f"📁 查找文件: {output_path}")
    print(f"📊 文件存在: {os.path.exists(output_path)}")
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path)
        print(f"📏 文件大小: {file_size} bytes")
    
    if not os.path.exists(output_path):
        return jsonify({'error': '文件不存在'}), 404
    return send_file(output_path, mimetype='video/mp4')

@app.route('/api/model-status')
def get_model_status():
    """获取AI模型状态"""
    try:
        # 创建临时AI管理器来检查状态
        ai_manager = AIModelManager()
        model_info = ai_manager.get_model_info()
        return jsonify({
            'status': 'success',
            'model_info': model_info,
            'message': 'AI模型已就绪' if model_info['loaded'] else 'AI模型未加载'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'模型状态检查失败: {str(e)}',
            'model_info': {
                'loaded': False,
                'type': None,
                'path': None,
                'available': False
            }
        })

if __name__ == '__main__':
    # 验证部署环境
    deploy_config.validate_environment()
    deploy_config.print_deployment_info()

    # 检查AI功能状态
    try:
        test_ai = AIModelManager()
        if test_ai.model_loaded:
            print("✅ AI功能状态: YOLO模型已成功加载")
            print(f"🎯 模型类型: {test_ai.model_type}")
            print(f"📁 模型路径: {test_ai.model_path}")
        else:
            print("❌ AI功能状态: YOLO模型加载失败")
    except Exception as e:
        print(f"⚠️  AI功能检查失败: {e}")
    
    print("✨ 功能特色:")
    print("   🤖 真实的YOLO深度学习检测")
    print("   🎯 完整的目标追踪和计数")
    print("   🚗 精确的车辆穿越计数（左右分别统计）")
    print("   👥 人员检测和统计（按ID去重）")
    print("   🎨 高级可视化注释和调试信息")
    print("   📹 处理后视频预览和下载")
    print("   🔧 OpenMP冲突解决")
    print("   🛡️  完整错误处理和恢复")
    print("=" * 70)
    print("🌐 访问地址: http://localhost:5000")
    print("📁 上传目录:", os.path.abspath(UPLOAD_FOLDER))
    print("📁 输出目录:", os.path.abspath(OUTPUT_FOLDER))
    print("🎯 AI功能已就绪，可以进行真实的车辆检测和计数")
    try:
        # 使用部署配置启动服务器
        server_config = deploy_config.get_server_config()
        app.run(**server_config)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        input("按回车键退出...")