@echo off
chcp 65001 >nul
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █                    YOLOv11 车辆计数器 - AI完整版                            █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🤖 AI功能特色:
echo    ✅ 真实的YOLO深度学习模型检测
echo    ✅ 完整的目标追踪和计数
echo    ✅ 精确的车辆穿越计数（左右分别统计）
echo    ✅ 人员检测和统计（按ID去重）
echo    ✅ 高级可视化注释和调试信息
echo    ✅ 处理后视频预览和下载
echo    ✅ OpenMP冲突自动解决
echo    ✅ 完整错误处理和恢复
echo.
echo ════════════════════════════════════════════════════════════════════════════════
echo.
echo 🔍 正在检查系统状态...
python -c "import ultralytics; print('✅ YOLO已安装')" 2>nul || (
    echo ❌ YOLO未安装，正在安装...
    pip install ultralytics
)
echo.
echo 🚀 启动AI功能完整版Web服务器...
echo.
echo 📖 使用说明:
echo    1. 等待服务器启动完成
echo    2. 打开浏览器访问: http://localhost:5000
echo    3. 上传视频文件进行AI分析
echo    4. 查看实时追踪和计数结果
echo    5. 下载处理后的视频
echo.
echo ⚠️  注意: 首次运行可能需要下载YOLO模型文件
echo.
echo ════════════════════════════════════════════════════════════════════════════════
echo.
python app.py
echo.
echo 服务器已停止
pause