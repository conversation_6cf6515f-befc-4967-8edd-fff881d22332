#!/usr/bin/env python3
"""
启动简化版Web界面 - 本地测试
"""

import sys
from pathlib import Path

# 添加src目录到路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

if __name__ == "__main__":
    print("🚗 YOLOv11车辆计数器 - 简化Web界面")
    print("🎯 专注于本地测试基本功能")
    print("=" * 50)
    
    try:
        from webui_simple import main
        main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")