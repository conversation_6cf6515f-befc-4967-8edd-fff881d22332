#!/bin/bash
# 服务器环境自动配置脚本 - YOLOv11车辆计数系统
# 支持Ubuntu 18.04/20.04/22.04

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo bash $0"
        exit 1
    fi
}

# 检测系统信息
detect_system() {
    log_step "检测系统信息..."
    
    # 检测操作系统
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
        CODENAME=$VERSION_CODENAME
    else
        log_error "无法检测操作系统版本"
        exit 1
    fi
    
    # 检测架构
    ARCH=$(uname -m)
    
    # 检测GPU
    GPU_INFO=""
    if lspci | grep -i nvidia > /dev/null 2>&1; then
        GPU_INFO="NVIDIA GPU detected"
        HAS_NVIDIA=true
    else
        GPU_INFO="No NVIDIA GPU detected"
        HAS_NVIDIA=false
    fi
    
    # 检测内存
    TOTAL_MEM=$(free -h | awk '/^Mem:/ {print $2}')
    
    # 检测CPU核心数
    CPU_CORES=$(nproc)
    
    log_info "系统信息:"
    log_info "  操作系统: $OS $VER ($CODENAME)"
    log_info "  架构: $ARCH"
    log_info "  CPU核心: $CPU_CORES"
    log_info "  内存: $TOTAL_MEM"
    log_info "  GPU: $GPU_INFO"
    
    # 检查系统兼容性
    if [[ "$OS" != *"Ubuntu"* ]]; then
        log_warn "此脚本主要为Ubuntu系统设计，其他系统可能需要调整"
    fi
    
    if [[ "$ARCH" != "x86_64" ]]; then
        log_warn "检测到非x86_64架构，某些组件可能不兼容"
    fi
}

# 更新系统
update_system() {
    log_step "更新系统包..."
    
    export DEBIAN_FRONTEND=noninteractive
    
    # 更新包列表
    apt-get update -y
    
    # 升级系统包
    apt-get upgrade -y
    
    # 安装基础工具
    apt-get install -y \
        curl \
        wget \
        gnupg \
        lsb-release \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        build-essential \
        cmake \
        git \
        vim \
        htop \
        tree \
        unzip \
        zip
        
    log_success "系统更新完成"
}

# 安装NVIDIA驱动
install_nvidia_driver() {
    if [ "$HAS_NVIDIA" = false ]; then
        log_warn "未检测到NVIDIA GPU，跳过驱动安装"
        return 0
    fi
    
    log_step "安装NVIDIA驱动..."
    
    # 检查是否已安装驱动
    if nvidia-smi > /dev/null 2>&1; then
        log_info "NVIDIA驱动已安装:"
        nvidia-smi --query-gpu=driver_version --format=csv,noheader,nounits
        return 0
    fi
    
    # 添加NVIDIA驱动PPA
    add-apt-repository ppa:graphics-drivers/ppa -y
    apt-get update -y
    
    # 自动检测推荐驱动版本
    RECOMMENDED_DRIVER=$(ubuntu-drivers devices | grep recommended | awk '{print $3}')
    
    if [ -z "$RECOMMENDED_DRIVER" ]; then
        # 如果无法自动检测，使用稳定版本
        RECOMMENDED_DRIVER="nvidia-driver-525"
        log_warn "无法自动检测推荐驱动，使用默认版本: $RECOMMENDED_DRIVER"
    else
        log_info "检测到推荐驱动: $RECOMMENDED_DRIVER"
    fi
    
    # 安装驱动
    apt-get install -y $RECOMMENDED_DRIVER
    
    log_success "NVIDIA驱动安装完成，需要重启系统生效"
    NEED_REBOOT=true
}

# 安装CUDA
install_cuda() {
    if [ "$HAS_NVIDIA" = false ]; then
        log_warn "未检测到NVIDIA GPU，跳过CUDA安装"
        return 0
    fi
    
    log_step "安装CUDA..."
    
    # 检查是否已安装CUDA
    if nvcc --version > /dev/null 2>&1; then
        log_info "CUDA已安装:"
        nvcc --version | grep "release"
        return 0
    fi
    
    # CUDA版本配置
    CUDA_VERSION="12.2"
    CUDA_VERSION_FULL="12-2"
    
    # 下载CUDA keyring
    wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu$(echo $VER | tr -d '.')/x86_64/cuda-keyring_1.0-1_all.deb
    dpkg -i cuda-keyring_1.0-1_all.deb
    
    # 更新包列表
    apt-get update -y
    
    # 安装CUDA toolkit
    apt-get install -y cuda-toolkit-$CUDA_VERSION_FULL
    
    # 设置环境变量
    echo 'export PATH=/usr/local/cuda/bin:$PATH' >> /etc/environment
    echo 'export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH' >> /etc/environment
    
    # 立即生效
    export PATH=/usr/local/cuda/bin:$PATH
    export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH
    
    # 清理下载文件
    rm -f cuda-keyring_1.0-1_all.deb
    
    log_success "CUDA安装完成"
}

# 安装cuDNN
install_cudnn() {
    if [ "$HAS_NVIDIA" = false ]; then
        log_warn "未检测到NVIDIA GPU，跳过cuDNN安装"
        return 0
    fi
    
    log_step "安装cuDNN..."
    
    # 检查是否已安装cuDNN
    if [ -f /usr/local/cuda/include/cudnn.h ]; then
        log_info "cuDNN已安装"
        return 0
    fi
    
    # 安装cuDNN (通过apt)
    apt-get install -y libcudnn8 libcudnn8-dev
    
    log_success "cuDNN安装完成"
}

# 安装Python环境
install_python() {
    log_step "安装Python环境..."
    
    # 安装Python 3.9及相关工具
    apt-get install -y \
        python3.9 \
        python3.9-dev \
        python3.9-venv \
        python3-pip \
        python3-setuptools \
        python3-wheel
    
    # 设置Python3.9为默认python3
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1
    
    # 升级pip
    python3 -m pip install --upgrade pip
    
    # 安装常用Python包
    python3 -m pip install \
        numpy \
        opencv-python \
        pillow \
        matplotlib \
        scipy \
        scikit-learn \
        pandas
    
    log_success "Python环境安装完成"
}

# 安装PyTorch
install_pytorch() {
    log_step "安装PyTorch..."
    
    if [ "$HAS_NVIDIA" = true ]; then
        # 安装GPU版本PyTorch
        log_info "安装GPU版本PyTorch..."
        python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    else
        # 安装CPU版本PyTorch
        log_info "安装CPU版本PyTorch..."
        python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    fi
    
    log_success "PyTorch安装完成"
}

# 安装OpenCV依赖
install_opencv_deps() {
    log_step "安装OpenCV依赖..."
    
    apt-get install -y \
        libopencv-dev \
        python3-opencv \
        libgtk-3-dev \
        libavcodec-dev \
        libavformat-dev \
        libswscale-dev \
        libv4l-dev \
        libxvidcore-dev \
        libx264-dev \
        libjpeg-dev \
        libpng-dev \
        libtiff-dev \
        gfortran \
        openexr \
        libatlas-base-dev \
        libtbb2 \
        libtbb-dev \
        libdc1394-22-dev \
        libopenexr-dev \
        libgstreamer-plugins-base1.0-dev \
        libgstreamer1.0-dev
    
    log_success "OpenCV依赖安装完成"
}

# 安装Ultralytics YOLO
install_ultralytics() {
    log_step "安装Ultralytics YOLO..."
    
    # 安装ultralytics
    python3 -m pip install ultralytics
    
    # 验证安装
    python3 -c "from ultralytics import YOLO; print('Ultralytics YOLO安装成功')"
    
    log_success "Ultralytics YOLO安装完成"
}

# 安装Web服务依赖
install_web_deps() {
    log_step "安装Web服务依赖..."
    
    # 安装Apache
    apt-get install -y apache2 libapache2-mod-wsgi-py3
    
    # 启用Apache模块
    a2enmod ssl
    a2enmod rewrite
    a2enmod headers
    a2enmod proxy
    a2enmod proxy_http
    a2enmod proxy_wstunnel
    
    # 安装Python Web框架
    python3 -m pip install \
        flask \
        flask-socketio \
        gunicorn \
        eventlet \
        werkzeug
    
    # 安装Supervisor
    apt-get install -y supervisor
    
    log_success "Web服务依赖安装完成"
}

# 创建项目目录
create_project_structure() {
    log_step "创建项目目录结构..."
    
    PROJECT_DIR="/var/www/vehicle-counter"
    
    # 创建目录
    mkdir -p $PROJECT_DIR/{models,uploads,static,templates,logs}
    mkdir -p /var/log/vehicle-counter
    
    # 设置权限
    chown -R www-data:www-data $PROJECT_DIR
    chown -R www-data:www-data /var/log/vehicle-counter
    chmod -R 755 $PROJECT_DIR
    chmod -R 755 /var/log/vehicle-counter
    
    log_success "项目目录创建完成: $PROJECT_DIR"
}

# 系统优化
optimize_system() {
    log_step "优化系统配置..."
    
    # 增加文件描述符限制
    echo "* soft nofile 65536" >> /etc/security/limits.conf
    echo "* hard nofile 65536" >> /etc/security/limits.conf
    
    # 优化内核参数
    cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.core.netdev_max_backlog = 5000

# 文件系统优化
fs.file-max = 2097152
vm.swappiness = 10
EOF
    
    # 应用内核参数
    sysctl -p
    
    log_success "系统优化完成"
}

# 测试GPU环境
test_gpu_environment() {
    if [ "$HAS_NVIDIA" = false ]; then
        log_warn "未检测到NVIDIA GPU，跳过GPU测试"
        return 0
    fi

    log_step "测试GPU环境..."

    # 测试NVIDIA驱动
    if nvidia-smi > /dev/null 2>&1; then
        log_info "NVIDIA驱动测试通过:"
        nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv,noheader
    else
        log_error "NVIDIA驱动测试失败"
        return 1
    fi

    # 测试CUDA
    if nvcc --version > /dev/null 2>&1; then
        log_info "CUDA测试通过:"
        nvcc --version | grep "release"
    else
        log_error "CUDA测试失败"
        return 1
    fi

    # 测试PyTorch GPU支持
    python3 -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU数量: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
"

    log_success "GPU环境测试完成"
}

# 下载和测试YOLO模型
download_test_models() {
    log_step "下载和测试YOLO模型..."

    PROJECT_DIR="/var/www/vehicle-counter"

    # 创建测试脚本
    cat > /tmp/test_yolo.py << 'EOF'
import sys
import os
sys.path.append('/var/www/vehicle-counter')

try:
    from ultralytics import YOLO
    import torch
    import cv2
    import numpy as np

    print("=== YOLO模型测试 ===")

    # 下载YOLOv11模型
    print("下载YOLOv11n模型...")
    model = YOLO('yolo11n.pt')

    # 移动模型到项目目录
    import shutil
    if os.path.exists('yolo11n.pt'):
        shutil.move('yolo11n.pt', '/var/www/vehicle-counter/models/')
        print("模型已移动到项目目录")

    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)

    # 测试推理
    print("测试模型推理...")
    results = model(test_image, verbose=False)

    print(f"推理成功! 检测到 {len(results[0].boxes) if results[0].boxes is not None else 0} 个目标")

    # 测试设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    if torch.cuda.is_available():
        model.to('cuda')
        results = model(test_image, verbose=False)
        print("GPU推理测试通过")

    print("=== YOLO测试完成 ===")

except Exception as e:
    print(f"YOLO测试失败: {str(e)}")
    sys.exit(1)
EOF

    # 运行测试
    python3 /tmp/test_yolo.py

    # 清理测试文件
    rm -f /tmp/test_yolo.py

    log_success "YOLO模型下载和测试完成"
}

# 创建系统服务
create_system_services() {
    log_step "创建系统服务..."

    # 创建vehicle-counter服务
    cat > /etc/systemd/system/vehicle-counter.service << 'EOF'
[Unit]
Description=Vehicle Counter Web Application
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/vehicle-counter
Environment=PATH=/var/www/vehicle-counter/venv/bin
ExecStart=/var/www/vehicle-counter/venv/bin/gunicorn -c gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload

    log_success "系统服务创建完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."

    # 安装ufw
    apt-get install -y ufw

    # 配置防火墙规则
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing

    # 允许SSH
    ufw allow 22/tcp

    # 允许HTTP/HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp

    # 启用防火墙
    ufw --force enable

    log_success "防火墙配置完成"
}

# 创建监控脚本
create_monitoring() {
    log_step "创建监控脚本..."

    cat > /usr/local/bin/vehicle-counter-monitor.sh << 'EOF'
#!/bin/bash
# 车辆计数系统监控脚本

LOG_FILE="/var/log/vehicle-counter/monitor.log"
PROJECT_DIR="/var/www/vehicle-counter"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查服务状态
check_service() {
    if systemctl is-active --quiet vehicle-counter; then
        log_message "INFO: 服务运行正常"
        return 0
    else
        log_message "ERROR: 服务未运行，尝试重启"
        systemctl restart vehicle-counter
        sleep 5
        if systemctl is-active --quiet vehicle-counter; then
            log_message "INFO: 服务重启成功"
        else
            log_message "ERROR: 服务重启失败"
        fi
    fi
}

# 检查GPU状态
check_gpu() {
    if command -v nvidia-smi &> /dev/null; then
        if nvidia-smi > /dev/null 2>&1; then
            log_message "INFO: GPU状态正常"
        else
            log_message "ERROR: GPU状态异常"
        fi
    fi
}

# 检查磁盘空间
check_disk() {
    DISK_USAGE=$(df /var/www | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        log_message "WARN: 磁盘使用率过高: ${DISK_USAGE}%"
    fi
}

# 清理日志文件
cleanup_logs() {
    find /var/log/vehicle-counter -name "*.log" -mtime +7 -delete
    find $PROJECT_DIR/uploads -name "*" -mtime +1 -delete
}

# 执行检查
check_service
check_gpu
check_disk
cleanup_logs

log_message "INFO: 监控检查完成"
EOF

    chmod +x /usr/local/bin/vehicle-counter-monitor.sh

    # 添加到crontab
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/vehicle-counter-monitor.sh") | crontab -

    log_success "监控脚本创建完成"
}

# 生成配置文件
generate_configs() {
    log_step "生成配置文件..."

    PROJECT_DIR="/var/www/vehicle-counter"

    # 创建Gunicorn配置
    cat > $PROJECT_DIR/gunicorn.conf.py << 'EOF'
import multiprocessing

# 服务器套接字
bind = "127.0.0.1:5000"
backlog = 2048

# 工作进程
workers = min(2, multiprocessing.cpu_count())
worker_class = "eventlet"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 超时设置
timeout = 300
keepalive = 2
graceful_timeout = 30

# 日志设置
accesslog = "/var/log/vehicle-counter/access.log"
errorlog = "/var/log/vehicle-counter/error.log"
loglevel = "info"

# 进程命名
proc_name = "vehicle-counter"

# 预加载应用
preload_app = True
EOF

    # 创建Apache虚拟主机配置模板
    cat > $PROJECT_DIR/apache-vhost-template.conf << 'EOF'
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/vehicle-counter

    # 重定向到HTTPS
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /var/www/vehicle-counter

    # SSL配置 (需要配置证书)
    # SSLEngine on
    # SSLCertificateFile /path/to/certificate.crt
    # SSLCertificateKeyFile /path/to/private.key

    # 代理设置
    ProxyPreserveHost On
    ProxyRequests Off

    # 静态文件
    ProxyPass /static/ !
    ProxyPass /uploads/ !

    # 主应用代理
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/

    # WebSocket支持
    RewriteEngine On
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/?(.*) "ws://127.0.0.1:5000/$1" [P,L]

    # 静态文件服务
    Alias /static /var/www/vehicle-counter/static
    Alias /uploads /var/www/vehicle-counter/uploads

    # 日志
    ErrorLog /var/log/apache2/vehicle-counter-error.log
    CustomLog /var/log/apache2/vehicle-counter-access.log combined
</VirtualHost>
EOF

    # 设置权限
    chown -R www-data:www-data $PROJECT_DIR

    log_success "配置文件生成完成"
}

# 环境验证
verify_environment() {
    log_step "验证环境安装..."

    ERRORS=0

    # 检查Python
    if python3 --version > /dev/null 2>&1; then
        log_info "✓ Python: $(python3 --version)"
    else
        log_error "✗ Python未正确安装"
        ((ERRORS++))
    fi

    # 检查pip包
    REQUIRED_PACKAGES=("torch" "ultralytics" "opencv-python" "flask" "numpy")
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if python3 -c "import $package" > /dev/null 2>&1; then
            log_info "✓ $package已安装"
        else
            log_error "✗ $package未安装"
            ((ERRORS++))
        fi
    done

    # 检查NVIDIA环境
    if [ "$HAS_NVIDIA" = true ]; then
        if nvidia-smi > /dev/null 2>&1; then
            log_info "✓ NVIDIA驱动正常"
        else
            log_error "✗ NVIDIA驱动异常"
            ((ERRORS++))
        fi

        if nvcc --version > /dev/null 2>&1; then
            log_info "✓ CUDA正常"
        else
            log_error "✗ CUDA异常"
            ((ERRORS++))
        fi
    fi

    # 检查服务
    SERVICES=("apache2" "supervisor")
    for service in "${SERVICES[@]}"; do
        if systemctl is-enabled $service > /dev/null 2>&1; then
            log_info "✓ $service服务已启用"
        else
            log_warn "! $service服务未启用"
        fi
    done

    if [ $ERRORS -eq 0 ]; then
        log_success "环境验证通过！"
        return 0
    else
        log_error "环境验证失败，发现 $ERRORS 个错误"
        return 1
    fi
}

# 显示安装总结
show_summary() {
    log_step "安装总结"

    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  车辆计数系统环境配置完成${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    echo -e "${GREEN}系统信息:${NC}"
    echo -e "  操作系统: $OS $VER"
    echo -e "  架构: $ARCH"
    echo -e "  CPU核心: $CPU_CORES"
    echo -e "  内存: $TOTAL_MEM"
    echo -e "  GPU: $GPU_INFO"
    echo ""
    echo -e "${GREEN}已安装组件:${NC}"
    echo -e "  ✓ Python 3.9 + pip"
    echo -e "  ✓ PyTorch ($([ "$HAS_NVIDIA" = true ] && echo "GPU版本" || echo "CPU版本"))"
    echo -e "  ✓ Ultralytics YOLO"
    echo -e "  ✓ OpenCV"
    echo -e "  ✓ Flask + Gunicorn"
    echo -e "  ✓ Apache + Supervisor"
    if [ "$HAS_NVIDIA" = true ]; then
        echo -e "  ✓ NVIDIA驱动 + CUDA + cuDNN"
    fi
    echo ""
    echo -e "${GREEN}项目目录:${NC}"
    echo -e "  /var/www/vehicle-counter/"
    echo ""
    echo -e "${GREEN}日志目录:${NC}"
    echo -e "  /var/log/vehicle-counter/"
    echo ""
    echo -e "${GREEN}下一步操作:${NC}"
    echo -e "  1. 上传项目代码到 /var/www/vehicle-counter/"
    echo -e "  2. 配置Apache虚拟主机 (模板已生成)"
    echo -e "  3. 启动服务: systemctl start vehicle-counter"
    echo -e "  4. 配置SSL证书 (如需要)"
    echo ""

    if [ "$NEED_REBOOT" = true ]; then
        echo -e "${YELLOW}⚠️  需要重启系统以使NVIDIA驱动生效${NC}"
        echo -e "${YELLOW}   重启命令: reboot${NC}"
    fi

    echo -e "${CYAN}================================${NC}"
}

# 主函数
main() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    YOLOv11车辆计数系统 - 服务器环境配置脚本"
    echo "=================================================="
    echo -e "${NC}"

    NEED_REBOOT=false

    check_root
    detect_system

    echo ""
    read -p "是否继续安装? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi

    # 执行安装步骤
    update_system
    install_nvidia_driver
    install_cuda
    install_cudnn
    install_python
    install_pytorch
    install_opencv_deps
    install_ultralytics
    install_web_deps
    create_project_structure
    optimize_system
    test_gpu_environment
    download_test_models
    create_system_services
    configure_firewall
    create_monitoring
    generate_configs

    # 验证安装
    if verify_environment; then
        show_summary
        log_success "环境配置完成！"
    else
        log_error "环境配置过程中出现错误，请检查日志"
        exit 1
    fi
}

# 脚本入口
case "${1:-install}" in
    install)
        main
        ;;
    test)
        detect_system
        verify_environment
        ;;
    gpu-test)
        detect_system
        test_gpu_environment
        ;;
    *)
        echo "用法: $0 {install|test|gpu-test}"
        echo "  install   - 完整安装环境"
        echo "  test      - 测试当前环境"
        echo "  gpu-test  - 测试GPU环境"
        exit 1
        ;;
esac
