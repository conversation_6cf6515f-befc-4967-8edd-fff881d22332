# YOLOv11车辆计数系统 - 云服务器部署指南

## 服务器环境信息
- **系统**: Ubuntu 22.04.4 LTS
- **IP**: ************ (内网) / 你的公网IP
- **现有服务**: Apache (已有网站运行)

## 1. 系统准备

### 1.1 更新系统
```bash
sudo apt update && sudo apt upgrade -y
```

### 1.2 安装Python环境
```bash
# 安装Python 3.10+
sudo apt install python3 python3-pip python3-venv -y

# 安装系统依赖
sudo apt install python3-dev build-essential -y
sudo apt install libgl1-mesa-glx libglib2.0-0 -y
sudo apt install ffmpeg libsm6 libxext6 -y
```

## 2. 项目部署

### 2.1 创建项目目录
```bash
# 创建项目根目录
sudo mkdir -p /var/www/vehicle-counter
sudo chown $USER:$USER /var/www/vehicle-counter
cd /var/www/vehicle-counter
```

### 2.2 上传项目文件
```bash
# 方法1: 使用scp从本地上传
scp -r /path/to/yolov11_vehicle_counter/webapp/* user@your-server:/var/www/vehicle-counter/

# 方法2: 使用git (如果有仓库)
git clone your-repo-url .
```

### 2.3 创建Python虚拟环境
```bash
cd /var/www/vehicle-counter
python3 -m venv venv
source venv/bin/activate
```

### 2.4 安装依赖
```bash
# 安装基础依赖
pip install --upgrade pip
pip install -r requirements.txt

# 安装额外的生产环境依赖
pip install gunicorn supervisor
```

## 3. Apache虚拟主机配置

### 3.1 启用必要的Apache模块
```bash
sudo a2enmod rewrite
sudo a2enmod proxy
sudo a2enmod proxy_http
sudo a2enmod headers
sudo systemctl restart apache2
```

### 3.2 创建虚拟主机配置
```bash
sudo nano /etc/apache2/sites-available/vehicle-counter.conf
```

配置内容：
```apache
<VirtualHost *:80>
    ServerName vehicle.your-domain.com
    ServerAlias www.vehicle.your-domain.com
    
    DocumentRoot /var/www/vehicle-counter
    
    # 静态文件直接由Apache服务
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
    </Directory>
    
    # API请求代理到Flask应用
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    # 主页面代理到Flask
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/vehicle-counter_error.log
    CustomLog ${APACHE_LOG_DIR}/vehicle-counter_access.log combined
</VirtualHost>
```

### 3.3 启用站点
```bash
sudo a2ensite vehicle-counter.conf
sudo systemctl reload apache2
```

## 4. 生产环境配置

### 4.1 创建Gunicorn配置
```bash
nano /var/www/vehicle-counter/gunicorn.conf.py
```

配置内容：
```python
# Gunicorn配置文件
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

### 4.2 创建启动脚本
```bash
nano /var/www/vehicle-counter/start.sh
```

脚本内容：
```bash
#!/bin/bash
cd /var/www/vehicle-counter
source venv/bin/activate
exec gunicorn --config gunicorn.conf.py app:app
```

```bash
chmod +x /var/www/vehicle-counter/start.sh
```

### 4.3 配置Supervisor (进程管理)
```bash
sudo nano /etc/supervisor/conf.d/vehicle-counter.conf
```

配置内容：
```ini
[program:vehicle-counter]
command=/var/www/vehicle-counter/start.sh
directory=/var/www/vehicle-counter
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
environment=PATH="/var/www/vehicle-counter/venv/bin"
```

### 4.4 启动服务
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start vehicle-counter
sudo supervisorctl status
```

## 5. 域名和SSL配置

### 5.1 域名解析
在你的域名提供商处添加A记录：
```
vehicle.your-domain.com -> 你的服务器公网IP
```

### 5.2 SSL证书 (Let's Encrypt)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-apache -y

# 获取SSL证书
sudo certbot --apache -d vehicle.your-domain.com
```

## 6. 防火墙配置
```bash
# 开放HTTP和HTTPS端口
sudo ufw allow 80
sudo ufw allow 443
sudo ufw reload
```

## 7. 测试部署
```bash
# 检查服务状态
sudo supervisorctl status vehicle-counter
sudo systemctl status apache2

# 测试API
curl http://localhost:5000/api/health
curl http://vehicle.your-domain.com/api/health
```

## 8. 监控和维护

### 8.1 日志查看
```bash
# 应用日志
sudo tail -f /var/log/vehicle-counter.log

# Apache日志
sudo tail -f /var/log/apache2/vehicle-counter_access.log
sudo tail -f /var/log/apache2/vehicle-counter_error.log
```

### 8.2 性能优化
- 配置适当的worker数量
- 设置文件上传大小限制
- 配置缓存策略

## 9. 备份策略
```bash
# 创建备份脚本
sudo nano /usr/local/bin/backup-vehicle-counter.sh
```

## 故障排除
1. **端口冲突**: 确保5000端口未被占用
2. **权限问题**: 检查文件权限和用户组
3. **依赖问题**: 确保所有Python包正确安装
4. **内存不足**: 监控服务器资源使用情况

## 下一步
部署完成后，你可以：
1. 配置监控系统
2. 设置自动备份
3. 优化性能参数
4. 添加更多功能模块
