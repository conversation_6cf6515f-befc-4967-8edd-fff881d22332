#!/usr/bin/env python3
"""
测试完整版Web应用
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_webapp():
    """测试Web应用"""
    print("🧪 测试完整版Web应用...")
    
    # 检查服务器是否运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
            print("🌐 访问地址: http://localhost:5000")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: run_webapp_complete.bat")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_files():
    """检查必要文件"""
    print("📁 检查必要文件...")
    
    webapp_dir = Path("webapp")
    required_files = [
        webapp_dir / "app_complete.py",
        webapp_dir / "index_complete.html",
        webapp_dir / "static/js/main_complete.js",
        webapp_dir / "templates/index.html"
    ]
    
    all_exist = True
    for file_path in required_files:
        if file_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("🚗 YOLOv11车辆计数器 - 完整版测试")
    print("=" * 50)
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败")
        return
    
    print("\n✅ 文件检查通过")
    
    # 测试Web应用
    if test_webapp():
        print("\n🎉 完整版Web应用测试通过！")
        print("\n📱 界面特色:")
        print("   📤 左上角: 视频上传与设置")
        print("   🎬 中上角: 原始视频预览")
        print("   📊 右上角: 实时统计与进度")
        print("   🎯 左下角: 处理结果视频")
        print("   ⚖️  中下角: 原视频vs结果对比")
        print("   📈 右下角: 数据分析与下载")
        
        print("\n🎯 处理特色:")
        print("   🔍 真实YOLO目标检测")
        print("   🚗 车辆识别和分类")
        print("   📏 智能目标跟踪")
        print("   📊 穿越计数统计")
        print("   🎨 检测框和轨迹可视化")
        print("   📹 高质量视频输出")
    else:
        print("\n❌ Web应用测试失败")
        print("\n💡 解决方案:")
        print("   1. 运行 run_webapp_complete.bat 启动服务器")
        print("   2. 检查端口5000是否被占用")
        print("   3. 确保安装了所需依赖:")
        print("      pip install flask ultralytics opencv-python")

if __name__ == "__main__":
    main()