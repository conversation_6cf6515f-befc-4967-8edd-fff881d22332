#!/usr/bin/env python3
"""
启动YOLOv11车辆计数器Web应用
"""

import sys
import subprocess
from pathlib import Path
import webbrowser
import time

def check_dependencies():
    """检查依赖"""
    required_packages = {
        'flask': 'flask',
        'flask_cors': 'flask-cors', 
        'ultralytics': 'ultralytics',
        'cv2': 'opencv-python'
    }
    missing_packages = []
    
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚗 YOLOv11车辆计数器 - Web应用启动器")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查依赖包...")
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("✅ 依赖检查通过")
    
    # 启动Flask应用
    print("🚀 启动Web服务器...")
    try:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Flask应用
        from app import app
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()