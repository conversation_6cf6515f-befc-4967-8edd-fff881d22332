#!/bin/bash

# YOLOv11车辆计数系统部署脚本
# 正式域名: vehicle.smart-traffic.top
# 服务器: ************ (阿里云)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_NAME="vehicle-counter"
PROJECT_DIR="/var/www/$PROJECT_NAME"
DOMAIN_NAME="vehicle.smart-traffic.top"
SERVER_IP="************"
PYTHON_VERSION="python3"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示欢迎信息
show_welcome() {
    clear
    echo "=================================================="
    echo "🚗 YOLOv11智能车辆计数系统"
    echo "=================================================="
    echo "🌐 正式域名: $DOMAIN_NAME"
    echo "🖥️  服务器IP: $SERVER_IP"
    echo "📁 项目目录: $PROJECT_DIR"
    echo "🐍 Python版本: $PYTHON_VERSION"
    echo "=================================================="
    echo
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查用户权限
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查Ubuntu版本
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warning "检测到非Ubuntu系统，可能需要调整部分命令"
    fi
    
    # 检查Apache
    if ! command -v apache2 &> /dev/null; then
        log_error "Apache2未安装，请先安装Apache2"
        log_info "安装命令: sudo apt install apache2 -y"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包列表
    sudo apt update
    
    # 安装Python相关
    sudo apt install -y python3-pip python3-venv python3-dev build-essential
    
    # 安装OpenCV依赖
    sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg
    
    # 安装进程管理工具
    sudo apt install -y supervisor
    
    # 安装SSL工具
    sudo apt install -y certbot python3-certbot-apache
    
    log_success "系统依赖安装完成"
}

# 设置项目目录
setup_project() {
    log_info "设置项目目录..."
    
    # 备份现有目录
    if [ -d "$PROJECT_DIR" ]; then
        log_warning "项目目录已存在，创建备份..."
        sudo mv "$PROJECT_DIR" "${PROJECT_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 创建项目目录
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown $USER:$USER "$PROJECT_DIR"
    
    log_success "项目目录设置完成: $PROJECT_DIR"
}

# 验证项目文件
verify_project_files() {
    log_info "验证项目文件..."
    
    local required_files=("app.py" "requirements.txt" "index.html")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$PROJECT_DIR/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少必要文件: ${missing_files[*]}"
        log_error "请先上传项目文件到 $PROJECT_DIR"
        log_info "使用命令: ./upload_files.sh"
        exit 1
    fi
    
    log_success "项目文件验证通过"
}

# 设置Python环境
setup_python_env() {
    log_info "设置Python虚拟环境..."
    
    cd "$PROJECT_DIR"
    
    # 创建虚拟环境
    $PYTHON_VERSION -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装项目依赖
    pip install -r requirements.txt
    
    # 安装生产环境依赖
    pip install gunicorn
    
    log_success "Python环境设置完成"
}

# 配置Apache虚拟主机
configure_apache() {
    log_info "配置Apache虚拟主机..."
    
    # 启用必要模块
    sudo a2enmod rewrite proxy proxy_http headers ssl
    
    # 创建虚拟主机配置
    sudo tee /etc/apache2/sites-available/${PROJECT_NAME}.conf > /dev/null <<EOF
<VirtualHost *:80>
    ServerName $DOMAIN_NAME
    ServerAlias www.$DOMAIN_NAME
    
    DocumentRoot $PROJECT_DIR
    
    # 静态文件服务
    Alias /static $PROJECT_DIR/static
    <Directory $PROJECT_DIR/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    # 代理到Flask应用
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # 文件上传限制 (100MB)
    LimitRequestBody 104857600
    
    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # 日志
    ErrorLog \${APACHE_LOG_DIR}/${PROJECT_NAME}_error.log
    CustomLog \${APACHE_LOG_DIR}/${PROJECT_NAME}_access.log combined
</VirtualHost>
EOF
    
    # 启用站点
    sudo a2ensite ${PROJECT_NAME}.conf
    sudo systemctl reload apache2
    
    log_success "Apache虚拟主机配置完成"
}

# 创建应用配置
create_app_config() {
    log_info "创建应用配置..."
    
    # Gunicorn配置
    cat > "$PROJECT_DIR/gunicorn.conf.py" <<EOF
# Gunicorn生产环境配置
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF
    
    # 启动脚本
    cat > "$PROJECT_DIR/start.sh" <<EOF
#!/bin/bash
cd $PROJECT_DIR
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF
    
    chmod +x "$PROJECT_DIR/start.sh"
    
    log_success "应用配置创建完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor..."
    
    sudo tee /etc/supervisor/conf.d/${PROJECT_NAME}.conf > /dev/null <<EOF
[program:$PROJECT_NAME]
command=$PROJECT_DIR/start.sh
directory=$PROJECT_DIR
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/${PROJECT_NAME}.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="$PROJECT_DIR/venv/bin"
EOF
    
    # 重新加载配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动应用
    sudo supervisorctl start $PROJECT_NAME
    
    # 等待启动
    sleep 5
    
    # 检查状态
    if sudo supervisorctl status $PROJECT_NAME | grep -q "RUNNING"; then
        log_success "应用启动成功"
    else
        log_error "应用启动失败"
        sudo supervisorctl status $PROJECT_NAME
        sudo tail -20 /var/log/${PROJECT_NAME}.log
        exit 1
    fi
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 测试本地API
    if curl -s http://localhost:5000/api/health > /dev/null; then
        log_success "本地API测试通过"
    else
        log_warning "本地API测试失败"
    fi
    
    # 测试域名解析
    if nslookup $DOMAIN_NAME > /dev/null 2>&1; then
        log_success "域名解析正常"
    else
        log_warning "域名解析可能未生效，请稍后再试"
    fi
}

# 显示完成信息
show_completion() {
    echo
    echo "=================================================="
    echo "🎉 部署完成！"
    echo "=================================================="
    echo "🌐 网站地址: http://$DOMAIN_NAME"
    echo "📊 健康检查: http://$DOMAIN_NAME/api/health"
    echo "📁 项目目录: $PROJECT_DIR"
    echo "📝 应用日志: /var/log/${PROJECT_NAME}.log"
    echo "=================================================="
    echo
    echo "🔧 管理命令:"
    echo "重启应用: sudo supervisorctl restart $PROJECT_NAME"
    echo "查看状态: sudo supervisorctl status $PROJECT_NAME"
    echo "查看日志: sudo tail -f /var/log/${PROJECT_NAME}.log"
    echo
    echo "🔒 下一步 - 配置SSL证书:"
    echo "./ssl_setup.sh"
    echo
    echo "=================================================="
}

# 主函数
main() {
    show_welcome
    check_system
    install_dependencies
    setup_project
    verify_project_files
    setup_python_env
    configure_apache
    create_app_config
    configure_supervisor
    start_services
    test_deployment
    show_completion
}

# 运行主函数
main "$@"
