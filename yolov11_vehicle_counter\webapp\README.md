# 🚗 YOLOv11智能车辆计数系统 - Web应用

## 📋 项目简介

这是一个基于YOLOv11深度学习模型的智能车辆计数Web应用，具有现代化的用户界面和强大的视频处理功能。

### ✨ 主要特性

- 🎯 **智能检测**: 基于YOLOv11的高精度车辆和人员检测
- 📊 **实时统计**: 车辆穿越计数和人员总数统计
- 🎨 **现代界面**: 响应式HTML/CSS/JavaScript界面
- 📱 **移动友好**: 支持各种设备和屏幕尺寸
- 🎥 **视频处理**: 支持多种视频格式，实时处理进度
- 📈 **数据可视化**: 时间线图表、热力图、详细数据表
- 💾 **结果下载**: 处理后视频、分析报告、原始数据下载

## 🛠️ 技术栈

### 前端
- **HTML5**: 现代化的页面结构
- **CSS3**: 渐变背景、动画效果、响应式设计
- **JavaScript**: 交互逻辑、文件上传、实时更新
- **Chart.js**: 数据可视化图表
- **Font Awesome**: 图标库

### 后端
- **Python**: 核心处理逻辑
- **Flask**: Web框架和API服务
- **YOLOv11**: 目标检测模型
- **OpenCV**: 视频处理
- **NumPy**: 数值计算

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- 支持的操作系统: Windows, macOS, Linux

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt
```

### 3. 启动应用

```bash
# 方法1: 使用批处理文件 (Windows推荐)
start_webapp.bat

# 方法2: 使用Python启动器
python start_webapp.py

# 方法3: 直接启动Flask应用
python app.py
```

### 4. 访问应用

打开浏览器访问: http://localhost:5000

## 📖 使用指南

### 1. 上传视频

- 点击"上传视频文件"或拖拽视频到上传区域
- 支持格式: MP4, AVI, MOV, MKV
- 建议分辨率: 1080p

### 2. 配置设置

- **检测模型**: 选择YOLOv11模型版本
- **置信度阈值**: 调整检测敏感度 (0.1-0.9)
- **处理速度**: 选择处理精度和速度平衡

### 3. 开始处理

- 点击"开始处理"按钮
- 实时查看处理进度和统计信息
- 支持暂停/继续/停止操作

### 4. 查看结果

- **统计卡片**: 车辆总数、左右通过数量、人员数量
- **视频播放**: 处理后的标注视频
- **数据分析**: 时间线图表、热力图、详细数据表
- **下载选项**: 视频文件、分析报告、原始数据

## 🎨 界面预览

### 主页
- 渐变背景设计
- 功能特点展示
- 技术栈介绍

### 上传页面
- 拖拽上传区域
- 文件信息显示
- 处理参数配置

### 处理页面
- 圆形进度指示器
- 实时统计信息
- 处理日志显示

### 结果页面
- 统计数据卡片
- 视频播放器
- 交互式图表
- 下载功能

## 🔧 API接口

### 上传视频
```
POST /api/upload
Content-Type: multipart/form-data
```

### 开始处理
```
POST /api/process
Content-Type: application/json
{
  "task_id": "uuid",
  "settings": {
    "model": "yolov11m",
    "confidence": 0.5,
    "speed": "normal"
  }
}
```

### 获取状态
```
GET /api/status/<task_id>
```

### 获取结果
```
GET /api/results/<task_id>
```

### 下载视频
```
GET /api/download/<task_id>
```

## 📁 项目结构

```
webapp/
├── index.html              # 主页面
├── static/
│   ├── css/
│   │   └── style.css       # 样式文件
│   └── js/
│       └── main.js         # JavaScript逻辑
├── app.py                  # Flask后端应用
├── start_webapp.py         # 启动脚本
├── start_webapp.bat        # Windows批处理启动
├── requirements.txt        # Python依赖
└── README.md              # 项目文档
```

## 🎯 核心功能

### 车辆检测与计数
- 支持多种车辆类型: 汽车、卡车、公交车、摩托车、自行车
- 基于轨迹的穿越检测，避免重复计数
- 左右方向分别统计

### 人员检测
- 按唯一ID统计人员总数
- 避免重复计数同一人员

### 实时处理
- WebSocket实时状态更新
- 处理进度可视化
- 支持暂停/继续操作

### 数据可视化
- Chart.js时间线图表
- Canvas热力图显示
- 详细数据表格

## 🔒 安全特性

- 文件类型验证
- 临时文件自动清理
- 错误处理和恢复
- CORS跨域保护

## 🚀 部署指南

### 本地部署
1. 克隆项目到本地
2. 安装依赖: `pip install -r requirements.txt`
3. 运行: `python start_webapp.py`

### 云服务器部署
1. 上传项目文件到服务器
2. 安装依赖和配置环境
3. 使用Gunicorn或uWSGI部署
4. 配置Nginx反向代理
5. 设置SSL证书

### Docker部署
```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径
   - 确认模型文件完整性

2. **视频处理慢**
   - 降低视频分辨率
   - 调整处理速度设置

3. **内存不足**
   - 减少批处理大小
   - 使用更小的模型

4. **端口被占用**
   - 修改app.py中的端口号
   - 或终止占用端口的进程

## 📞 技术支持

如遇问题，请检查:
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 模型文件是否存在
4. 视频文件格式是否支持

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🙏 致谢

- YOLOv11团队提供的优秀检测模型
- OpenCV社区的视频处理支持
- Flask框架的Web开发便利
- Chart.js提供的数据可视化功能

---

**享受智能车辆计数的便利！** 🎉