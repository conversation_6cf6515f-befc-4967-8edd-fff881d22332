# WinSCP连接配置指南

## 正确的连接参数

### 基本设置
```
文件协议: SFTP
主机名: ************
端口号: 22
用户名: admin
密码: [你的服务器密码]
```

### 详细配置步骤

1. **打开WinSCP**，点击"新建站点"

2. **填写连接信息**：
   - **文件协议**: 选择 `SFTP`
   - **主机名**: `************`
   - **端口号**: `22` (默认)
   - **用户名**: `admin`
   - **密码**: 输入你的服务器密码

3. **高级设置** (可选)：
   - 点击"高级"按钮
   - 在"连接"选项卡中，设置"连接超时"为 `30` 秒

4. **保存站点**：
   - 点击"保存"
   - 给站点起个名字，如"阿里云服务器"

5. **测试连接**：
   - 点击"登录"按钮
   - 如果提示"服务器主机密钥未知"，点击"是"接受

## 常见连接问题及解决方案

### 问题1: "Connection failed"
**可能原因**:
- 服务器IP地址错误
- 端口被防火墙阻止
- 网络连接问题

**解决方案**:
```bash
# 在本地命令行测试连接
ping ************
telnet ************ 22
```

### 问题2: "Authentication failed"
**可能原因**:
- 用户名或密码错误
- 服务器禁用了密码认证

**解决方案**:
1. 确认用户名是 `admin`
2. 确认密码正确
3. 如果还是不行，可能需要使用SSH密钥认证

### 问题3: "Host key verification failed"
**解决方案**:
- 点击"是"接受服务器密钥
- 或者在高级设置中禁用主机密钥检查

## 连接成功后的操作

### 上传文件步骤：
1. **左侧窗口**: 导航到 `D:\lkr_yolo\`
2. **右侧窗口**: 确保在 `/home/<USER>/` 目录
3. **拖拽上传**: 将 `one_click_deploy.sh` 从左侧拖到右侧

### 验证上传成功：
- 在右侧窗口应该能看到 `one_click_deploy.sh` 文件
- 文件大小应该显示为几KB

## 替代连接方法

### 方法1: 使用SSH命令测试
```cmd
# 在Windows命令行中测试
ssh admin@************
```

### 方法2: 使用其他SFTP客户端
- **FileZilla**: https://filezilla-project.org/
- **MobaXterm**: https://mobaxterm.mobatek.net/

## 如果WinSCP仍然无法连接

### 检查服务器状态：
```bash
# 检查SSH服务是否运行
systemctl status ssh

# 检查防火墙设置
ufw status

# 检查SSH配置
cat /etc/ssh/sshd_config | grep -E "(PasswordAuthentication|PubkeyAuthentication)"
```

### 联系服务器管理员：
如果以上方法都不行，可能需要：
1. 确认服务器SSH服务正常运行
2. 确认防火墙允许SSH连接
3. 确认用户账户和密码正确

## 成功连接的标志

连接成功后，你应该看到：
- 右侧窗口显示服务器的文件列表
- 状态栏显示"已连接"
- 可以在右侧窗口中看到类似 `/home/<USER>/` 的路径
