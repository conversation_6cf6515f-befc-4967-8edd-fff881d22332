# 🚗 YOLOv11车辆计数器 - Web部署指南

## 📋 项目概述

这是一个基于YOLOv11的智能车辆计数系统，具有美观的Web界面，支持本地测试和云服务器部署。

### ✨ 主要功能
- 🎯 智能车辆检测和跟踪
- 📊 实时计数统计（车辆穿越 + 人员总数）
- 🎨 1080p优化的美观界面
- 🆔 清洁ID管理系统
- 🌐 Web端随时访问

## 🛠️ 本地测试部署

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements_web.txt

# 或者手动安装主要依赖
pip install gradio ultralytics opencv-python numpy torch torchvision
```

### 2. 启动本地服务

```bash
# 方法1: 使用批处理文件（Windows推荐）
run_webui.bat

# 方法2: 直接运行Python脚本
python run_webui.py

# 方法3: 直接运行Web界面
python src/webui_modern.py
```

### 3. 访问界面

- 本地访问: http://localhost:7860
- 局域网访问: http://你的IP:7860

## ☁️ 云服务器部署

### 1. 服务器环境配置

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip -y

# 安装系统依赖
sudo apt install libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1 -y
```

### 2. 上传项目文件

```bash
# 使用scp上传项目文件
scp -r yolov11_vehicle_counter/ user@server_ip:/home/<USER>/

# 或者使用git克隆
git clone your_repository_url
cd yolov11_vehicle_counter
```

### 3. 安装依赖

```bash
# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements_web.txt
```

### 4. 配置防火墙

```bash
# Ubuntu/Debian
sudo ufw allow 7860

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=7860/tcp
sudo firewall-cmd --reload
```

### 5. 启动服务

```bash
# 前台运行（测试用）
python run_webui.py

# 后台运行（生产环境）
nohup python run_webui.py > webui.log 2>&1 &

# 使用screen（推荐）
screen -S vehicle_counter
python run_webui.py
# Ctrl+A+D 退出screen
```

### 6. 配置域名和SSL（可选）

使用Nginx反向代理：

```nginx
server {
    listen 80;
    server_name your_domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:7860;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔧 配置说明

### 模型路径配置

在 `src/webui_modern.py` 中修改模型路径：

```python
model_paths = {
    "YOLOv11m (推荐)": "/path/to/your/model.pt",
    "YOLOv11m 备用": "/path/to/backup/model.pt",
    "YOLOv11m 预训练": "yolov11m.pt"
}
```

### 服务器配置

修改启动参数：

```python
demo.launch(
    server_name="0.0.0.0",  # 允许外部访问
    server_port=7860,       # 端口号
    share=True,             # 生成公共链接（Gradio提供）
    debug=False,            # 生产环境关闭调试
    show_error=False        # 生产环境隐藏错误
)
```

## 📊 性能优化

### 1. GPU加速

确保安装了CUDA版本的PyTorch：

```bash
# 检查CUDA版本
nvidia-smi

# 安装对应的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2. 内存优化

```python
# 在webui_modern.py中添加
import torch
torch.cuda.empty_cache()  # 清理GPU内存
```

### 3. 并发处理

```python
# 启动时增加队列大小
demo.queue(concurrency_count=2).launch()
```

## 🔒 安全配置

### 1. 访问控制

```python
# 添加用户认证
demo.launch(auth=("username", "password"))
```

### 2. 文件上传限制

```python
# 限制上传文件大小和类型
video_input = gr.Video(
    label="上传视频文件",
    sources=["upload"],
    height=300,
    file_types=[".mp4", ".avi", ".mov"]  # 限制文件类型
)
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :7860
   # 杀死进程
   kill -9 PID
   ```

2. **模型加载失败**
   - 检查模型文件路径
   - 确认模型文件完整性
   - 检查磁盘空间

3. **视频处理慢**
   - 使用GPU加速
   - 降低视频分辨率
   - 优化模型参数

4. **内存不足**
   - 增加系统内存
   - 使用更小的模型
   - 批处理优化

## 📝 维护建议

### 1. 日志管理

```bash
# 设置日志轮转
sudo apt install logrotate
# 配置日志轮转规则
```

### 2. 定期备份

```bash
# 备份重要文件
tar -czf backup_$(date +%Y%m%d).tar.gz yolov11_vehicle_counter/
```

### 3. 监控服务

```bash
# 使用systemd管理服务
sudo systemctl status vehicle_counter
```

## 🚀 扩展功能

### 1. 数据库集成
- 保存处理结果到数据库
- 历史记录查询
- 统计报表生成

### 2. API接口
- RESTful API
- 批量处理接口
- 实时流处理

### 3. 多语言支持
- 国际化界面
- 多语言文档
- 本地化配置

---

## 📞 技术支持

如有问题，请检查：
1. 依赖是否正确安装
2. 模型文件是否存在
3. 端口是否被占用
4. 防火墙设置是否正确

祝您部署成功！🎉