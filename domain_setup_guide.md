# 域名配置指南

## 当前域名配置

### 正式域名 ✅
- **域名**: `smart-traffic.top` (已通过审核)
- **子域名**: `vehicle.smart-traffic.top` (车辆计数系统)
- **服务器IP**: `************` (阿里云服务器)

### 备用域名
- **域名**: `lkr666.online` (备用)
- **子域名**: `vehicle.lkr666.online`

## DNS配置步骤

### 1. 服务器公网IP
```
服务器IP: ************ (已确认)
```

### 2. 配置DNS解析记录

在你的域名管理面板中添加以下记录：

#### A记录配置
```
记录类型: A
主机记录: vehicle
记录值: ************
TTL: 600 (10分钟)
```

#### 可选的CNAME记录
```
记录类型: CNAME  
主机记录: www.vehicle
记录值: vehicle.lkr666.online
TTL: 600
```

### 3. 验证DNS解析
```bash
# 检查DNS解析
nslookup vehicle.lkr666.online
# 或者
dig vehicle.lkr666.online

# 检查解析是否生效
ping vehicle.lkr666.online
```

## 多网站架构规划

### 当前服务器网站布局
```
你的阿里云服务器 (************)
├── 现有网站 (Apache默认站点)
├── 车辆计数系统 (vehicle.lkr666.online)
└── 未来扩展...
```

### 推荐的域名结构
```
主域名: lkr666.online
├── www.lkr666.online (主网站)
├── vehicle.lkr666.online (车辆计数系统)  
├── api.lkr666.online (API服务)
└── admin.lkr666.online (管理后台)
```

## Apache虚拟主机配置

### 当前配置文件位置
- 车辆计数系统: `/etc/apache2/sites-available/vehicle-counter.conf`
- 现有网站: `/etc/apache2/sites-available/000-default.conf`

### 查看当前站点状态
```bash
# 查看启用的站点
sudo a2ensite -l

# 查看Apache状态
sudo systemctl status apache2

# 查看虚拟主机配置
sudo apache2ctl -S
```

## SSL证书配置

### 使用Let's Encrypt免费证书
```bash
# 运行SSL配置脚本
./ssl_setup.sh

# 手动配置 (如果脚本失败)
sudo certbot --apache -d vehicle.lkr666.online
```

### 证书管理命令
```bash
# 查看证书状态
sudo certbot certificates

# 测试自动续期
sudo certbot renew --dry-run

# 手动续期
sudo certbot renew
```

## 防火墙配置

### 开放必要端口
```bash
# 查看当前防火墙状态
sudo ufw status

# 开放HTTP和HTTPS端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 重新加载防火墙
sudo ufw reload
```

## 测试部署

### 1. 本地测试
```bash
# 测试Flask应用
curl http://localhost:5000/api/health

# 测试Apache代理
curl http://localhost/api/health
```

### 2. 域名测试
```bash
# 测试域名访问
curl http://vehicle.lkr666.online/api/health

# 测试HTTPS (配置SSL后)
curl https://vehicle.lkr666.online/api/health
```

### 3. 浏览器测试
- 访问: `http://vehicle.lkr666.online`
- 检查页面加载是否正常
- 测试文件上传功能
- 验证视频处理功能

## 监控和日志

### 应用日志
```bash
# 查看应用日志
sudo tail -f /var/log/vehicle-counter.log

# 查看Supervisor状态
sudo supervisorctl status

# 重启应用
sudo supervisorctl restart vehicle-counter
```

### Apache日志
```bash
# 查看访问日志
sudo tail -f /var/log/apache2/vehicle-counter_access.log

# 查看错误日志  
sudo tail -f /var/log/apache2/vehicle-counter_error.log
```

## 性能优化建议

### 1. 静态文件缓存
已在Apache配置中启用静态文件缓存

### 2. 文件上传限制
- Apache: 100MB (已配置)
- 可根据需要调整

### 3. 进程管理
- Gunicorn workers: 2个 (可根据CPU核心数调整)
- 内存使用监控

## 故障排除

### 常见问题
1. **域名无法访问**
   - 检查DNS解析是否生效
   - 确认防火墙端口开放
   - 验证Apache配置

2. **应用启动失败**
   - 查看应用日志
   - 检查Python依赖
   - 验证文件权限

3. **上传功能异常**
   - 检查文件大小限制
   - 确认临时目录权限
   - 验证磁盘空间

### 调试命令
```bash
# 检查端口占用
sudo netstat -tlnp | grep :5000
sudo netstat -tlnp | grep :80

# 检查进程状态
ps aux | grep gunicorn
ps aux | grep apache2

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 备份策略

### 定期备份
```bash
# 备份项目文件
sudo tar -czf /backup/vehicle-counter-$(date +%Y%m%d).tar.gz /var/www/vehicle-counter

# 备份Apache配置
sudo cp /etc/apache2/sites-available/vehicle-counter.conf /backup/

# 备份Supervisor配置
sudo cp /etc/supervisor/conf.d/vehicle-counter.conf /backup/
```

## 下一步计划

1. **完成测试部署** - 使用 `vehicle.lkr666.online`
2. **功能测试** - 验证所有功能正常
3. **性能优化** - 根据使用情况调整配置
4. **SSL配置** - 启用HTTPS
5. **域名迁移** - `smart-traffic.top` 审核通过后迁移
