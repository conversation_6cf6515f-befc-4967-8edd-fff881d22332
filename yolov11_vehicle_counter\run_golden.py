#!/usr/bin/env python3
"""
运行黄金版本的YOLOv11车辆计数器
基于真正有效的代码
"""

import os
import sys
from pathlib import Path
import subprocess

def main():
    print("🏆 YOLOv11车辆计数器 - 黄金版本")
    print("=" * 50)
    print("✨ 基于真正有效的main_upgraded.py")
    print("🎯 使用YOLO内置跟踪，简单直接")
    print()
    
    # 检查文件
    model_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    video_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    output_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/output_golden.mp4')
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        input("按回车键退出...")
        return
        
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        input("按回车键退出...")
        return
    
    print("✅ 文件检查通过")
    print(f"🤖 模型: {model_path}")
    print(f"🎥 视频: {video_path}")
    print(f"💾 输出: {output_path}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 构建命令
    cmd = [
        sys.executable, 'main_golden.py',
        '--model', str(model_path),
        '--source', str(video_path),
        '--save-path', str(output_path),
        '--no-show'  # 不显示窗口，避免GUI问题
    ]
    
    print("🔄 开始处理...")
    print("💡 关键特性:")
    print("   • 使用YOLO内置跟踪 (model.track)")
    print("   • 简单直接的计数逻辑")
    print("   • 最佳的可视化效果 (results.plot)")
    print("   • 无复杂错误处理")
    print()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True)
        print()
        print("✅ 处理完成！")
        print(f"📁 输出视频: {output_path}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
        print("💡 即使中断，部分视频可能已经处理完成")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()