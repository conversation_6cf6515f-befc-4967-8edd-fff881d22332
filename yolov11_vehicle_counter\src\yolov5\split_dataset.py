import os
import shutil

# UltraThink分割参数
IMG_DIR = r'D:/lkr_yolo/yolov11_vehicle_counter/dataset/images'
LABEL_DIR = r'D:/lkr_yolo/yolov11_vehicle_counter/dataset/label'
OUT_ROOT = r'D:/lkr_yolo/yolov11_vehicle_counter/splitted_dataset'
IMG_OUT = os.path.join(OUT_ROOT, 'images')
LABEL_OUT = os.path.join(OUT_ROOT, 'labels')

# 交错分配比例
SPLIT_PATTERN = ['train']*7 + ['val']*2 + ['test']

img_files = sorted([f for f in os.listdir(IMG_DIR) if f.lower().endswith('.jpg')])

for idx, img_name in enumerate(img_files):
    split = SPLIT_PATTERN[idx % len(SPLIT_PATTERN)]
    label_name = img_name.replace('.jpg', '.txt')
    # 目标路径
    img_dst = os.path.join(IMG_OUT, split, img_name)
    label_dst = os.path.join(LABEL_OUT, split, label_name)
    # 源路径
    img_src = os.path.join(IMG_DIR, img_name)
    label_src = os.path.join(LABEL_DIR, label_name)
    # 复制图片
    shutil.copy2(img_src, img_dst)
    # 复制标签（如标签不存在则跳过）
    if os.path.exists(label_src):
        shutil.copy2(label_src, label_dst)
    else:
        print(f'警告: 未找到标签 {label_src}')

print('UltraThink分割完成！')