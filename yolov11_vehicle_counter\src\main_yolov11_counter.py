#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 基于webui.py改写
包含完整的撞线计数功能
"""

# 设置环境变量，限制线程数为1，python的并行并不是真正的并行，因为GIL锁
import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"

import copy
import sys
import argparse
import shutil
import time
from pathlib import Path
import numpy as np
import cv2
import torch
import torch.backends.cudnn as cudnn

# YOLOv11导入
from ultralytics import YOLO

# DeepSORT导入
from tracker.utils.parser import get_config
from tracker.deep_sort import DeepSort

# 全局计数变量 - 与原版webui.py完全相同
count = 0
count2 = 0
data = []

def count_obj(box, w, h, id):
    """
    撞线计数函数 - 与原版webui.py完全相同
    检测车辆是否穿过计数线
    """
    global count, count2, data
    center_coor = (int(box[0] + (box[2] - box[0]) / 2), int(box[1] + (box[3] - box[1]) / 2))
    
    # 撞线检测：当车辆中心点的Y坐标超过 h-350 且该ID未被计数过
    if int(box[1] + (box[3] - box[1]) / 2) > h - 350 and id not in data:
        # 根据X坐标判断是左侧还是右侧
        if int(box[0] + (box[2] - box[0]) / 2) > int(w / 2):
            count2 += 1  # 右侧计数
        else:
            count += 1   # 左侧计数
        data.append(id)  # 记录已计数的ID，避免重复计数

def detect(opt):
    """
    主检测函数 - 基于webui.py改写为YOLOv11版本
    """
    global count, count2, data
    
    # 重置全局计数变量
    count = 0
    count2 = 0
    data = []
    
    # 解析参数 - 与原版webui.py相同的参数结构
    out, source, yolo_model, deep_sort_model, show_vid, save_vid, save_txt, imgsz, evaluate, half, \
        project, exist_ok, update, save_crop = \
        opt.output, opt.source, opt.yolo_model, opt.deep_sort_model, opt.show_vid, opt.save_vid, \
        opt.save_txt, opt.imgsz, opt.evaluate, opt.half, opt.project, opt.exist_ok, opt.update, opt.save_crop

    # 设备选择 - 适配YOLOv11
    if opt.device == '':
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(f'cuda:{opt.device}' if opt.device.isdigit() else opt.device)
    
    print(f"使用设备: {device}")

    # 输出目录管理 - 与原版相同
    if not evaluate:
        if os.path.exists(out):
            shutil.rmtree(out)
        os.makedirs(out)

    # 目录设置 - 与原版webui.py相同的逻辑
    if type(yolo_model) is str:
        exp_name = Path(yolo_model).stem
    elif type(yolo_model) is list and len(yolo_model) == 1:
        exp_name = Path(yolo_model[0]).stem
    else:
        exp_name = "ensemble"
    
    exp_name = exp_name + "_" + Path(deep_sort_model).stem
    save_dir = Path(project) / exp_name
    save_dir.mkdir(parents=True, exist_ok=exist_ok)
    
    if save_txt:
        (save_dir / 'tracks').mkdir(exist_ok=True)

    # 加载YOLOv11模型 - 替换原版的YOLOv5
    print(f"加载YOLOv11模型: {yolo_model}")
    model = YOLO(yolo_model)
    names = model.names
    print(f"检测类别: {names}")

    # 半精度设置 - 适配YOLOv11
    if half and device.type != 'cpu':
        model.model.half()
        print("启用FP16半精度推理")

    # 初始化DeepSORT - 与原版webui.py完全相同
    print(f"加载DeepSORT模型: {deep_sort_model}")
    cfg = get_config()
    cfg.merge_from_file(opt.config_deepsort)
    
    deepsort = DeepSort(
        deep_sort_model,
        device,
        max_dist=cfg.DEEPSORT.MAX_DIST,
        max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
        max_age=cfg.DEEPSORT.MAX_AGE,
        n_init=cfg.DEEPSORT.N_INIT,
        nn_budget=cfg.DEEPSORT.NN_BUDGET,
    )

    # 视频处理设置
    print(f"打开视频源: {source}")
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {source}")
        return

    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {w}x{h} @ {fps}fps, 总帧数: {total_frames}")

    # 视频写入器设置 - 与原版webui.py相同
    vid_writer = None
    save_path = None
    
    if save_vid:
        save_path = str(save_dir / f"{Path(source).stem}.mp4")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        vid_writer = cv2.VideoWriter(save_path, fourcc, fps, (w, h))
        print(f"输出视频将保存到: {save_path}")

    # 性能计时 - 与原版webui.py相同
    dt = [0.0, 0.0, 0.0, 0.0]
    seen = 0
    frame_idx = 0
    
    print("开始处理视频帧...")
    print("=" * 60)

    # 主处理循环 - 基于webui.py的逻辑
    while True:
        ret, im0 = cap.read()
        if not ret:
            print("视频处理完成")
            break
            
        seen += 1
        frame_idx += 1
        
        # 预处理计时
        t1 = time.time()
        
        # YOLOv11推理 - 替换原版的YOLOv5推理
        results = model.predict(
            im0,
            conf=opt.conf_thres,
            iou=opt.iou_thres,
            classes=opt.classes,
            max_det=opt.max_det,
            augment=opt.augment,
            verbose=False
        )
        
        t2 = time.time()
        dt[1] += t2 - t1

        # 处理检测结果 - 适配YOLOv11的结果格式
        if results and len(results) > 0:
            result = results[0]
            if result.boxes is not None and len(result.boxes) > 0:
                # 获取检测框数据
                det = result.boxes.data.cpu().numpy()
                
                if len(det) > 0:
                    # 打印检测结果 - 与原版webui.py相同的逻辑
                    for c in np.unique(det[:, 5]):
                        n = (det[:, 5] == c).sum()
                        class_name = names[int(c)]
                        print(f"帧 {frame_idx}: 检测到 {n} 个 {class_name}")

                    # 转换坐标格式为DeepSORT需要的xywh格式
                    xywhs = det[:, :4].copy()
                    # xyxy转xywh
                    xywhs[:, 2] = xywhs[:, 2] - xywhs[:, 0]  # width
                    xywhs[:, 3] = xywhs[:, 3] - xywhs[:, 1]  # height
                    xywhs[:, 0] = xywhs[:, 0] + xywhs[:, 2] / 2  # center x
                    xywhs[:, 1] = xywhs[:, 1] + xywhs[:, 3] / 2  # center y
                    
                    confs = det[:, 4]
                    clss = det[:, 5]

                    # DeepSORT跟踪 - 与原版webui.py完全相同
                    t3 = time.time()
                    try:
                        outputs = deepsort.update(xywhs, confs, clss, im0)
                        t4 = time.time()
                        dt[3] += t4 - t3

                        # 处理跟踪结果 - 与原版webui.py完全相同的逻辑
                        if len(outputs) > 0:
                            for j, (output, conf) in enumerate(zip(outputs, confs)):
                                bboxes = output[0:4]
                                track_id = int(output[4])
                                cls_id = int(output[5])
                                
                                # 撞线计数 - 与原版webui.py完全相同
                                count_obj(bboxes, w, h, track_id)

                                # 保存跟踪结果到txt文件 - 与原版相同
                                if save_txt:
                                    txt_path = save_dir / 'tracks' / f"{Path(source).stem}.txt"
                                    bbox_left = output[0]
                                    bbox_top = output[1]
                                    bbox_w = output[2] - output[0]
                                    bbox_h = output[3] - output[1]
                                    with open(txt_path, 'a') as f:
                                        f.write(('%g ' * 10 + '\n') % (frame_idx, track_id, bbox_left,
                                                                       bbox_top, bbox_w, bbox_h, -1, -1, -1, 0))

                                # 绘制边界框和标签 - 与原版相同的可视化
                                if save_vid or show_vid:
                                    x1, y1, x2, y2 = map(int, bboxes)
                                    class_name = names[cls_id]
                                    label = f'{track_id} {class_name} {conf:.2f}'
                                    
                                    # 绘制边界框
                                    cv2.rectangle(im0, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                    cv2.putText(im0, label, (x1, y1-10), 
                                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                        print(f'帧 {frame_idx} 完成. YOLO: {(t2-t1)*1000:.1f}ms, DeepSort: {(t4-t3)*1000:.1f}ms')
                        
                    except Exception as e:
                        print(f"帧 {frame_idx} DeepSORT错误: {e}")
                        deepsort.increment_ages()
                else:
                    deepsort.increment_ages()
                    print(f'帧 {frame_idx}: 无检测结果')
            else:
                deepsort.increment_ages()
                print(f'帧 {frame_idx}: 无检测结果')
        else:
            deepsort.increment_ages()
            print(f'帧 {frame_idx}: 无检测结果')

        # 绘制计数线和计数显示 - 与原版webui.py完全相同
        if save_vid or show_vid:
            # 左侧计数线（绿色）
            color = (0, 255, 0)
            start_point = (0, h - 350)
            end_point = (int(w / 2) - 50, h - 350)
            cv2.line(im0, start_point, end_point, color, thickness=2)
            
            # 左侧计数显示
            org = (150, 150)
            font = cv2.FONT_HERSHEY_COMPLEX
            fontScale = 3
            thickness = 3
            cv2.putText(im0, str(count), org, font, fontScale, color, thickness, cv2.LINE_AA)

            # 右侧计数线（红色）
            color = (255, 0, 0)
            start_point = (int(w / 2) + 50, h - 350)
            end_point = (w, h - 350)
            cv2.line(im0, start_point, end_point, color, thickness=2)
            
            # 右侧计数显示
            org = (w - 150, 150)
            cv2.putText(im0, str(count2), org, font, fontScale, color, thickness, cv2.LINE_AA)

        # 显示视频 - 与原版相同
        if show_vid:
            cv2.imshow('YOLOv11 Vehicle Counter', im0)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

        # 保存视频 - 与原版相同
        if save_vid and vid_writer:
            vid_writer.write(im0)

        # 进度显示
        if frame_idx % 100 == 0:
            progress = (frame_idx / total_frames) * 100 if total_frames > 0 else 0
            print(f"处理进度: {progress:.1f}% ({frame_idx}/{total_frames})")
            print(f"当前计数 - 左侧: {count}, 右侧: {count2}, 总计: {count + count2}")
            print("-" * 60)

    # 清理资源
    cap.release()
    if vid_writer:
        vid_writer.release()
    cv2.destroyAllWindows()

    # 性能统计 - 与原版webui.py相同
    t = tuple(x / seen * 1E3 for x in dt)
    print(f'处理速度: 预处理 {t[0]:.1f}ms, 推理 {t[1]:.1f}ms, NMS {t[2]:.1f}ms, DeepSort {t[3]:.1f}ms')
    
    # 最终结果显示
    print("=" * 60)
    print("🎉 视频处理完成！")
    print("=" * 60)
    print(f"📊 最终计数结果:")
    print(f"   左侧通过: {count} 辆")
    print(f"   右侧通过: {count2} 辆")
    print(f"   总计通过: {count + count2} 辆")
    print(f"   处理帧数: {frame_idx}")
    
    if save_vid and save_path:
        print(f"💾 输出视频: {save_path}")
    
    if save_txt:
        tracks_saved = len(list((save_dir / 'tracks').glob('*.txt'))) if (save_dir / 'tracks').exists() else 0
        print(f"📝 跟踪文件: {tracks_saved} 个文件保存到 {save_dir / 'tracks'}")
    
    print("=" * 60)

if __name__ == '__main__':
    # 参数解析 - 与原版webui.py相同，但适配YOLOv11
    parser = argparse.ArgumentParser()
    parser.add_argument('--yolo_model', type=str, 
                       default='D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt',
                       help='YOLOv11 model path')
    parser.add_argument('--deep_sort_model', type=str, 
                       default='tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
                       help='DeepSORT model path')
    parser.add_argument('--source', type=str, 
                       default='D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4',
                       help='video source')
    parser.add_argument('--output', type=str, default='inference/output', help='output folder')
    parser.add_argument('--imgsz', type=int, default=640, help='inference size')
    parser.add_argument('--conf-thres', type=float, default=0.6, help='confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.7, help='IOU threshold for NMS')
    parser.add_argument('--max-det', type=int, default=100, help='maximum detections per image')
    parser.add_argument('--device', default='0', help='cuda device, i.e. 0 or cpu')
    parser.add_argument('--show-vid', action='store_true', help='display video results')
    parser.add_argument('--save-vid', action='store_true', default=True, help='save video results')
    parser.add_argument('--save-txt', action='store_true', help='save results to *.txt')
    parser.add_argument('--classes', nargs='+', type=int, help='filter by class: --class 0, or --class 16 17')
    parser.add_argument('--agnostic-nms', action='store_true', help='class-agnostic NMS')
    parser.add_argument('--augment', action='store_true', help='augmented inference')
    parser.add_argument('--update', action='store_true', help='update all models')
    parser.add_argument('--evaluate', action='store_true', help='evaluate mode')
    parser.add_argument("--config_deepsort", type=str, default="tracker/configs/deep_sort.yaml")
    parser.add_argument("--half", action="store_true", help="use FP16 half-precision inference")
    parser.add_argument('--visualize', action='store_true', help='visualize features')
    parser.add_argument('--save-crop', action='store_true', help='save cropped prediction boxes')
    parser.add_argument('--dnn', action='store_true', help='use OpenCV DNN for ONNX inference')
    parser.add_argument('--project', default='runs/track', help='save results to project/name')
    parser.add_argument('--name', default='exp', help='save results to project/name')
    parser.add_argument('--exist-ok', action='store_true', help='existing project/name ok, do not increment')
    
    opt = parser.parse_args()
    
    # 启动信息
    print("🚗 YOLOv11车辆计数器 - 撞线计数版本")
    print("基于备份文件夹webui.py改写，保持原有撞线计数逻辑")
    print("=" * 60)
    print(f"🤖 YOLO模型: {opt.yolo_model}")
    print(f"🔍 DeepSORT模型: {opt.deep_sort_model}")
    print(f"🎥 视频源: {opt.source}")
    print(f"⚙️  置信度阈值: {opt.conf_thres}")
    print(f"⚙️  IOU阈值: {opt.iou_thres}")
    print(f"⚙️  最大检测数: {opt.max_det}")
    print(f"💻 设备: {opt.device}")
    print(f"💾 保存视频: {opt.save_vid}")
    print(f"👁️  显示视频: {opt.show_vid}")
    print("=" * 60)
    
    with torch.no_grad():
        detect(opt)