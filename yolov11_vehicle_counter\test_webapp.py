#!/usr/bin/env python3
"""
测试YOLOv11车辆计数器Web应用
"""

import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_webapp_dependencies():
    """检查Web应用依赖"""
    print("🔍 检查Web应用依赖...")
    
    required_packages = {
        'flask': 'Flask',
        'flask_cors': 'Flask-CORS', 
        'ultralytics': 'ultralytics',
        'cv2': 'opencv-python'
    }
    
    missing_packages = []
    
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name} 未安装")
    
    if missing_packages:
        print("\n💡 安装缺少的依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_model_files():
    """检查模型文件"""
    print("\n🤖 检查模型文件...")
    
    model_paths = [
        "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
        "runs/train/yolov11m_vehicle_detection/weights/best.pt"
    ]
    
    found_models = []
    for model_path in model_paths:
        full_path = Path(model_path)
        if full_path.exists():
            print(f"✅ 找到模型: {model_path}")
            found_models.append(str(full_path))
        else:
            print(f"❌ 模型不存在: {model_path}")
    
    if not found_models:
        print("⚠️  没有找到自定义模型，将使用预训练模型 yolov11m.pt")
    
    return len(found_models) > 0

def test_webapp():
    """测试Web应用"""
    print("🚗 YOLOv11车辆计数器 - Web应用测试")
    print("=" * 50)
    
    # 检查依赖
    if not check_webapp_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺少的包")
        return False
    
    # 检查模型
    has_models = check_model_files()
    
    # 检查webapp文件
    webapp_dir = Path("webapp")
    if not webapp_dir.exists():
        print(f"❌ webapp目录不存在: {webapp_dir}")
        return False
    
    required_files = [
        "webapp/app.py",
        "webapp/index.html",
        "webapp/static/js/main.js"
    ]
    
    print("\n📁 检查Web应用文件...")
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    print("\n🚀 启动Web应用...")
    print("📱 功能特性:")
    print("   - 视频上传和处理")
    print("   - 实时进度显示")
    print("   - 美观的结果可视化")
    print("   - 数据分析和下载")
    print("   - 响应式界面设计")
    
    print("\n🌐 访问地址: http://localhost:5000")
    print("💡 使用说明:")
    print("   1. 上传视频文件（支持MP4、AVI等格式）")
    print("   2. 配置处理参数（模型、置信度、速度）")
    print("   3. 点击开始处理")
    print("   4. 查看实时进度和统计")
    print("   5. 下载处理结果")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5000')
            print("🌐 浏览器已打开")
        except:
            print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:5000")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    return True

def main():
    """主函数"""
    try:
        if test_webapp():
            # 启动Flask应用
            print("\n" + "="*50)
            print("🎯 启动Flask服务器...")
            print("按 Ctrl+C 停止服务器")
            print("="*50)
            
            # 切换到webapp目录并启动
            import os
            os.chdir("webapp")
            
            # 导入并启动app
            sys.path.insert(0, ".")
            from app import app
            app.run(host='0.0.0.0', port=5000, debug=False)
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查:")
        print("   - Python版本是否为3.8+")
        print("   - 依赖包是否正确安装")
        print("   - 端口5000是否被占用")

if __name__ == "__main__":
    main()