#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 最终完美版Web应用后端
基于main_final_perfect.py，包含调试信息，修复车辆计数为0的问题
"""

import os
import sys
import uuid
import time
import json
import threading
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file, render_template
from werkzeug.utils import secure_filename
import cv2
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    print("✅ ultralytics已安装")
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️  警告: ultralytics未安装，将使用模拟模式")

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB限制

# 全局变量
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv'}

# 任务状态存储
task_status = {}
task_results = {}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# COCO类别映射 - 按照最终完美版本
CLASS_NAMES = {
    0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane',
    5: 'bus', 6: 'train', 7: 'truck', 8: 'boat'
}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def is_vehicle(class_id):
    """判断是否为车辆 - 按照最终完美版本"""
    vehicle_classes = {1, 2, 3, 5, 7}  # bicycle, car, motorcycle, bus, truck
    return int(class_id) in vehicle_classescla
ss FinalPerfectVehicleCounter:
    """最终完美版车辆计数器 - 基于main_final_perfect.py"""
    
    def __init__(self, model_name='yolov11m', confidence=0.5):
        self.model_name = model_name
        self.confidence = confidence
        self.model = None
        
        # 跟踪相关 - 完全按照最终完美版本
        self.track_history = {}
        self.vehicle_crossed_ids = set()  # 已穿越计数线的车辆ID
        self.person_seen_ids = set()      # 已见过的人员ID
        
        # ID映射管理
        self.original_to_clean_id = {}
        self.next_clean_id = 1
        
        # 计数结果
        self.vehicle_left_count = 0
        self.vehicle_right_count = 0
        
        # 调试信息
        self.debug_info = {
            'vehicles_detected': 0,
            'people_detected': 0,
            'near_line': 0,
            'classes_found': set()
        }
        
        self.load_model()
    
    def load_model(self):
        """加载YOLO模型"""
        if not YOLO_AVAILABLE:
            print("⚠️  YOLO不可用，使用模拟模式")
            return
        
        try:
            # 尝试加载自定义训练的模型
            custom_model_paths = [
                "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                "../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                "../../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
            ]
            
            model_loaded = False
            for model_path in custom_model_paths:
                if os.path.exists(model_path):
                    self.model = YOLO(model_path)
                    print(f"✅ 成功加载自定义模型: {model_path}")
                    model_loaded = True
                    break
            
            if not model_loaded:
                # 使用预训练模型
                model_path = f"{self.model_name}.pt"
                self.model = YOLO(model_path)
                print(f"✅ 成功加载预训练模型: {model_path}")
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def get_clean_id(self, original_id):
        """获取清洁的ID（从1开始按顺序分配）"""
        if original_id not in self.original_to_clean_id:
            self.original_to_clean_id[original_id] = self.next_clean_id
            self.next_clean_id += 1
        return self.original_to_clean_id[original_id]    
def create_perfect_ui_overlay(self, frame, line_x):
        """创建完美的UI覆盖层 - 按照最终完美版本"""
        h, w = frame.shape[:2]
        
        # 创建半透明覆盖层
        overlay = frame.copy()
        
        # 左上角 - 车辆穿越统计区域
        cv2.rectangle(overlay, (10, 10), (400, 140), (139, 69, 19), -1)  # 深蓝色
        # 右上角 - 人员总数统计区域  
        cv2.rectangle(overlay, (w-410, 10), (w-10, 140), (19, 139, 69), -1)  # 深绿色
        
        # 调试信息区域
        cv2.rectangle(overlay, (10, 150), (600, 250), (69, 69, 139), -1)  # 紫色
        
        # 混合原图和覆盖层
        alpha = 0.85
        frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
        
        # 字体设置
        font = cv2.FONT_HERSHEY_DUPLEX
        font_scale = 1.5
        thickness = 3
        
        # 左上角 - 车辆穿越统计
        cv2.putText(frame, "VEHICLES CROSSED", (20, 40), font, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Left: {self.vehicle_left_count:03d}", (20, 75), font, 0.8, (255, 255, 255), 2)
        cv2.putText(frame, f"Right: {self.vehicle_right_count:03d}", (20, 110), font, 0.8, (255, 255, 255), 2)
        cv2.putText(frame, f"Total: {self.vehicle_left_count + self.vehicle_right_count:03d}", (200, 75), font, font_scale, (255, 255, 0), thickness)
        
        # 右上角 - 人员总数统计
        cv2.putText(frame, "PEOPLE DETECTED", (w-390, 40), font, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Total: {len(self.person_seen_ids):03d}", (w-200, 85), font, font_scale, (255, 255, 255), thickness)
        
        # 调试信息区域
        cv2.putText(frame, "DEBUG INFO:", (20, 175), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Vehicles detected: {self.debug_info['vehicles_detected']}", (20, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(frame, f"People detected: {self.debug_info['people_detected']}", (20, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(frame, f"Near line: {self.debug_info['near_line']}", (300, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(frame, f"Classes: {self.debug_info['classes_found']}", (20, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 绘制计数线
        cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 4)
        
        # 计数线标签 - 移到上方，不遮挡视野
        label_y = 260  # 放在调试信息下方
        cv2.rectangle(frame, (line_x-80, label_y), (line_x+80, label_y+40), (0, 255, 0), -1)
        cv2.putText(frame, "VEHICLE", (line_x-70, label_y+15), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        cv2.putText(frame, "COUNT LINE", (line_x-75, label_y+32), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return frame   
 def draw_perfect_detections(self, frame, results, line_x):
        """绘制完美的检测框 - 按照最终完美版本"""
        # 重置调试信息
        self.debug_info = {
            'vehicles_detected': 0,
            'people_detected': 0,
            'near_line': 0,
            'classes_found': set()
        }
        
        if results[0].boxes is None or results[0].boxes.id is None:
            self.debug_info['classes_found'] = ', '.join(self.debug_info['classes_found'])
            return frame
        
        boxes = results[0].boxes.xyxy.cpu().numpy()
        confidences = results[0].boxes.conf.cpu().numpy()
        class_ids = results[0].boxes.cls.cpu().numpy()
        original_track_ids = results[0].boxes.id.int().cpu().tolist()
        
        for box, conf, cls_id, original_id in zip(boxes, confidences, class_ids, original_track_ids):
            x1, y1, x2, y2 = map(int, box)
            
            # 获取清洁的ID
            clean_id = self.get_clean_id(original_id)
            
            # 获取正确的类别名称
            class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
            self.debug_info['classes_found'].add(class_name)
            
            # 统计调试信息
            if is_vehicle(cls_id):
                self.debug_info['vehicles_detected'] += 1
            elif class_name == 'person':
                self.debug_info['people_detected'] += 1
            
            # 为不同类别选择颜色
            if class_name == 'car':
                color = (0, 255, 255)  # 黄色
            elif class_name == 'truck':
                color = (255, 0, 255)  # 紫色
            elif class_name == 'bus':
                color = (255, 165, 0)  # 橙色
            elif class_name == 'motorcycle':
                color = (0, 255, 0)    # 绿色
            elif class_name == 'person':
                color = (255, 255, 255)  # 白色
            else:
                color = (128, 128, 128)  # 灰色
            
            # 绘制检测框
            thickness = 3 if is_vehicle(cls_id) else 2
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
            
            # 计算中心点
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            
            # 绘制中心点
            cv2.circle(frame, (center_x, center_y), 5, color, -1)
            
            # 绘制轨迹（使用原始ID作为键）
            if original_id in self.track_history:
                prev_x, prev_y = self.track_history[original_id]
                cv2.line(frame, (prev_x, prev_y), (center_x, center_y), color, 2)
            
            # 绘制标签 - 正确显示类别名称
            label = f"ID:{clean_id} {class_name}"
            if conf > 0:
                label += f" {conf:.2f}"
                
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 标签背景
            cv2.rectangle(frame, (x1, y1-25), (x1 + label_size[0] + 10, y1), color, -1)
            
            # 标签文字
            cv2.putText(frame, label, (x1+5, y1-8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            
            # 如果是车辆且靠近计数线，高亮显示
            if is_vehicle(cls_id) and abs(center_x - line_x) < 50:
                self.debug_info['near_line'] += 1
                cv2.rectangle(frame, (x1-5, y1-5), (x2+5, y2+5), (0, 0, 255), 3)
                # 添加"即将计数"提示
                cv2.putText(frame, "COUNTING ZONE", (x1, y2+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # 转换set为字符串用于显示
        self.debug_info['classes_found'] = ', '.join(self.debug_info['classes_found'])
        
        return frame   
 def process_video(self, input_path, output_path, task_id, settings):
        """处理视频文件 - 完全基于最终完美版本的逻辑"""
        try:
            # 重置计数器 - 按照最终完美版本
            self.track_history.clear()
            self.vehicle_crossed_ids.clear()
            self.person_seen_ids.clear()
            self.original_to_clean_id.clear()
            self.next_clean_id = 1
            self.vehicle_left_count = 0
            self.vehicle_right_count = 0
            
            print(f"🚗 加载模型: {self.model_name}")
            print(f"🎬 开始处理视频: {input_path}")
            
            # 更新任务状态
            task_status[task_id] = {
                'status': 'processing',
                'progress': 0,
                'message': '正在加载模型...',
                'vehicle_count': 0,
                'current_fps': 0
            }
            
            if self.model is None:
                raise Exception("模型未加载")
            
            # 打开视频 - 按照最终完美版本
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {input_path}")
            
            # 获取视频属性 - 按照最终完美版本
            w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            line_x = w // 2
            
            print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
            print(f"📏 计数线位置: x = {line_x}")
            
            # 设置输出视频编码器 - 按照最终完美版本
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
            print(f"💾 输出视频: {output_path}")
            
            frame_count = 0
            start_time = time.time()
            
            while cap.isOpened():
                success, frame = cap.read()
                if not success:
                    print("✅ 视频处理完成")
                    break
                
                frame_count += 1
                
                # 更新进度
                progress = (frame_count / total_frames) * 100 if total_frames > 0 else 0
                current_time = time.time()
                elapsed_time = current_time - start_time
                current_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                task_status[task_id].update({
                    'progress': progress,
                    'message': f'处理第 {frame_count}/{total_frames} 帧',
                    'vehicle_count': self.vehicle_left_count + self.vehicle_right_count,
                    'current_fps': current_fps
                })
                
                # YOLO跟踪 - 按照最终完美版本
                results = self.model.track(frame, persist=True, verbose=False, conf=self.confidence)
                
                # 绘制检测结果并获取调试信息 - 按照最终完美版本
                annotated_frame = self.draw_perfect_detections(frame.copy(), results, line_x)
                
                # 处理跟踪结果 - 完全按照最终完美版本的逻辑
                if results[0].boxes is not None and results[0].boxes.id is not None:
                    boxes = results[0].boxes.xywh.cpu()
                    class_ids = results[0].boxes.cls.cpu().numpy()
                    original_track_ids = results[0].boxes.id.int().cpu().tolist()

                    for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
                        center_x, center_y = int(box[0]), int(box[1])
                        clean_id = self.get_clean_id(original_id)
                        class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
                        
                        # 人员统计 - 按ID去重
                        if class_name == 'person':
                            if original_id not in self.person_seen_ids:
                                self.person_seen_ids.add(original_id)
                                print(f"👤 发现新人员 ID:{clean_id}")
                        
                        # 车辆穿越统计 - 完全按照最终完美版本
                        elif is_vehicle(cls_id):
                            if original_id in self.track_history:
                                prev_x, _ = self.track_history[original_id]
                                if original_id not in self.vehicle_crossed_ids:
                                    # 从左到右穿越
                                    if prev_x < line_x and center_x >= line_x:
                                        self.vehicle_right_count += 1
                                        self.vehicle_crossed_ids.add(original_id)
                                        print(f"🚙 车辆 ID:{clean_id} ({class_name}) 从左到右穿越，右侧计数: {self.vehicle_right_count}")
                                    # 从右到左穿越
                                    elif prev_x > line_x and center_x <= line_x:
                                        self.vehicle_left_count += 1
                                        self.vehicle_crossed_ids.add(original_id)
                                        print(f"🚗 车辆 ID:{clean_id} ({class_name}) 从右到左穿越，左侧计数: {self.vehicle_left_count}")
                            else:
                                # 第一次出现的车辆，记录位置但不计数
                                print(f"🔍 发现新车辆 ID:{clean_id} ({class_name}) 位置: x={center_x}, 计数线: x={line_x}")
                        
                        # 更新历史位置 - 按照最终完美版本
                        self.track_history[original_id] = (center_x, center_y)
                
                # 创建完美UI覆盖层 - 按照最终完美版本
                annotated_frame = self.create_perfect_ui_overlay(annotated_frame, line_x)
                
                # 保存帧 - 按照最终完美版本
                out.write(annotated_frame)
                
                # 进度显示 - 按照最终完美版本
                if frame_count % 100 == 0:
                    print(f"📊 进度: {frame_count} 帧, 左侧: {self.vehicle_left_count}, 右侧: {self.vehicle_right_count}")
                    print(f"🔍 调试: 车辆检测{self.debug_info['vehicles_detected']}, 人员检测{self.debug_info['people_detected']}, 靠近线{self.debug_info['near_line']}")
            
            # 清理资源 - 按照最终完美版本
            cap.release()
            out.release()
            
            # 计算最终结果
            processing_time = time.time() - start_time
            avg_fps = frame_count / processing_time if processing_time > 0 else 0
            
            # 保存结果
            task_results[task_id] = {
                'vehicle_left': self.vehicle_left_count,
                'vehicle_right': self.vehicle_right_count,
                'people_total': len(self.person_seen_ids),
                'processed_frames': frame_count,
                'processing_time': f"{processing_time:.2f}s",
                'avg_fps': avg_fps,
                'avg_confidence': self.confidence,
                'debug_vehicles_detected': self.debug_info['vehicles_detected'],
                'debug_people_detected': self.debug_info['people_detected'],
                'completion_time': datetime.now().isoformat()
            }
            
            # 更新最终状态
            task_status[task_id] = {
                'status': 'completed',
                'progress': 100,
                'message': '处理完成',
                'vehicle_count': self.vehicle_left_count + self.vehicle_right_count,
                'current_fps': avg_fps
            }
            
            print("=" * 50)
            print("🎉 处理完成！")
            print(f"📊 最终计数结果:")
            print(f"   左侧: {self.vehicle_left_count}")
            print(f"   右侧: {self.vehicle_right_count}")
            print(f"   总计: {self.vehicle_left_count + self.vehicle_right_count}")
            print(f"   人员: {len(self.person_seen_ids)}")
            print(f"💾 输出视频: {output_path}")
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ 视频处理失败: {e}")
            import traceback
            traceback.print_exc()
            task_status[task_id] = {
                'status': 'error',
                'progress': 0,
                'message': f'处理失败: {str(e)}',
                'vehicle_count': 0,
                'current_fps': 0
            }# Fla
sk路由部分
@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_video():
    """上传视频文件"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 生成任务ID和安全文件名
        task_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{filename}")
        
        # 保存文件
        file.save(file_path)
        print(f"📁 文件已保存: {file_path}")
        
        # 初始化任务状态
        task_status[task_id] = {
            'status': 'uploaded',
            'progress': 0,
            'message': '文件上传成功',
            'vehicle_count': 0,
            'current_fps': 0,
            'file_path': file_path
        }
        
        return jsonify({
            'task_id': task_id,
            'message': '文件上传成功'
        })
        
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/process', methods=['POST'])
def process_video():
    """开始处理视频"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        settings = data.get('settings', {})
        
        print(f"🚀 开始处理任务: {task_id}")
        print(f"⚙️  设置: {settings}")
        
        if not task_id or task_id not in task_status:
            return jsonify({'error': '无效的任务ID'}), 400
        
        if task_status[task_id]['status'] != 'uploaded':
            return jsonify({'error': '任务状态错误'}), 400
        
        # 获取输入文件路径
        input_path = task_status[task_id]['file_path']
        output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_processed.mp4")
        
        print(f"📥 输入文件: {input_path}")
        print(f"📤 输出文件: {output_path}")
        
        # 创建最终完美版车辆计数器
        counter = FinalPerfectVehicleCounter(
            model_name=settings.get('model', 'yolov11m'),
            confidence=settings.get('confidence', 0.5)
        )
        
        # 在后台线程中处理视频
        thread = threading.Thread(
            target=counter.process_video,
            args=(input_path, output_path, task_id, settings)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'message': '开始AI智能分析',
            'task_id': task_id
        })
        
    except Exception as e:
        print(f"❌ 处理启动失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """获取任务状态"""
    if task_id not in task_status:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(task_status[task_id])

@app.route('/api/results/<task_id>')
def get_results(task_id):
    """获取处理结果"""
    if task_id not in task_results:
        return jsonify({'error': '结果不存在'}), 404
    
    return jsonify(task_results[task_id])

@app.route('/api/download/<task_id>')
def download_video(task_id):
    """下载处理后的视频"""
    output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_processed.mp4")
    
    if not os.path.exists(output_path):
        return jsonify({'error': '文件不存在'}), 404
    
    return send_file(
        output_path,
        as_attachment=True,
        download_name=f"final_perfect_video_{task_id}.mp4",
        mimetype='video/mp4'
    )

if __name__ == '__main__':
    print("🏆 YOLOv11车辆计数器 - 最终完美版Web服务器")
    print("=" * 70)
    print("🌟 最终完美版特色:")
    print("   ✅ 基于main_final_perfect.py最新版本")
    print("   🔍 包含完整调试信息")
    print("   🎯 修复车辆计数为0的问题")
    print("   📊 车辆穿越计数（左右分别）+ 人员总数统计")
    print("   🎨 完美的UI覆盖层和可视化")
    print("   ⚡ 经过验证的稳定算法")
    print("=" * 70)
    print("🌐 访问地址: http://localhost:5000")
    print("📁 上传目录:", os.path.abspath(UPLOAD_FOLDER))
    print("📁 输出目录:", os.path.abspath(OUTPUT_FOLDER))
    
    if not YOLO_AVAILABLE:
        print("\n⚠️  注意: YOLO不可用，运行在模拟模式")
        print("💡 安装ultralytics以启用真实AI检测:")
        print("   pip install ultralytics")
    
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)