#!/usr/bin/env python3
"""
测试AI功能是否正常工作
"""

import os
import sys
import requests
import time

def test_ai_functionality():
    """测试AI功能"""
    print("🧪 测试AI功能...")
    
    # 检查服务器是否运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
        else:
            print("❌ 服务器响应异常")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: python app.py")
        return False
    
    return True

def check_model_loading():
    """检查模型加载"""
    print("🤖 检查模型加载...")
    
    # 检查YOLO是否可用
    try:
        from ultralytics import YOLO
        print("✅ ultralytics已安装")
        
        # 尝试加载模型
        try:
            model = YOLO('yolov11m.pt')
            print("✅ 预训练模型可以加载")
            return True
        except Exception as e:
            print(f"⚠️  预训练模型加载失败: {e}")
            return False
            
    except ImportError:
        print("❌ ultralytics未安装")
        print("💡 请安装: pip install ultralytics")
        return False

def main():
    """主函数"""
    print("🏆 YOLOv11车辆计数器 - AI功能测试")
    print("=" * 50)
    
    tests = [
        ("模型加载测试", check_model_loading),
        ("Web服务器测试", test_ai_functionality)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 项通过")
    
    if passed == len(tests):
        print("🎉 AI功能测试通过！")
        print("\n🚀 现在可以:")
        print("   1. 运行 python app.py")
        print("   2. 访问 http://localhost:5000")
        print("   3. 上传视频进行AI分析")
        print("\n💡 关键改进:")
        print("   ✅ 强制加载AI模型")
        print("   ✅ 每帧都进行AI检测")
        print("   ✅ 详细的处理日志")
        print("   ✅ 修复视频编码器")
        print("   ✅ 添加视频流播放")
    else:
        print("⚠️  部分测试失败")
        print("💡 请检查环境配置")

if __name__ == "__main__":
    main()