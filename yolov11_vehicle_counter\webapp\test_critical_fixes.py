#!/usr/bin/env python3
"""
关键问题修复验证脚本
逐一测试每个关键功能
"""

import os
import time
import requests
import json

def test_server_running():
    """测试服务器是否运行"""
    print("🧪 测试1: 服务器运行状态")
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器运行正常")
            return True
        else:
            print(f"❌ Web服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到Web服务器: {e}")
        print("💡 请确保运行了: python app.py")
        return False

def test_model_loading():
    """测试AI模型加载状态"""
    print("\\n🧪 测试2: AI模型加载状态")
    try:
        response = requests.get("http://localhost:5000/api/model-status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            model_info = data.get('model_info', {})
            
            print(f"📊 模型状态: {data.get('status')}")
            print(f"🤖 模型已加载: {model_info.get('loaded')}")
            print(f"📁 模型类型: {model_info.get('type')}")
            print(f"📂 模型路径: {model_info.get('path')}")
            
            if model_info.get('loaded'):
                print("✅ AI模型加载成功")
                return True
            else:
                print("❌ AI模型未加载")
                print("💡 请检查ultralytics是否安装: pip install ultralytics")
                return False
        else:
            print(f"❌ 无法获取模型状态: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模型状态检查失败: {e}")
        return False

def test_file_upload():
    """测试文件上传功能"""
    print("\\n🧪 测试3: 文件上传功能")
    
    # 创建测试视频文件
    test_file_path = "test_video.mp4"
    try:
        # 创建一个小的测试文件
        with open(test_file_path, 'wb') as f:
            f.write(b'\\x00\\x00\\x00\\x20ftypmp42\\x00\\x00\\x00\\x00mp42isom' + b'\\x00' * 1000)
        
        with open(test_file_path, 'rb') as f:
            files = {'video': ('test_video.mp4', f, 'video/mp4')}
            response = requests.post("http://localhost:5000/api/upload", files=files, timeout=10)
        
        os.remove(test_file_path)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data.get('task_id')
            print(f"✅ 文件上传成功，任务ID: {task_id}")
            return task_id
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 文件上传测试失败: {e}")
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        return None

def test_status_api(task_id):
    """测试状态API是否包含计数字段"""
    print("\\n🧪 测试4: 状态API字段完整性")
    
    if not task_id:
        print("❌ 没有有效的任务ID")
        return False
    
    try:
        response = requests.get(f"http://localhost:5000/api/status/{task_id}", timeout=5)
        if response.status_code == 200:
            status_data = response.json()
            
            # 检查必要字段
            required_fields = ['vehicle_count', 'vehicle_left', 'vehicle_right', 'people_count']
            present_fields = []
            missing_fields = []
            
            for field in required_fields:
                if field in status_data:
                    present_fields.append(field)
                    print(f"✅ {field}: {status_data[field]}")
                else:
                    missing_fields.append(field)
                    print(f"❌ 缺少字段: {field}")
            
            if not missing_fields:
                print("✅ 所有必要的计数字段都存在")
                return True
            else:
                print(f"❌ 缺少字段: {missing_fields}")
                return False
        else:
            print(f"❌ 无法获取状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态API测试失败: {e}")
        return False

def test_preview_endpoint(task_id):
    """测试视频预览端点"""
    print("\\n🧪 测试5: 视频预览端点")
    
    if not task_id:
        print("❌ 没有有效的任务ID")
        return False
    
    try:
        response = requests.head(f"http://localhost:5000/api/preview/{task_id}", timeout=5)
        print(f"📊 预览端点响应状态: {response.status_code}")
        
        if response.status_code == 404:
            print("⚠️  视频文件不存在（这是正常的，因为我们没有真正处理视频）")
            return True
        elif response.status_code == 200:
            print("✅ 预览端点工作正常")
            return True
        else:
            print(f"❌ 预览端点异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 预览端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 关键问题修复验证测试")
    print("=" * 60)
    print("📋 测试项目:")
    print("   1. 服务器运行状态")
    print("   2. AI模型加载状态")
    print("   3. 文件上传功能")
    print("   4. 状态API字段完整性")
    print("   5. 视频预览端点")
    print("=" * 60)
    
    # 执行测试
    results = []
    
    # 测试1: 服务器状态
    server_ok = test_server_running()
    results.append(("服务器运行", server_ok))
    
    if not server_ok:
        print("\\n❌ 服务器未运行，无法继续测试")
        return
    
    # 测试2: 模型加载
    model_ok = test_model_loading()
    results.append(("AI模型加载", model_ok))
    
    # 测试3: 文件上传
    task_id = test_file_upload()
    upload_ok = task_id is not None
    results.append(("文件上传", upload_ok))
    
    # 测试4: 状态API
    status_ok = test_status_api(task_id)
    results.append(("状态API", status_ok))
    
    # 测试5: 预览端点
    preview_ok = test_preview_endpoint(task_id)
    results.append(("预览端点", preview_ok))
    
    # 汇总结果
    print("\\n" + "=" * 60)
    print("📊 测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\\n🎉 所有关键功能测试通过！")
        print("\\n✅ 修复确认:")
        print("   🌐 Web服务器正常运行")
        print("   🤖 AI模型成功加载")
        print("   📤 文件上传功能正常")
        print("   📊 状态API包含计数字段")
        print("   🎬 视频预览端点正常")
        print("\\n🚀 现在可以测试完整的视频处理流程！")
    else:
        print("\\n⚠️  部分功能存在问题，需要进一步修复")
        print("\\n💡 故障排除建议:")
        if not model_ok:
            print("   - 安装YOLO: pip install ultralytics")
        if not upload_ok:
            print("   - 检查文件上传权限和磁盘空间")
        if not status_ok:
            print("   - 检查后端状态更新逻辑")
        if not preview_ok:
            print("   - 检查视频文件路径和权限")

if __name__ == "__main__":
    main()