@echo off
REM ========================================
REM Local Upload Script - Run on Windows PC
REM ========================================

echo ========================================
echo YOLOv11 Vehicle Counter - File Upload
echo ========================================
echo This script runs on your Windows PC
echo Server IP: ************
echo Username: admin
echo ========================================
echo.

REM Check if local files exist
echo Checking local files...
if not exist "yolov11_vehicle_counter\webapp\app.py" (
    echo ERROR: app.py file not found
    echo Please run this script in project root directory
    pause
    exit /b 1
)

if not exist "yolov11_vehicle_counter\webapp\requirements.txt" (
    echo ERROR: requirements.txt file not found
    pause
    exit /b 1
)

if not exist "yolov11_vehicle_counter\webapp\templates\index.html" (
    echo ERROR: templates/index.html file not found
    pause
    exit /b 1
)

if not exist "yolov11_vehicle_counter\webapp\static" (
    echo ERROR: static directory not found
    pause
    exit /b 1
)

if not exist "yolov11_vehicle_counter\webapp\deploy_config.py" (
    echo ERROR: deploy_config.py file not found
    pause
    exit /b 1
)

echo SUCCESS: Local files check passed
echo.

echo Starting file upload to server...
echo NOTE: You need to enter server password for each file
echo.

echo Creating webapp directory on server...
ssh admin@************ "mkdir -p ~/webapp"

echo.
echo Uploading main application files...
scp "yolov11_vehicle_counter\webapp\app.py" admin@************:~/webapp/
scp "yolov11_vehicle_counter\webapp\deploy_config.py" admin@************:~/webapp/
scp "yolov11_vehicle_counter\webapp\start_server.py" admin@************:~/webapp/
scp "yolov11_vehicle_counter\webapp\requirements.txt" admin@************:~/webapp/
scp "yolov11_vehicle_counter\webapp\DEPLOYMENT.md" admin@************:~/webapp/

echo.
echo Uploading templates directory...
scp -r "yolov11_vehicle_counter\webapp\templates" admin@************:~/webapp/

echo.
echo Uploading static directory...
scp -r "yolov11_vehicle_counter\webapp\static" admin@************:~/webapp/

echo.
echo ========================================
echo File upload completed!
echo ========================================
echo.
echo Next steps:
echo 1. Login to server: ssh admin@************
echo 2. cd ~/webapp
echo 3. pip install -r requirements.txt
echo 4. python start_server.py
echo.
echo Web application will be available at:
echo http://************:5000
echo.
echo If upload fails, use WinSCP:
echo https://winscp.net/eng/download.php
echo.
pause
