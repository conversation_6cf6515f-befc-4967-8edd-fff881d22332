"""
文件管理器模块
处理时间戳命名和文件管理功能
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class FileManager:
    """文件管理器 - 处理时间戳命名和实验记录"""
    
    def __init__(self):
        self.experiment_log_path = Path("logs/experiment_log.json")
        self.ensure_log_directory()
    
    def ensure_log_directory(self):
        """确保日志目录存在"""
        self.experiment_log_path.parent.mkdir(parents=True, exist_ok=True)
    
    def generate_timestamp_filename(self, base_name: str, extension: str = ".mp4") -> str:
        """
        生成带时间戳的文件名
        
        Args:
            base_name: 基础文件名
            extension: 文件扩展名
            
        Returns:
            str: 带时间戳的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_{timestamp}{extension}"
    
    def generate_experiment_id(self) -> str:
        """生成实验ID"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def create_timestamped_output_path(self, output_dir: Path, base_name: str, 
                                     environment_type: str) -> Path:
        """
        创建带时间戳的输出路径
        
        Args:
            output_dir: 输出目录
            base_name: 基础文件名
            environment_type: 环境类型
            
        Returns:
            Path: 完整的输出路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{base_name}_{environment_type}_{timestamp}.mp4"
        return output_dir / filename
    
    def log_experiment(self, experiment_data: Dict[str, Any]):
        """
        记录实验信息
        
        Args:
            experiment_data: 实验数据
        """
        try:
            # 读取现有日志
            if self.experiment_log_path.exists():
                with open(self.experiment_log_path, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            # 添加时间戳
            experiment_data['timestamp'] = datetime.now().isoformat()
            
            # 添加新实验
            logs.append(experiment_data)
            
            # 保存日志
            with open(self.experiment_log_path, 'w', encoding='utf-8') as f:
                json.dump(logs, f, indent=2, ensure_ascii=False)
            
            logger.info(f"实验记录已保存: {experiment_data.get('experiment_id', 'unknown')}")
            
        except Exception as e:
            logger.error(f"保存实验记录失败: {e}")
    
    def get_experiment_summary(self) -> Dict[str, Any]:
        """获取实验摘要"""
        try:
            if not self.experiment_log_path.exists():
                return {"total_experiments": 0, "experiments": []}
            
            with open(self.experiment_log_path, 'r', encoding='utf-8') as f:
                logs = json.load(f)
            
            return {
                "total_experiments": len(logs),
                "experiments": logs[-10:],  # 最近10次实验
                "log_file": str(self.experiment_log_path)
            }
            
        except Exception as e:
            logger.error(f"读取实验记录失败: {e}")
            return {"error": str(e)}

# 全局文件管理器实例
file_manager = FileManager()

def get_file_manager() -> FileManager:
    """获取全局文件管理器实例"""
    return file_manager