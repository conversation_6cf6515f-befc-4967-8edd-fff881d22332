#!/usr/bin/env python3
"""
测试终极完美版Web应用
验证是否基于最好的追踪+撞线计数实现
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_ultimate_backend():
    """测试终极完美版后端"""
    print("🏆 测试终极完美版后端...")
    backend_file = Path("webapp/app_ultimate_perfect.py")
    
    if not backend_file.exists():
        print("❌ 终极完美版后端文件不存在")
        return False
    
    content = backend_file.read_text(encoding='utf-8')
    
    # 检查终极完美版特征
    ultimate_features = 0
    features_to_check = [
        ("基于main_final_perfect.py", "基于main_final_perfect.py"),
        ("车辆穿越统计", "vehicle_crossed_ids"),
        ("人员统计", "person_seen_ids"),
        ("调试信息", "debug_info"),
        ("完美UI覆盖层", "create_perfect_ui_overlay"),
        ("撞线计数逻辑", "prev_x < line_x and center_x >= line_x"),
        ("轨迹绘制", "track_history"),
        ("ID管理", "get_clean_id"),
        ("计数区域高亮", "COUNTING ZONE"),
        ("类别颜色区分", "class_name == 'car'")
    ]
    
    for feature_name, feature_code in features_to_check:
        if feature_code in content:
            print(f"✅ {feature_name}")
            ultimate_features += 1
        else:
            print(f"❌ {feature_name}")
    
    if ultimate_features >= 8:
        print(f"✅ 终极完美版后端验证通过 ({ultimate_features}/{len(features_to_check)} 项特征)")
        return True
    else:
        print(f"⚠️  终极完美版后端不完整 ({ultimate_features}/{len(features_to_check)} 项特征)")
        return False

def compare_with_original():
    """与原始最终完美版本对比"""
    print("🔍 与原始最终完美版本对比...")
    original_file = Path("src/main_final_perfect.py")
    web_file = Path("webapp/app_ultimate_perfect.py")
    
    if not original_file.exists():
        print("❌ 原始最终完美版本不存在")
        return False
    
    if not web_file.exists():
        print("❌ Web终极完美版本不存在")
        return False
    
    original_content = original_file.read_text(encoding='utf-8')
    web_content = web_file.read_text(encoding='utf-8')
    
    # 检查关键算法是否一致
    key_algorithms = [
        "model.track(frame, persist=True",
        "vehicle_crossed_ids",
        "person_seen_ids", 
        "get_clean_id",
        "prev_x < line_x and center_x >= line_x",
        "prev_x > line_x and center_x <= line_x",
        "create_perfect_ui_overlay",
        "draw_perfect_detections",
        "COUNTING ZONE",
        "debug_info"
    ]
    
    consistent = 0
    for algorithm in key_algorithms:
        if algorithm in original_content and algorithm in web_content:
            print(f"✅ 算法一致: {algorithm[:30]}...")
            consistent += 1
        else:
            print(f"⚠️  算法不一致: {algorithm[:30]}...")
    
    if consistent >= 8:
        print(f"✅ 核心算法一致性验证通过 ({consistent}/{len(key_algorithms)})")
        return True
    else:
        print(f"❌ 核心算法一致性验证失败 ({consistent}/{len(key_algorithms)})")
        return False

def test_webapp_connection():
    """测试Web应用连接"""
    print("🧪 测试Web应用连接...")
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: run_webapp_ultimate.bat")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def check_file_completeness():
    """检查文件完整性"""
    print("📋 检查文件完整性...")
    
    required_files = [
        Path("webapp/app_ultimate_perfect.py"),
        Path("run_webapp_ultimate.py"),
        Path("run_webapp_ultimate.bat"),
        Path("src/main_final_perfect.py")
    ]
    
    missing_files = []
    for file_path in required_files:
        if file_path.exists():
            print(f"✅ {file_path.name}")
        else:
            print(f"❌ {file_path.name}")
            missing_files.append(file_path.name)
    
    if not missing_files:
        print("✅ 所有必需文件都存在")
        return True
    else:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False

def analyze_best_implementation():
    """分析最佳实现"""
    print("🔍 分析最佳实现...")
    
    # 检查所有可能的实现文件
    implementation_files = [
        ("main_final_perfect.py", "最终完美版"),
        ("main_perfect.py", "完美版"),
        ("main_golden.py", "黄金版"),
        ("main_clean_id.py", "清洁ID版"),
        ("main_beautiful_1080p.py", "美化版")
    ]
    
    best_score = 0
    best_file = None
    
    for filename, description in implementation_files:
        file_path = Path(f"src/{filename}")
        if file_path.exists():
            try:
                content = file_path.read_text(encoding='utf-8')
                score = 0
                
                # 评分标准
                scoring_criteria = [
                    ("model.track", 2),  # YOLO内置跟踪
                    ("vehicle_crossed_ids", 2),  # 车辆计数
                    ("person_seen_ids", 1),  # 人员统计
                    ("debug_info", 1),  # 调试信息
                    ("create_perfect_ui_overlay", 2),  # UI覆盖层
                    ("get_clean_id", 1),  # ID管理
                    ("COUNTING ZONE", 1),  # 计数区域
                    ("track_history", 1)  # 轨迹历史
                ]
                
                for criterion, points in scoring_criteria:
                    if criterion in content:
                        score += points
                
                print(f"📊 {filename}: {score}分 ({description})")
                
                if score > best_score:
                    best_score = score
                    best_file = filename
                    
            except Exception as e:
                print(f"⚠️  无法分析 {filename}: {e}")
    
    if best_file:
        print(f"🏆 最佳实现: {best_file} ({best_score}分)")
        return best_file == "main_final_perfect.py"
    else:
        print("❌ 未找到有效的实现文件")
        return False

def main():
    """主函数"""
    print("🏆 YOLOv11车辆计数器 - 终极完美版Web应用测试")
    print("=" * 80)
    
    tests = [
        ("文件完整性检查", check_file_completeness),
        ("最佳实现分析", analyze_best_implementation),
        ("终极完美版后端验证", test_ultimate_backend),
        ("算法一致性检查", compare_with_original),
        ("Web应用连接测试", test_webapp_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 60)
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed >= 4:  # 至少4项通过就算成功
        print("🎉 终极完美版Web应用验证通过！")
        print("\n🏆 终极完美版特色:")
        print("   ✅ 基于main_final_perfect.py最佳实现")
        print("   🎯 完整的车辆追踪+撞线计数功能")
        print("   👥 人员检测和统计")
        print("   🔍 实时调试信息显示")
        print("   🎨 完美的UI覆盖层和可视化")
        print("   📊 详细的统计结果")
        print("   🌐 友好的Web界面，支持大文件上传")
        print("\n🚀 现在可以运行:")
        print("   run_webapp_ultimate.bat")
        print("\n💡 这是基于最好的追踪+撞线计数实现的Web版本！")
    else:
        print("⚠️  部分验证未通过")
        print("\n💡 建议:")
        print("   1. 检查终极完美版文件是否正确创建")
        print("   2. 确保算法逻辑与最佳版本一致")
        print("   3. 重新运行 run_webapp_ultimate.py")

if __name__ == "__main__":
    main()