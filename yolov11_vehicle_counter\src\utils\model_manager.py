#!/usr/bin/env python3
"""
模型管理器 - 处理YOLO模型加载、验证和备用机制
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from ultralytics import YOLO
import torch

class ModelManager:
    """模型管理器 - 处理YOLO模型的加载和验证"""
    
    def __init__(self, base_path: Path):
        self.base_path = Path(base_path)
        self.logger = logging.getLogger(__name__)
        self.loaded_models = {}  # 缓存已加载的模型
        
    def load_yolo_model(self, model_path: Optional[str] = None) -> Tuple[YOLO, str]:
        """
        加载YOLO模型，支持备用机制
        
        Returns:
            Tuple[YOLO, str]: (模型实例, 实际使用的模型路径)
        """
        try:
            # 如果指定了模型路径，优先使用
            if model_path and self.validate_model_path(model_path):
                return self._load_single_model(model_path), model_path
            
            # 否则按优先级尝试加载模型
            model_paths = self.get_available_model_paths()
            
            for path_info in model_paths:
                model_path = path_info['path']
                try:
                    self.logger.info(f"尝试加载模型: {model_path}")
                    model = self._load_single_model(model_path)
                    self.logger.info(f"成功加载模型: {model_path}")
                    return model, str(model_path)
                except Exception as e:
                    self.logger.warning(f"加载模型失败 {model_path}: {e}")
                    continue
            
            # 如果所有本地模型都失败，使用预训练模型
            self.logger.info("所有本地模型加载失败，使用预训练模型")
            pretrained_model = "yolov11m.pt"
            model = self._load_single_model(pretrained_model)
            return model, pretrained_model
            
        except Exception as e:
            self.logger.error(f"模型加载完全失败: {e}")
            raise RuntimeError(f"无法加载任何YOLO模型: {e}")
    
    def _load_single_model(self, model_path: str) -> YOLO:
        """加载单个模型"""
        # 检查缓存
        if model_path in self.loaded_models:
            self.logger.info(f"从缓存加载模型: {model_path}")
            return self.loaded_models[model_path]
        
        # 加载新模型
        model = YOLO(model_path)
        
        # 验证模型是否正常工作
        self._validate_model_functionality(model)
        
        # 缓存模型
        self.loaded_models[model_path] = model
        
        return model
    
    def validate_model_path(self, model_path: str) -> bool:
        """验证模型路径是否有效"""
        try:
            path = Path(model_path)
            
            # 如果是预训练模型名称，直接返回True
            if model_path.endswith('.pt') and not path.is_absolute():
                return True
            
            # 检查文件是否存在
            if not path.exists():
                self.logger.warning(f"模型文件不存在: {model_path}")
                return False
            
            # 检查文件大小
            if path.stat().st_size < 1024:  # 小于1KB可能是损坏的文件
                self.logger.warning(f"模型文件可能损坏（文件过小）: {model_path}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证模型路径时出错: {e}")
            return False
    
    def get_available_model_paths(self) -> List[Dict[str, any]]:
        """获取可用的模型路径列表，按优先级排序"""
        model_candidates = [
            {
                'name': 'portable',
                'path': self.base_path / "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
                'priority': 1,
                'description': '便携式车辆检测模型'
            },
            {
                'name': 'standard',
                'path': self.base_path / "runs/train/yolov11m_vehicle_detection/weights/best.pt",
                'priority': 2,
                'description': '标准车辆检测模型'
            },
            {
                'name': 'backup_portable',
                'path': self.base_path / "yolov11m_vehicle_detection_portable.pt",
                'priority': 3,
                'description': '备用便携式模型'
            },
            {
                'name': 'backup_standard',
                'path': self.base_path / "yolov11m_vehicle_detection.pt",
                'priority': 4,
                'description': '备用标准模型'
            }
        ]
        
        # 过滤存在的模型并按优先级排序
        available_models = []
        for model_info in model_candidates:
            if self.validate_model_path(str(model_info['path'])):
                available_models.append(model_info)
                self.logger.info(f"发现可用模型: {model_info['description']} - {model_info['path']}")
        
        # 按优先级排序
        available_models.sort(key=lambda x: x['priority'])
        
        return available_models
    
    def _validate_model_functionality(self, model: YOLO) -> bool:
        """验证模型功能是否正常"""
        try:
            # 创建一个测试图像
            import numpy as np
            test_image = np.zeros((640, 640, 3), dtype=np.uint8)
            
            # 尝试进行推理
            results = model(test_image, verbose=False)
            
            # 检查结果格式
            if not results or len(results) == 0:
                raise ValueError("模型推理返回空结果")
            
            self.logger.debug("模型功能验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"模型功能验证失败: {e}")
            raise RuntimeError(f"模型功能异常: {e}")
    
    def get_model_info(self, model: YOLO) -> Dict[str, any]:
        """获取模型信息"""
        try:
            info = {
                'model_type': 'YOLOv11',
                'device': str(model.device) if hasattr(model, 'device') else 'unknown',
                'task': getattr(model, 'task', 'detect'),
                'names': getattr(model, 'names', {}),
                'num_classes': len(getattr(model, 'names', {})),
            }
            
            # 尝试获取更多信息
            if hasattr(model, 'model') and hasattr(model.model, 'yaml'):
                yaml_info = model.model.yaml
                if isinstance(yaml_info, dict):
                    info.update({
                        'input_size': yaml_info.get('imgsz', 'unknown'),
                        'backbone': yaml_info.get('backbone', 'unknown'),
                    })
            
            return info
            
        except Exception as e:
            self.logger.warning(f"获取模型信息时出错: {e}")
            return {'error': str(e)}
    
    def optimize_model_for_inference(self, model: YOLO) -> YOLO:
        """优化模型以提高推理性能"""
        try:
            # 设置模型为评估模式
            if hasattr(model, 'model'):
                model.model.eval()
            
            # 如果有GPU，尝试使用GPU
            if torch.cuda.is_available():
                try:
                    model.to('cuda')
                    self.logger.info("模型已移至GPU")
                except Exception as e:
                    self.logger.warning(f"无法将模型移至GPU: {e}")
            
            # 设置推理优化参数
            if hasattr(model, 'overrides'):
                model.overrides.update({
                    'verbose': False,
                    'conf': 0.5,
                    'iou': 0.7,
                    'max_det': 300,
                })
            
            self.logger.info("模型推理优化完成")
            return model
            
        except Exception as e:
            self.logger.warning(f"模型优化失败: {e}")
            return model
    
    def create_error_report(self, errors: List[Exception]) -> Dict[str, any]:
        """创建错误报告"""
        report = {
            'timestamp': str(pd.Timestamp.now()) if 'pd' in globals() else 'unknown',
            'total_errors': len(errors),
            'error_types': {},
            'suggestions': []
        }
        
        # 分析错误类型
        for error in errors:
            error_type = type(error).__name__
            if error_type not in report['error_types']:
                report['error_types'][error_type] = []
            report['error_types'][error_type].append(str(error))
        
        # 生成建议
        if 'FileNotFoundError' in report['error_types']:
            report['suggestions'].append("检查模型文件路径是否正确")
            report['suggestions'].append("确保模型文件已下载并放置在正确位置")
        
        if 'RuntimeError' in report['error_types']:
            report['suggestions'].append("检查CUDA/GPU驱动是否正确安装")
            report['suggestions'].append("尝试使用CPU模式运行")
        
        if 'ImportError' in report['error_types']:
            report['suggestions'].append("检查ultralytics库是否正确安装")
            report['suggestions'].append("运行: pip install ultralytics")
        
        return report
    
    def clear_model_cache(self):
        """清理模型缓存"""
        self.loaded_models.clear()
        self.logger.info("模型缓存已清理")
    
    def get_fallback_models(self) -> List[str]:
        """获取备用模型列表"""
        return [
            "yolov11n.pt",  # 最小模型
            "yolov11s.pt",  # 小模型
            "yolov11m.pt",  # 中等模型
            "yolov11l.pt",  # 大模型
            "yolov11x.pt"   # 超大模型
        ]