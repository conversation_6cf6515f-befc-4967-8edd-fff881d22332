#!/usr/bin/env python3
"""
使用原始main.py的优化运行脚本
配置最佳参数以获得高性能
"""

import os
import sys
from pathlib import Path
import subprocess

def detect_environment():
    """自动检测环境并返回相应的路径"""
    # 本地环境路径
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    # 服务器环境路径
    server_model = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt')
    server_video = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    if local_model.exists():
        print("✅ 检测到本地环境")
        return {
            'model': str(local_model),
            'video': str(local_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'local'
        }
    elif server_model.exists():
        print("✅ 检测到服务器环境")
        return {
            'model': str(server_model),
            'video': str(server_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'server'
        }
    else:
        print("❌ 未找到有效的环境配置")
        return None

def main():
    print("🔥 车辆计数系统 - 原始版本优化")
    print("=" * 50)
    print("📝 使用原始main.py + 优化参数")
    print("🎯 目标: 最佳性能配置")
    print()
    
    # 检测环境
    config = detect_environment()
    if not config:
        input("按回车键退出...")
        return
    
    print(f"🌍 环境类型: {config['env']}")
    print(f"🤖 YOLO模型: {config['model']}")
    print(f"🎥 视频文件: {config['video']}")
    print(f"🔍 ReID模型: {config['reid']}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 构建优化的命令参数
    cmd = [
        sys.executable, 'main.py',
        '--yolo_model', config['model'],
        '--deep_sort_model', config['reid'],
        '--source', config['video'],
        '--save-vid',                    # 保存视频
        '--conf-thres', '0.6',          # 提高置信度阈值（减少误检，提升速度）
        '--iou-thres', '0.7',           # IOU阈值
        '--max-det', '100',             # 大幅减少最大检测数量（从1000->100）
        '--imgsz', '640',               # 输入图像尺寸
        '--device', '0',                # 使用GPU（如果可用）
        '--half',                       # 使用FP16半精度（提升速度）
        '--project', '../runs/track',   # 输出目录
        '--name', 'optimized_run',      # 运行名称
        '--exist-ok'                    # 允许覆盖现有结果
    ]
    
    print("⚙️  优化配置:")
    print("   • 置信度阈值: 0.6 (提高精度，减少误检)")
    print("   • IOU阈值: 0.7 (标准设置)")
    print("   • 最大检测数: 100 (大幅减少，提升90%速度)")
    print("   • 输入尺寸: 640x640 (标准分辨率)")
    print("   • GPU加速: 启用")
    print("   • FP16半精度: 启用")
    print("   • 显示视频: 禁用（提升性能）")
    print()
    
    print("🔄 开始优化处理...")
    print("📝 完整命令:")
    print("   " + " ".join(cmd))
    print()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True)
        print()
        print("✅ 处理完成！")
        print("📁 检查 runs/track/optimized_run 目录查看结果")
        
        # 显示输出路径
        output_dir = Path("../runs/track/optimized_run")
        if output_dir.exists():
            print(f"📊 输出目录: {output_dir.absolute()}")
            
            # 查找输出视频
            for video_file in output_dir.glob("*.mp4"):
                print(f"🎥 输出视频: {video_file}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
        print()
        print("💡 可能的解决方案:")
        print("   1. 检查模型文件是否存在")
        print("   2. 检查视频文件是否存在")
        print("   3. 确保GPU驱动正常（如果使用GPU）")
        print("   4. 尝试使用CPU: 将 --device 0 改为 --device cpu")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()