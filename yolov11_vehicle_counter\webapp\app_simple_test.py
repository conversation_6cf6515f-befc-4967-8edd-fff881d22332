#!/usr/bin/env python3
"""
简化版本的webapp用于测试
"""

from flask import Flask, render_template, request, jsonify, send_file
import os

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024

# 全局变量
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv'}

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_file():
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file and allowed_file(file.filename):
            # 生成任务ID
            import uuid
            task_id = str(uuid.uuid4())
            
            # 保存文件
            filename = f"{task_id}_{file.filename}"
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(file_path)
            
            return jsonify({
                'success': True,
                'task_id': task_id,
                'filename': filename,
                'message': '文件上传成功'
            })
        else:
            return jsonify({'error': '不支持的文件格式'}), 400
            
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/process', methods=['POST'])
def process_video():
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id:
            return jsonify({'error': '缺少任务ID'}), 400
        
        # 模拟处理
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '开始处理视频'
        })
        
    except Exception as e:
        return jsonify({'error': f'处理失败: {str(e)}'}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    # 模拟状态
    return jsonify({
        'status': 'processing',
        'progress': 50,
        'message': '正在处理...',
        'vehicle_count': 10,
        'people_count': 5,
        'current_fps': 25.5
    })

@app.route('/api/results/<task_id>')
def get_results(task_id):
    # 模拟结果
    return jsonify({
        'vehicle_left': 5,
        'vehicle_right': 8,
        'vehicle_total': 13,
        'people_total': 7,
        'processed_frames': 1000,
        'processing_time': '45.2s'
    })

if __name__ == '__main__':
    print("🧪 简化版webapp测试服务器")
    print("🌐 访问地址: http://localhost:5000")
    try:
        app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
