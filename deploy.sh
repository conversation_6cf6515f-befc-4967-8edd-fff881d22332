#!/bin/bash

# YOLOv11车辆计数系统自动部署脚本
# 适用于Ubuntu 22.04 + Apache环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="vehicle-counter"
PROJECT_DIR="/var/www/$PROJECT_NAME"
DOMAIN_NAME="vehicle.lkr666.online"  # 使用测试域名
PYTHON_VERSION="python3"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查Ubuntu版本
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_error "此脚本仅支持Ubuntu系统"
        exit 1
    fi
    
    # 检查Apache是否安装
    if ! command -v apache2 &> /dev/null; then
        log_error "Apache2未安装，请先安装Apache2"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    sudo apt update && sudo apt upgrade -y
    log_success "系统更新完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # Python环境
    sudo apt install -y python3 python3-pip python3-venv python3-dev build-essential
    
    # OpenCV依赖
    sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg
    
    # 进程管理
    sudo apt install -y supervisor
    
    log_success "依赖安装完成"
}

# 创建项目目录
create_project_dir() {
    log_info "创建项目目录..."
    
    if [ -d "$PROJECT_DIR" ]; then
        log_warning "项目目录已存在，将备份现有目录"
        sudo mv "$PROJECT_DIR" "${PROJECT_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown $USER:$USER "$PROJECT_DIR"
    
    log_success "项目目录创建完成: $PROJECT_DIR"
}

# 部署应用代码
deploy_application() {
    log_info "部署应用代码..."
    
    # 这里需要你手动上传代码文件
    log_warning "请手动将webapp目录下的所有文件上传到 $PROJECT_DIR"
    log_warning "可以使用以下命令："
    log_warning "scp -r ./yolov11_vehicle_counter/webapp/* user@server:$PROJECT_DIR/"
    
    read -p "代码文件是否已上传完成？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "请先上传代码文件"
        exit 1
    fi
    
    log_success "应用代码部署完成"
}

# 设置Python环境
setup_python_env() {
    log_info "设置Python虚拟环境..."
    
    cd "$PROJECT_DIR"
    
    # 创建虚拟环境
    $PYTHON_VERSION -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    else
        log_error "requirements.txt文件不存在"
        exit 1
    fi
    
    # 安装生产环境依赖
    pip install gunicorn
    
    log_success "Python环境设置完成"
}

# 配置Apache虚拟主机
configure_apache() {
    log_info "配置Apache虚拟主机..."
    
    # 启用必要模块
    sudo a2enmod rewrite proxy proxy_http headers
    
    # 创建虚拟主机配置
    sudo tee /etc/apache2/sites-available/${PROJECT_NAME}.conf > /dev/null <<EOF
<VirtualHost *:80>
    ServerName $DOMAIN_NAME
    ServerAlias www.$DOMAIN_NAME
    
    DocumentRoot $PROJECT_DIR
    
    # 静态文件
    Alias /static $PROJECT_DIR/static
    <Directory $PROJECT_DIR/static>
        Require all granted
    </Directory>
    
    # 代理到Flask应用
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # 日志
    ErrorLog \${APACHE_LOG_DIR}/${PROJECT_NAME}_error.log
    CustomLog \${APACHE_LOG_DIR}/${PROJECT_NAME}_access.log combined
</VirtualHost>
EOF
    
    # 启用站点
    sudo a2ensite ${PROJECT_NAME}.conf
    sudo systemctl reload apache2
    
    log_success "Apache配置完成"
}

# 创建Gunicorn配置
create_gunicorn_config() {
    log_info "创建Gunicorn配置..."
    
    cat > "$PROJECT_DIR/gunicorn.conf.py" <<EOF
# Gunicorn配置文件
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF
    
    log_success "Gunicorn配置创建完成"
}

# 创建启动脚本
create_start_script() {
    log_info "创建启动脚本..."
    
    cat > "$PROJECT_DIR/start.sh" <<EOF
#!/bin/bash
cd $PROJECT_DIR
source venv/bin/activate
exec gunicorn --config gunicorn.conf.py app:app
EOF
    
    chmod +x "$PROJECT_DIR/start.sh"
    
    log_success "启动脚本创建完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor..."
    
    sudo tee /etc/supervisor/conf.d/${PROJECT_NAME}.conf > /dev/null <<EOF
[program:$PROJECT_NAME]
command=$PROJECT_DIR/start.sh
directory=$PROJECT_DIR
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/${PROJECT_NAME}.log
environment=PATH="$PROJECT_DIR/venv/bin"
EOF
    
    # 重新加载配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动应用
    sudo supervisorctl start $PROJECT_NAME
    
    # 检查状态
    sleep 3
    if sudo supervisorctl status $PROJECT_NAME | grep -q "RUNNING"; then
        log_success "应用启动成功"
    else
        log_error "应用启动失败"
        sudo supervisorctl status $PROJECT_NAME
        exit 1
    fi
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 测试本地连接
    if curl -s http://localhost:5000/api/health > /dev/null; then
        log_success "本地API测试通过"
    else
        log_error "本地API测试失败"
    fi
    
    log_info "部署完成！"
    log_info "请配置域名解析: $DOMAIN_NAME -> 你的服务器IP"
    log_info "然后访问: http://$DOMAIN_NAME"
}

# 主函数
main() {
    log_info "开始部署YOLOv11车辆计数系统..."
    
    check_root
    check_system
    update_system
    install_dependencies
    create_project_dir
    deploy_application
    setup_python_env
    configure_apache
    create_gunicorn_config
    create_start_script
    configure_supervisor
    start_services
    test_deployment
    
    log_success "部署完成！"
}

# 运行主函数
main "$@"
