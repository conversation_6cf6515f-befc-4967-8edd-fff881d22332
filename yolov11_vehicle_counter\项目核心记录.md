# 项目核心记录

本文档旨在记录本项目的核心信息、关键代码和开发进展，以便于追踪和回顾。

---

## 1. 核心资产

### 最佳模型
- **本地训练最优:**
  - **mAP@.5-.95:** `0.698`
  - **路径:** `D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt`
- **服务器训练最优:**
  - **mAP@.5-.95:** `0.695`
  - **路径:** `C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt`

---

## 2. 终版推理脚本 (`main_final_version.py`)

这是我们当前最稳定、最方便的推理脚本。它无需任何命令行参数，可自动检测环境并加载对应路径。

### 完整代码

```python
import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
from ultralytics import YOLO
from pathlib import Path
import numpy as np
from tracker.deep_sort import DeepSort

# --- Configuration ---
track_history = {}
counted_ids = set()

def run_tracker(model_path, video_path, save_path, show_video=True):
    """Runs YOLOv11 object tracking, saves the output, and optionally displays it."""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    print(f"Loading model from: {model_path}")
    model = YOLO(model_path)

    # --- Initialize DeepSORT ---
    # We manually create the tracker with our optimized parameters
    reid_model_path = 'D:/lkr_yolo/yolov11_vehicle_counter/src/tracker/deep/osnet模型/osnet_x1_0_imagenet.pth'
    tracker = DeepSort(
        model_path=reid_model_path, # Pass the full path to the ReID model
        max_dist=0.3,
        max_iou_distance=0.7,
        max_age=150, # Our key optimization
        n_init=3,
        nn_budget=100,
        device='cpu' # or 'cuda' if you have a GPU-enabled deepsort model
    )
    # ---

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)

    # --- Dashboard Configuration ---
    dashboard_height = 100
    dashboard_color = (0, 0, 0) # Black
    new_h = h + dashboard_height
    # ---

    # Setup video writer with the new dimensions
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, new_h))
    print(f"Output video will be saved to: {save_path}")

    frame_idx = 0
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("Video processing completed.")
            break
        frame_idx += 1

        # --- Create the main canvas with the dashboard ---
        main_canvas = np.zeros((new_h, w, 3), dtype=np.uint8)
        main_canvas[:dashboard_height, :] = dashboard_color
        main_canvas[dashboard_height:, :] = frame
        # ---

        # --- Manual Tracking Loop ---
        # 1. Get detections from YOLO
        results = model.predict(frame, verbose=False)
        detections = results[0].boxes.data.cpu().numpy()

        # 2. Format detections for DeepSORT
        # We need (x, y, w, h) and confidence
        xywhs = detections[:, :4]
        confs = detections[:, 4]
        clss = detections[:, 5]

        # 3. Update the tracker
        tracks = tracker.update(xywhs, confs, clss, frame)
        # ---

        # Draw the counting line
        cv2.line(main_canvas, (line_start[0], line_start[1] + dashboard_height), (line_end[0], line_end[1] + dashboard_height), (0, 255, 0), 2)

        if len(tracks) > 0:
            for track in tracks:
                if not track.is_confirmed() or track.time_since_update > 1:
                    continue
                
                bbox = track.to_tlbr() # Get bounding box in (top, left, bottom, right)
                track_id = track.track_id
                cls = track.get_class()

                # Draw the bounding box on the main canvas
                x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
                cv2.rectangle(main_canvas, (x1, y1 + dashboard_height), (x2, y2 + dashboard_height), (0, 255, 0), 2)
                cv2.putText(main_canvas, f"ID: {track_id}", (x1, y1 + dashboard_height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # Counting logic
                center_x, center_y = int((x1 + x2) / 2), int((y1 + y2) / 2)
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                track_history[track_id] = (center_x, center_y)

        # --- Draw dashboard text ---
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1
        font_thickness = 2
        text_color = (255, 255, 255) # White

        cv2.putText(main_canvas, f"Left Count: {left_count}", (50, 60), font, font_scale, text_color, font_thickness)
        cv2.putText(main_canvas, f"Right Count: {right_count}", (w - 350, 60), font, font_scale, text_color, font_thickness)
        # ---

        if show_video:
            try:
                cv2.imshow("YOLOv11 Tracking and Counting", main_canvas)
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    break
            except cv2.error:
                print("(GUI not available) Cannot display video. Continuing to process and save the video.")
                show_video = False

        out.write(main_canvas)

    cap.release()
    out.release()
    cv2.destroyAllWindows()
    print(f"Final Counts -> Left: {left_count}, Right: {right_count}")
    print(f"Output video saved successfully to {save_path}")

if __name__ == '__main__':
    # --- Hard-coded Path Configuration with Smart Environment Detection ---
    local_model_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    local_save_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/output_final_local.mp4')

    server_model_path = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt')
    server_video_path = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    server_save_path = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/output_final_server.mp4')

    if local_model_path.exists():
        print("Local environment detected.")
        model_path = local_model_path
        video_path = local_video_path
        save_path = local_save_path
        show_video = False
    elif server_model_path.exists():
        print("Server environment detected.")
        model_path = server_model_path
        video_path = server_video_path
        save_path = server_save_path
        show_video = False
    else:
        print("Error: Could not find model files for either local or server environment.")
        exit()
    
    run_tracker(model_path, video_path, save_path, show_video=show_video)
```

### 如何运行

**在任何环境下（本地或服务器），都使用以下简单命令：**

```bash
python D:/lkr_yolo/yolov11_vehicle_counter/src/main_final_version.py
```
*(请根据实际情况调整 `D:/lkr_yolo` 部分)*

---

## 3. 进展日志

- **2025-07-23 (V2.0):**
  - **重大重构:** 实现了模块化架构，创建了环境管理器、模型管理器和文件管理器
  - **环境管理:** 新增 `src/utils/environment_manager.py` - 自动检测本地/服务器环境并配置路径
  - **模型管理:** 新增 `src/utils/model_manager.py` - 处理YOLO和DeepSORT模型加载，包含错误处理
  - **文件管理:** 新增 `src/utils/file_manager.py` - 实现时间戳命名和实验记录功能
  - **配置管理:** 新增 `src/config/settings.py` - 集中管理所有配置参数
  - **Matrix错误修复:** 实现4层级DeepSORT配置尝试机制，从保守到紧急配置，彻底解决"Matrix is not positive definite"错误
  - **时间戳命名:** 输出视频现在自动添加时间戳，避免文件覆盖，便于科研对比
  - **实验记录:** 自动记录每次运行的参数和结果到 `logs/experiment_log.json`
  - **YOLO预测优化:** 直接使用优化参数进行预测，避免NMS超时问题
  - **运行时错误处理:** 在tracker.update()周围添加Matrix错误捕获，跳过问题帧继续处理
  - **错误处理优化:** 改进DeepSORT错误处理，统一处理Matrix错误和数值错误码
  - **状态:** 系统成功运行！能够处理视频并跳过问题帧，时间戳命名和实验记录功能正常工作

- **2025-07-22 (V1.1):**
  - **修复:** 解决了由于环境损坏导致的 `torchreid` 库的 `ModuleNotFoundError`。
  - **核心修改:**
    - **代码内联:** 将 `torchreid` 库中计算距离和提取特征的核心函数代码，直接提取并集成到了项目内部的 `src/tracker/sort/distance_metrics.py` 和 `src/tracker/feature_extractor.py` 文件中。
    - **移除依赖:** `deep_sort.py` 和 `nn_matching.py` 现在依赖于项目内部的模块，彻底移除了对外部 `torchreid` 库的依赖。
    - **手动跟踪循环:** `main_final_version.py` 现在使用手动的 `predict -> update` 循环，而不是依赖于 `ultralytics` 不稳定的 `track` 函数，增强了代码的长期稳定性。
  - **状态:** 已在本地和服务器上成功运行，功能符合预期，环境依赖问题已彻底解决。

- **2025-07-22 (V1.0):**
  - **创建:** 创建了终极版推理脚本 `main_final_version.py`。
  - **核心功能:**
    - 集成仪表盘，将计数信息与视频画面分离。
    - 脚本内置本地和服务器的双重路径配置，通过智能环境检测实现零参数运行。
    - 默认不显示视频窗口，改为直接保存结果视频，解决了本地OpenCV的GUI报错问题。
  - **状态:** 首次版本，后发现存在未解决的环境依赖问题。
