#!/bin/bash

# YOLOv11车辆计数系统一键部署脚本
# 包含所有必要文件和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "========================================"
echo "🚗 YOLOv11车辆计数系统一键部署"
echo "========================================"
echo "域名: vehicle.smart-traffic.top"
echo "服务器IP: ************"
echo "========================================"
echo

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
    log_error "请不要使用root用户运行此脚本"
    exit 1
fi

# 解压文件
log_info "解压项目文件..."
echo "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" | base64 -d > webapp_files.zip

# 创建项目目录
log_info "创建项目目录..."
sudo mkdir -p /var/www/vehicle-counter
sudo chown $USER:$USER /var/www/vehicle-counter

# 解压文件到项目目录
cd /var/www/vehicle-counter
unzip -o ~/webapp_files.zip
rm ~/webapp_files.zip

log_success "项目文件解压完成"

# 安装系统依赖
log_info "安装系统依赖..."
sudo apt update
sudo apt install -y python3-pip python3-venv python3-dev build-essential
sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg
sudo apt install -y supervisor apache2
sudo apt install -y certbot python3-certbot-apache

# 设置Python环境
log_info "设置Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn

# 创建Gunicorn配置
log_info "创建应用配置..."
cat > gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF

# 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
cd /var/www/vehicle-counter
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF

chmod +x start.sh

# 配置Apache虚拟主机
log_info "配置Apache虚拟主机..."
sudo a2enmod rewrite proxy proxy_http headers ssl

sudo tee /etc/apache2/sites-available/vehicle-counter.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    ServerAlias www.vehicle.smart-traffic.top
    
    DocumentRoot /var/www/vehicle-counter
    
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    LimitRequestBody 104857600
    
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${APACHE_LOG_DIR}/vehicle-counter_error.log
    CustomLog ${APACHE_LOG_DIR}/vehicle-counter_access.log combined
</VirtualHost>
EOF

sudo a2ensite vehicle-counter.conf
sudo systemctl reload apache2

# 配置Supervisor
log_info "配置Supervisor..."
sudo tee /etc/supervisor/conf.d/vehicle-counter.conf > /dev/null << 'EOF'
[program:vehicle-counter]
command=/var/www/vehicle-counter/start.sh
directory=/var/www/vehicle-counter
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/var/www/vehicle-counter/venv/bin"
EOF

sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start vehicle-counter

# 测试部署
log_info "测试部署..."
sleep 5
sudo supervisorctl status vehicle-counter
curl -s http://localhost:5000/api/health || echo "API测试失败"

echo
echo "========================================"
echo "🎉 部署完成！"
echo "========================================"
echo "🌐 网站地址: http://vehicle.smart-traffic.top"
echo "📊 健康检查: http://vehicle.smart-traffic.top/api/health"
echo "📝 应用日志: /var/log/vehicle-counter.log"
echo "========================================"
echo
echo "下一步:"
echo "1. 配置域名DNS解析: vehicle -> ************"
echo "2. 配置SSL证书: sudo certbot --apache -d vehicle.smart-traffic.top"
echo "========================================"
