#!/usr/bin/env python3
"""
运行UI修复版本的车辆计数器
修复了计数为0的问题，保持golden版本的检测逻辑
"""

import sys
from pathlib import Path

# 添加src目录到路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

if __name__ == "__main__":
    # 导入主模块并执行
    from main_ui_fixed import *
    
    # 直接调用主程序逻辑
    import argparse
    from datetime import datetime
    
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_ui_fixed_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - UI修复版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - UI修复版本")
    print("✨ 特性:")
    print("   📱 超大字体清晰计数显示")
    print("   🔧 修复计数为0的问题")
    print("   🎨 增强可视化效果")
    print("   📊 实时进度显示")
    print("=" * 50)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 50)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)