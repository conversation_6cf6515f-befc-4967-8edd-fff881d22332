<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLOv11 智能车辆计数系统</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-car"></i>
                <span>YOLOv11 车辆计数系统</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">首页</a>
                <a href="#upload" class="nav-link">上传视频</a>
                <a href="#results" class="nav-link">处理结果</a>
                <a href="#about" class="nav-link">关于</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 首页部分 -->
        <section id="home" class="section active">
            <div class="hero">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-eye"></i>
                        智能车辆计数系统
                    </h1>
                    <p class="hero-subtitle">
                        基于YOLOv11深度学习模型的高精度车辆检测与计数系统
                    </p>
                    <div class="hero-features">
                        <div class="feature-card">
                            <i class="fas fa-brain"></i>
                            <h3>AI智能识别</h3>
                            <p>YOLOv11最新模型，识别精度高达95%</p>
                        </div>
                        <div class="feature-card">
                            <i class="fas fa-chart-line"></i>
                            <h3>实时统计</h3>
                            <p>实时车辆计数，支持多种车辆类型</p>
                        </div>
                        <div class="feature-card">
                            <i class="fas fa-video"></i>
                            <h3>视频处理</h3>
                            <p>支持多种视频格式，1080p高清处理</p>
                        </div>
                    </div>
                    <button class="cta-button" onclick="showSection('upload')">
                        <i class="fas fa-upload"></i>
                        开始使用
                    </button>
                </div>
            </div>
        </section>

        <!-- 上传部分 -->
        <section id="upload" class="section">
            <div class="container">
                <h2 class="section-title">
                    <i class="fas fa-cloud-upload-alt"></i>
                    上传视频文件
                </h2>
                
                <div class="upload-area">
                    <div class="upload-zone" id="uploadZone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3>拖拽视频文件到此处</h3>
                        <p>或者点击选择文件</p>
                        <p class="upload-hint">支持 MP4, AVI, MOV, MKV 格式</p>
                        <input type="file" id="videoFile" accept="video/*" hidden>
                        <button class="upload-button" onclick="document.getElementById('videoFile').click()">
                            <i class="fas fa-folder-open"></i>
                            选择文件
                        </button>
                    </div>
                </div>

                <div class="upload-settings">
                    <h3><i class="fas fa-cog"></i> 处理设置</h3>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="modelSelect">检测模型:</label>
                            <select id="modelSelect">
                                <option value="yolov11m">YOLOv11m (已训练)</option>
                            </select>
                            <small style="color: #888;">使用专门训练的车辆检测模型</small>
                        </div>
                        <div class="setting-item">
                            <label for="confidenceSlider">置信度阈值:</label>
                            <input type="range" id="confidenceSlider" min="0.1" max="0.9" value="0.5" step="0.1">
                            <span id="confidenceValue">0.5</span>
                        </div>
                        <div class="setting-item">
                            <label for="processSpeed">处理速度:</label>
                            <select id="processSpeed">
                                <option value="fast">快速 (每10帧)</option>
                                <option value="normal" selected>正常 (每5帧)</option>
                                <option value="detailed">详细 (每帧)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="file-info" id="fileInfo" style="display: none;">
                    <h3><i class="fas fa-info-circle"></i> 文件信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">文件名:</span>
                            <span id="fileName"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">文件大小:</span>
                            <span id="fileSize"></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">预计处理时间:</span>
                            <span id="estimatedTime"></span>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="process-button" id="processButton" disabled onclick="startProcessing()">
                        <i class="fas fa-play"></i>
                        开始处理
                    </button>
                    <button class="reset-button" onclick="resetUpload()">
                        <i class="fas fa-redo"></i>
                        重置
                    </button>
                </div>
            </div>
        </section>

        <!-- 处理进度部分 -->
        <section id="processing" class="section">
            <div class="container">
                <h2 class="section-title">
                    <i class="fas fa-cogs"></i>
                    正在处理视频
                </h2>
                
                <div class="processing-container">
                    <div class="progress-circle">
                        <svg class="progress-ring" width="200" height="200">
                            <circle class="progress-ring-circle" cx="100" cy="100" r="90"></circle>
                        </svg>
                        <div class="progress-text">
                            <span id="progressPercent">0%</span>
                            <small>已完成</small>
                        </div>
                    </div>
                    
                    <div class="processing-info">
                        <div class="processing-stats">
                            <div class="stat-item">
                                <i class="fas fa-car"></i>
                                <span class="stat-label">检测车辆</span>
                                <span class="stat-value" id="vehicleCount">0</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span class="stat-label">检测人员</span>
                                <span class="stat-value" id="peopleCount">0</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span class="stat-label">处理时间</span>
                                <span class="stat-value" id="processingTime">00:00</span>
                            </div>
                        </div>
                        
                        <div class="processing-log">
                            <h4><i class="fas fa-list"></i> 处理日志</h4>
                            <div class="log-container" id="logContainer">
                                <div class="log-item">
                                    <span class="log-time">00:00:01</span>
                                    <span class="log-message">开始加载模型...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="processing-controls">
                    <button class="pause-button" id="pauseButton" onclick="pauseProcessing()">
                        <i class="fas fa-pause"></i>
                        暂停
                    </button>
                    <button class="stop-button" onclick="stopProcessing()">
                        <i class="fas fa-stop"></i>
                        停止
                    </button>
                </div>
            </div>
        </section>

        <!-- 结果部分 -->
        <section id="results" class="section">
            <div class="container">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    处理结果
                </h2>
                
                <div class="results-container">
                    <!-- 统计卡片 -->
                    <div class="stats-cards">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalVehicles">0</h3>
                                <p>总车辆数</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-arrow-left"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="leftVehicles">0</h3>
                                <p>左侧通过</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="rightVehicles">0</h3>
                                <p>右侧通过</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalPeople">0</h3>
                                <p>检测人员</p>
                            </div>
                        </div>
                    </div>

                    <!-- 视频播放器 -->
                    <div class="video-player-container">
                        <div class="video-player">
                            <video id="resultVideo" controls>
                                <source src="" type="video/mp4">
                                您的浏览器不支持视频播放。
                            </video>
                            <div class="video-controls">
                                <button class="video-btn" onclick="togglePlay()">
                                    <i class="fas fa-play" id="playIcon"></i>
                                </button>
                                <div class="video-progress">
                                    <input type="range" id="progressBar" min="0" max="100" value="0">
                                </div>
                                <button class="video-btn" onclick="toggleFullscreen()">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 详细分析 -->
                    <div class="analysis-container">
                        <div class="analysis-tabs">
                            <button class="tab-button active" onclick="showTab('timeline')">
                                <i class="fas fa-chart-line"></i>
                                时间线分析
                            </button>
                            <button class="tab-button" onclick="showTab('heatmap')">
                                <i class="fas fa-fire"></i>
                                热力图
                            </button>
                            <button class="tab-button" onclick="showTab('details')">
                                <i class="fas fa-list-alt"></i>
                                详细数据
                            </button>
                        </div>
                        
                        <div class="tab-content">
                            <div id="timeline" class="tab-pane active">
                                <canvas id="timelineChart" width="800" height="400"></canvas>
                            </div>
                            <div id="heatmap" class="tab-pane">
                                <div class="heatmap-container">
                                    <canvas id="heatmapCanvas" width="800" height="450"></canvas>
                                </div>
                            </div>
                            <div id="details" class="tab-pane">
                                <div class="details-table">
                                    <table id="detailsTable">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>ID</th>
                                                <th>类型</th>
                                                <th>方向</th>
                                                <th>置信度</th>
                                            </tr>
                                        </thead>
                                        <tbody id="detailsTableBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 下载选项 -->
                    <div class="download-section">
                        <h3><i class="fas fa-download"></i> 下载结果</h3>
                        <div class="download-options">
                            <button class="download-btn" onclick="downloadVideo()">
                                <i class="fas fa-video"></i>
                                下载处理后视频
                            </button>
                            <button class="download-btn" onclick="downloadReport()">
                                <i class="fas fa-file-pdf"></i>
                                下载分析报告
                            </button>
                            <button class="download-btn" onclick="downloadData()">
                                <i class="fas fa-table"></i>
                                下载原始数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于部分 -->
        <section id="about" class="section">
            <div class="container">
                <h2 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    关于系统
                </h2>
                
                <div class="about-content">
                    <div class="about-text">
                        <h3>技术特点</h3>
                        <ul>
                            <li><strong>YOLOv11模型:</strong> 最新的目标检测算法，识别精度高</li>
                            <li><strong>实时跟踪:</strong> 支持多目标跟踪，ID持续性好</li>
                            <li><strong>智能计数:</strong> 基于轨迹的穿越检测，避免重复计数</li>
                            <li><strong>多格式支持:</strong> 支持MP4、AVI、MOV等主流视频格式</li>
                            <li><strong>高清处理:</strong> 专为1080p视频优化</li>
                        </ul>
                        
                        <h3>应用场景</h3>
                        <ul>
                            <li>交通流量统计</li>
                            <li>停车场管理</li>
                            <li>道路监控分析</li>
                            <li>城市规划数据收集</li>
                        </ul>
                    </div>
                    
                    <div class="tech-stack">
                        <h3>技术栈</h3>
                        <div class="tech-items">
                            <div class="tech-item">
                                <i class="fab fa-python"></i>
                                <span>Python</span>
                            </div>
                            <div class="tech-item">
                                <i class="fas fa-brain"></i>
                                <span>YOLOv11</span>
                            </div>
                            <div class="tech-item">
                                <i class="fab fa-html5"></i>
                                <span>HTML5</span>
                            </div>
                            <div class="tech-item">
                                <i class="fab fa-css3-alt"></i>
                                <span>CSS3</span>
                            </div>
                            <div class="tech-item">
                                <i class="fab fa-js-square"></i>
                                <span>JavaScript</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 YOLOv11 智能车辆计数系统. All rights reserved.</p>
        </div>
    </footer>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在处理，请稍候...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="static/js/main.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>