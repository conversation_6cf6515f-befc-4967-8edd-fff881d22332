@echo off
echo ========================================
echo    Vehicle Counter - UI Optimized
echo ========================================
echo.

cd src

REM 检测环境并设置路径
if exist "D:\lkr_yolo\yolov11_vehicle_counter\runs\train\yolov11m_vehicle_detection_portable\weights\best.pt" (
    echo 检测到本地环境
    set MODEL_PATH=D:\lkr_yolo\yolov11_vehicle_counter\runs\train\yolov11m_vehicle_detection_portable\weights\best.pt
    set VIDEO_PATH=D:\lkr_yolo\yolov11_vehicle_counter\video\3007580-uhd_3840_2160_30fps.mp4
    set REID_MODEL=tracker/deep/osnet模型/osnet_x1_0_imagenet.pth
) else (
    echo 检测到服务器环境
    set MODEL_PATH=C:\lkr_yolo\lkr_yolo\yolov11_vehicle_counter\runs\train\yolov11m_vehicle_detection\weights\best.pt
    set VIDEO_PATH=C:\lkr_yolo\lkr_yolo\yolov11_vehicle_counter\video\3007580-uhd_3840_2160_30fps.mp4
    set REID_MODEL=tracker/deep/osnet模型/osnet_x1_0_imagenet.pth
)

echo 使用模型: %MODEL_PATH%
echo 处理视频: %VIDEO_PATH%
echo ReID模型: %REID_MODEL%
echo.

REM 运行优化版本
python main_ui_optimized.py ^
    --yolo_model "%MODEL_PATH%" ^
    --deep_sort_model "%REID_MODEL%" ^
    --source "%VIDEO_PATH%" ^
    --save-vid ^
    --conf-thres 0.5 ^
    --iou-thres 0.7 ^
    --max-det 300 ^
    --device cpu

echo.
echo 处理完成！检查 runs/track 目录查看结果。
pause