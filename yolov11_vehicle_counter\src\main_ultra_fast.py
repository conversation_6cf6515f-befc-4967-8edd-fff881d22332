#!/usr/bin/env python3
"""
超高速车辆计数主程序
专门优化FPS性能，目标15+ FPS
"""

import os
import sys
import cv2
import numpy as np
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from ultralytics import YOLO
    from utils.environment_manager import get_environment_manager
    from utils.model_manager import get_model_manager
    from utils.file_manager import get_file_manager
    from utils.performance_monitor import get_performance_monitor
    from config.settings import settings
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在src目录下运行脚本")
    sys.exit(1)

# --- 超高速配置 ---
class UltraFastConfig:
    """超高速配置类"""
    
    def __init__(self):
        # 从环境变量读取配置，如果没有则使用默认值
        self.conf_threshold = float(os.getenv('YOLO_CONF_THRESHOLD', '0.7'))
        self.iou_threshold = float(os.getenv('YOLO_IOU_THRESHOLD', '0.8'))
        self.max_detections = int(os.getenv('YOLO_MAX_DETECTIONS', '50'))
        self.img_size = int(os.getenv('YOLO_IMG_SIZE', '416'))
        self.deepsort_max_age = int(os.getenv('DEEPSORT_MAX_AGE', '5'))
        self.deepsort_nn_budget = int(os.getenv('DEEPSORT_NN_BUDGET', '10'))
        self.skip_frames = int(os.getenv('SKIP_FRAMES', '2'))
        self.ultra_fast_mode = os.getenv('ULTRA_FAST_MODE', '0') == '1'
        
        # 显示配置
        if self.ultra_fast_mode:
            print("⚡ 超高速模式已启用")
            print(f"   置信度: {self.conf_threshold}")
            print(f"   IOU: {self.iou_threshold}")
            print(f"   最大检测: {self.max_detections}")
            print(f"   图像尺寸: {self.img_size}")
            print(f"   跳帧: {self.skip_frames}")

ultra_config = UltraFastConfig()

# --- 全局变量 ---
track_history = {}
counted_ids = set()

def run_ultra_fast_tracker(model_path, video_path, save_path, show_video=False):
    """超高速跟踪器运行函数"""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    # 使用模型管理器加载YOLO模型
    model_mgr = get_model_manager()
    print(f"Loading model from: {model_path}")
    
    # 获取备用模型路径
    env_manager = get_environment_manager()
    model_paths = env_manager.get_model_paths()
    fallback_paths = [path for key, path in model_paths.items() if path != model_path and path.exists()]
    
    try:
        model = model_mgr.load_yolo_model(Path(model_path), fallback_paths)
        
        # 超高速优化：设置模型为半精度
        if ultra_config.ultra_fast_mode:
            try:
                model.model.half()  # 使用FP16半精度
                print("✅ 启用FP16半精度加速")
            except:
                print("⚠️  FP16不可用，使用FP32")
        
        logger.info("YOLO模型加载成功")
    except Exception as e:
        logger.error(f"YOLO模型加载失败: {e}")
        return

    # --- 简化的跟踪器初始化 ---
    # 在超高速模式下，我们使用更简单的跟踪策略
    if ultra_config.ultra_fast_mode:
        print("⚡ 使用简化跟踪策略")
        tracker = None  # 不使用DeepSORT，使用简单的中心点跟踪
    else:
        # 正常DeepSORT初始化
        reid_model_path = env_manager.get_reid_model_path()
        try:
            deepsort_mgr = model_mgr.load_deepsort_tracker(reid_model_path)
            tracker = deepsort_mgr
            logger.info("DeepSORT管理器加载成功")
        except Exception as e:
            logger.error(f"DeepSORT管理器加载失败: {e}")
            tracker = None

    # --- 视频处理设置 ---
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)

    # 超高速模式：降低输出分辨率
    if ultra_config.ultra_fast_mode:
        output_scale = 0.5  # 输出分辨率减半
        output_w, output_h = int(w * output_scale), int(h * output_scale)
        print(f"⚡ 输出分辨率优化: {w}x{h} -> {output_w}x{output_h}")
    else:
        output_w, output_h = w, h

    # --- 仪表板配置 ---
    dashboard_height = 60 if ultra_config.ultra_fast_mode else 100
    new_h = output_h + dashboard_height

    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (output_w, new_h))
    print(f"Output video will be saved to: {save_path}")

    # 初始化性能监控器
    perf_monitor = get_performance_monitor()
    
    # 简单的跟踪器（用于超高速模式）
    simple_tracks = {}
    next_id = 1
    
    frame_idx = 0
    processed_frames = 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("Video processing completed.")
            break
        frame_idx += 1
        
        # 跳帧处理
        if ultra_config.ultra_fast_mode and frame_idx % (ultra_config.skip_frames + 1) != 0:
            continue
        
        processed_frames += 1
        
        # 开始性能监控
        frame_start_time = perf_monitor.start_frame_processing()
        
        # 超高速模式：降低处理分辨率
        if ultra_config.ultra_fast_mode:
            process_frame = cv2.resize(frame, (ultra_config.img_size, ultra_config.img_size))
        else:
            process_frame = frame

        # --- YOLO检测 ---
        try:
            results = model.predict(
                process_frame, 
                conf=ultra_config.conf_threshold,
                iou=ultra_config.iou_threshold,
                max_det=ultra_config.max_detections,
                verbose=False,
                imgsz=ultra_config.img_size
            )
        except Exception as e:
            logger.warning(f"帧 {processed_frames} 预测失败: {e}")
            continue
            
        detections = results[0].boxes.data.cpu().numpy()

        # 缩放检测结果回原始尺寸
        if ultra_config.ultra_fast_mode and len(detections) > 0:
            scale_x = w / ultra_config.img_size
            scale_y = h / ultra_config.img_size
            detections[:, [0, 2]] *= scale_x  # x坐标
            detections[:, [1, 3]] *= scale_y  # y坐标

        # --- 跟踪处理 ---
        if ultra_config.ultra_fast_mode:
            # 简单的中心点跟踪
            tracks = simple_center_tracking(detections, simple_tracks, next_id)
            if len(tracks) > len(simple_tracks):
                next_id = max([t[4] for t in tracks] + [next_id]) + 1
        else:
            # 使用DeepSORT
            if tracker and len(detections) > 0:
                xywhs = detections[:, :4]
                confs = detections[:, 4]
                clss = detections[:, 5]
                tracks = tracker.safe_update(xywhs, confs, clss, frame, processed_frames)
            else:
                tracks = []

        # --- 创建输出画布 ---
        if ultra_config.ultra_fast_mode:
            display_frame = cv2.resize(frame, (output_w, output_h))
        else:
            display_frame = frame
            
        main_canvas = np.zeros((new_h, output_w, 3), dtype=np.uint8)
        main_canvas[:dashboard_height, :] = (40, 40, 40)  # 仪表板
        main_canvas[dashboard_height:, :] = display_frame

        # --- 绘制计数线 ---
        scaled_line_x = int(line_x * (output_w / w)) if ultra_config.ultra_fast_mode else line_x
        cv2.line(main_canvas, (scaled_line_x, dashboard_height), (scaled_line_x, new_h), (0, 255, 0), 2)

        # --- 处理跟踪结果和计数 ---
        for track in tracks:
            if len(track) >= 5:
                x1, y1, x2, y2, track_id = track[:5]
                
                # 缩放坐标
                if ultra_config.ultra_fast_mode:
                    x1 = int(x1 * output_w / w)
                    y1 = int(y1 * output_h / h) + dashboard_height
                    x2 = int(x2 * output_w / w)
                    y2 = int(y2 * output_h / h) + dashboard_height
                else:
                    y1 += dashboard_height
                    y2 += dashboard_height

                # 绘制边界框
                cv2.rectangle(main_canvas, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 1)
                cv2.putText(main_canvas, f'ID:{int(track_id)}', (int(x1), int(y1)-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                # 计数逻辑
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                if track_id not in track_history:
                    track_history[track_id] = []
                track_history[track_id].append((center_x, center_y))
                
                if len(track_history[track_id]) > 1:
                    prev_x = track_history[track_id][-2][0]
                    curr_x = track_history[track_id][-1][0]
                    
                    if track_id not in counted_ids:
                        if prev_x < scaled_line_x < curr_x:  # 从左到右
                            right_count += 1
                            counted_ids.add(track_id)
                        elif prev_x > scaled_line_x > curr_x:  # 从右到左
                            left_count += 1
                            counted_ids.add(track_id)

        # --- 绘制计数信息 ---
        cv2.putText(main_canvas, f'Left: {left_count}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(main_canvas, f'Right: {right_count}', (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        # 写入视频
        out.write(main_canvas)
        
        # 结束性能监控
        perf_metrics = perf_monitor.end_frame_processing(frame_start_time, processed_frames)

    # 释放资源
    cap.release()
    out.release()
    
    # 安全地关闭OpenCV窗口
    try:
        cv2.destroyAllWindows()
    except Exception as e:
        logger.warning(f"关闭OpenCV窗口时出现错误（可忽略）: {e}")

    # 获取性能报告
    perf_report = perf_monitor.get_performance_report()
    
    # 记录实验结果
    file_mgr = get_file_manager()
    experiment_data = {
        'experiment_id': file_mgr.generate_experiment_id(),
        'model_path': str(model_path),
        'video_path': str(video_path),
        'output_path': str(save_path),
        'left_count': left_count,
        'right_count': right_count,
        'total_count': left_count + right_count,
        'processed_frames': processed_frames,
        'total_frames': frame_idx,
        'ultra_fast_mode': ultra_config.ultra_fast_mode,
        'performance_metrics': perf_report
    }
    file_mgr.log_experiment(experiment_data)
    
    print(f"Final Counts -> Left: {left_count}, Right: {right_count}")
    print(f"Output video saved successfully to {save_path}")
    print(f"🚀 Ultra Fast Performance - Average FPS: {perf_report['average_fps']:.1f}, Peak Memory: {perf_report['peak_memory_mb']:.1f}MB")
    print(f"📊 Processed {processed_frames}/{frame_idx} frames (skip ratio: {ultra_config.skip_frames})")
    
    logger.info(f"超高速实验完成 - 总计数: {left_count + right_count}, 输出: {save_path}")
    logger.info(f"性能报告 - 平均FPS: {perf_report['average_fps']:.1f}, 峰值内存: {perf_report['peak_memory_mb']:.1f}MB")

def simple_center_tracking(detections, tracks, next_id):
    """简单的中心点跟踪算法"""
    if len(detections) == 0:
        return []
    
    current_centers = []
    for det in detections:
        x1, y1, x2, y2 = det[:4]
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        current_centers.append((center_x, center_y, x1, y1, x2, y2))
    
    # 简单的距离匹配
    matched_tracks = []
    used_detections = set()
    
    for track_id, (prev_x, prev_y, _, _, _, _) in tracks.items():
        best_match = None
        best_distance = float('inf')
        
        for i, (curr_x, curr_y, x1, y1, x2, y2) in enumerate(current_centers):
            if i in used_detections:
                continue
                
            distance = np.sqrt((curr_x - prev_x)**2 + (curr_y - prev_y)**2)
            if distance < best_distance and distance < 100:  # 距离阈值
                best_distance = distance
                best_match = i
        
        if best_match is not None:
            used_detections.add(best_match)
            curr_x, curr_y, x1, y1, x2, y2 = current_centers[best_match]
            matched_tracks.append([x1, y1, x2, y2, track_id])
            tracks[track_id] = (curr_x, curr_y, x1, y1, x2, y2)
    
    # 为未匹配的检测创建新轨迹
    for i, (curr_x, curr_y, x1, y1, x2, y2) in enumerate(current_centers):
        if i not in used_detections:
            matched_tracks.append([x1, y1, x2, y2, next_id])
            tracks[next_id] = (curr_x, curr_y, x1, y1, x2, y2)
            next_id += 1
    
    return matched_tracks

if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # 使用环境管理器自动检测环境
        env_manager = get_environment_manager()
        config = env_manager.detect_environment()
        
        # 验证依赖
        missing_deps = env_manager.validate_dependencies()
        if missing_deps:
            logger.warning(f"发现缺失依赖: {missing_deps}")
        
        # 确保输出目录存在
        env_manager.ensure_output_directories()
        
        # 获取路径
        model_paths = env_manager.get_model_paths()
        video_paths = env_manager.get_video_paths()
        output_paths = env_manager.get_output_paths()
        
        # 设置路径
        model_path = model_paths['yolo_best']
        video_path = video_paths['input']
        
        # 使用文件管理器生成带时间戳的输出路径
        file_mgr = get_file_manager()
        save_path = file_mgr.create_timestamped_output_path(
            output_paths['video'], 
            "output_ultra_fast", 
            config.environment_type
        )
        
        logger.info(f"环境类型: {config.environment_type}")
        logger.info(f"模型路径: {model_path}")
        logger.info(f"视频路径: {video_path}")
        logger.info(f"输出路径: {save_path}")
        
        # 运行超高速跟踪器
        run_ultra_fast_tracker(model_path, video_path, save_path, show_video=False)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"错误: {e}")
        exit(1)