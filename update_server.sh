#!/bin/bash

# 更新服务器脚本 - 将新版本部署到生产环境
echo "=========================================="
echo "🔄 更新YOLOv11车辆计数器到新版本"
echo "=========================================="

# 停止当前服务
echo "⏹️  停止当前服务..."
sudo supervisorctl stop vehicle-counter

# 备份当前版本
echo "💾 备份当前版本..."
sudo cp -r /var/www/vehicle-counter /var/www/vehicle-counter-backup-$(date +%Y%m%d_%H%M%S)

# 复制新版本文件
echo "📁 复制新版本文件..."
cp ~/webapp/app.py /var/www/vehicle-counter/
cp ~/webapp/deploy_config.py /var/www/vehicle-counter/
cp ~/webapp/templates/index.html /var/www/vehicle-counter/
cp -r ~/webapp/static/* /var/www/vehicle-counter/static/

# 更新requirements.txt
echo "📦 更新依赖..."
cp ~/webapp/requirements.txt /var/www/vehicle-counter/
cd /var/www/vehicle-counter
source venv/bin/activate
pip install -r requirements.txt

# 设置权限
echo "🔐 设置权限..."
sudo chown -R admin:admin /var/www/vehicle-counter
sudo chmod -R 755 /var/www/vehicle-counter

# 重启服务
echo "🚀 重启服务..."
sudo supervisorctl start vehicle-counter

# 检查状态
echo "✅ 检查服务状态..."
sudo supervisorctl status vehicle-counter

echo "=========================================="
echo "🎉 更新完成！"
echo "🌐 访问地址: https://vehicle.smart-traffic.top"
echo "=========================================="
