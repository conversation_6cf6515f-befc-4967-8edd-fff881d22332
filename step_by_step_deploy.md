# 分步骤部署指南

## 🚀 快速部署 - 跟着做就行！

### 第一步：上传项目文件

#### 方法A：使用WinSCP (推荐)
1. **下载WinSCP**: https://winscp.net/eng/download.php
2. **连接服务器**:
   - 主机名: `************`
   - 用户名: `admin`
   - 密码: [你的服务器密码]
   - 端口: `22`
3. **上传文件**:
   - 在右侧创建目录: `/var/www/vehicle-counter/`
   - 上传以下文件到该目录:
     - `yolov11_vehicle_counter/webapp/app.py`
     - `yolov11_vehicle_counter/webapp/index.html`
     - `yolov11_vehicle_counter/webapp/requirements.txt`
     - `yolov11_vehicle_counter/webapp/static/` (整个文件夹)

#### 方法B：使用命令行
```bash
# 每次执行都需要输入密码
scp yolov11_vehicle_counter/webapp/app.py admin@************:~/
scp yolov11_vehicle_counter/webapp/index.html admin@************:~/
scp yolov11_vehicle_counter/webapp/requirements.txt admin@************:~/
scp -r yolov11_vehicle_counter/webapp/static admin@************:~/
```

### 第二步：登录服务器执行部署

```bash
# 登录服务器
ssh admin@************
```

登录后，复制粘贴以下命令（一次一个命令）：

#### 1. 创建项目目录
```bash
sudo mkdir -p /var/www/vehicle-counter
sudo chown admin:admin /var/www/vehicle-counter
```

#### 2. 移动文件到项目目录
```bash
mv ~/app.py /var/www/vehicle-counter/
mv ~/index.html /var/www/vehicle-counter/
mv ~/requirements.txt /var/www/vehicle-counter/
mv ~/static /var/www/vehicle-counter/
```

#### 3. 安装系统依赖
```bash
sudo apt update
sudo apt install -y python3-pip python3-venv python3-dev build-essential
sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg
sudo apt install -y supervisor apache2
```

#### 4. 设置Python环境
```bash
cd /var/www/vehicle-counter
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn
```

#### 5. 创建Gunicorn配置
```bash
cat > /var/www/vehicle-counter/gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF
```

#### 6. 创建启动脚本
```bash
cat > /var/www/vehicle-counter/start.sh << 'EOF'
#!/bin/bash
cd /var/www/vehicle-counter
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF

chmod +x /var/www/vehicle-counter/start.sh
```

#### 7. 配置Apache虚拟主机
```bash
sudo a2enmod rewrite proxy proxy_http headers ssl

sudo tee /etc/apache2/sites-available/vehicle-counter.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    ServerAlias www.vehicle.smart-traffic.top
    
    DocumentRoot /var/www/vehicle-counter
    
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    LimitRequestBody 104857600
    
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${APACHE_LOG_DIR}/vehicle-counter_error.log
    CustomLog ${APACHE_LOG_DIR}/vehicle-counter_access.log combined
</VirtualHost>
EOF

sudo a2ensite vehicle-counter.conf
sudo systemctl reload apache2
```

#### 8. 配置Supervisor
```bash
sudo tee /etc/supervisor/conf.d/vehicle-counter.conf > /dev/null << 'EOF'
[program:vehicle-counter]
command=/var/www/vehicle-counter/start.sh
directory=/var/www/vehicle-counter
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/var/www/vehicle-counter/venv/bin"
EOF

sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start vehicle-counter
```

#### 9. 测试部署
```bash
# 检查应用状态
sudo supervisorctl status vehicle-counter

# 测试API
curl http://localhost:5000/api/health

# 查看日志
sudo tail -f /var/log/vehicle-counter.log
```

### 第三步：配置域名解析

在你的域名管理面板添加A记录：
```
记录类型: A
主机记录: vehicle
记录值: ************
TTL: 600
```

### 第四步：测试访问

等待DNS解析生效后（通常5-10分钟），访问：
- http://vehicle.smart-traffic.top

### 第五步：配置SSL证书（可选）

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-apache

# 获取SSL证书
sudo certbot --apache -d vehicle.smart-traffic.top

# 测试自动续期
sudo certbot renew --dry-run
```

## 🔧 常用管理命令

```bash
# 重启应用
sudo supervisorctl restart vehicle-counter

# 查看应用状态
sudo supervisorctl status vehicle-counter

# 查看应用日志
sudo tail -f /var/log/vehicle-counter.log

# 查看Apache日志
sudo tail -f /var/log/apache2/vehicle-counter_error.log

# 重启Apache
sudo systemctl restart apache2

# 检查Apache配置
sudo apache2ctl configtest
```

## 🚨 故障排除

### 1. 应用无法启动
```bash
# 检查Python环境
cd /var/www/vehicle-counter
source venv/bin/activate
python app.py

# 检查依赖
pip list | grep -E "(flask|ultralytics|opencv)"
```

### 2. 域名无法访问
```bash
# 检查DNS解析
nslookup vehicle.smart-traffic.top

# 检查Apache状态
sudo systemctl status apache2

# 检查端口
sudo netstat -tlnp | grep :80
```

### 3. 文件上传失败
```bash
# 检查磁盘空间
df -h

# 检查权限
ls -la /var/www/vehicle-counter/

# 检查临时目录
ls -la /tmp/
```

## 🎉 部署完成！

部署成功后，你的网站将在以下地址可用：
- **HTTP**: http://vehicle.smart-traffic.top
- **HTTPS**: https://vehicle.smart-traffic.top (配置SSL后)

现在你可以开始使用YOLOv11智能车辆计数系统了！
