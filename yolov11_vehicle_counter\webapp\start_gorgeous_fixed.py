#!/usr/bin/env python3
"""
启动修复后的华丽版Web应用
"""

import os
import sys
import time
import webbrowser
import threading
from pathlib import Path

def main():
    """主函数"""
    print("🌟 华丽版车辆计数器 - 修复版")
    print("=" * 50)
    print("✨ 修复内容:")
    print("   🔧 JavaScript文件引用修复")
    print("   🎯 按钮点击事件修复")
    print("   🎨 华丽版界面完整保留")
    print("   🚀 AI分析功能正常")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("app.py").exists():
        print("❌ 请在webapp目录中运行此脚本")
        return
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5000')
            print("🌐 浏览器已打开")
        except:
            print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:5000")
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 导入并启动应用
        from app import app
        
        print("🚀 华丽版Web服务器启动中...")
        print("🌐 访问地址: http://localhost:5000")
        print("\n🎨 界面特色:")
        print("   ✨ 动态粒子背景")
        print("   🌈 玻璃拟态设计")
        print("   💫 流畅动画效果")
        print("   📱 响应式布局")
        
        print("\n🧪 测试步骤:")
        print("   1. 点击'选择视频文件'按钮")
        print("   2. 选择一个MP4视频文件")
        print("   3. 点击'开始智能分析'按钮")
        print("   4. 观察实时处理进度")
        
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查:")
        print("   - 依赖包是否安装 (pip install ultralytics opencv-python flask)")
        print("   - 端口5000是否被占用")

if __name__ == "__main__":
    main()
