#!/usr/bin/env python3
"""
云服务器启动脚本
确保在任何环境下都能正常启动Web应用
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'opencv-python',
        'numpy',
        'ultralytics',
        'werkzeug'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """设置环境变量"""
    # 解决OpenMP冲突
    os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
    
    # 设置OpenCV后端
    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
    
    # 设置CUDA可见性（如果有GPU）
    if 'CUDA_VISIBLE_DEVICES' not in os.environ:
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    
    print("✅ 环境变量已设置")

def check_ports():
    """检查端口是否可用"""
    import socket
    
    def is_port_available(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('', port))
                return True
            except OSError:
                return False
    
    port = 5000
    if is_port_available(port):
        print(f"✅ 端口 {port} 可用")
        return True
    else:
        print(f"❌ 端口 {port} 被占用")
        # 尝试其他端口
        for alt_port in [5001, 5002, 8000, 8080]:
            if is_port_available(alt_port):
                print(f"✅ 使用替代端口 {alt_port}")
                return alt_port
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🚀 YOLOv11车辆计数器 - 云服务器启动脚本")
    print("=" * 70)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查依赖包
    print("\n📦 检查依赖包...")
    if not check_dependencies():
        return False
    
    # 设置环境
    print("\n🔧 设置环境...")
    setup_environment()
    
    # 检查端口
    print("\n🌐 检查端口...")
    port_result = check_ports()
    if not port_result:
        print("❌ 无可用端口")
        return False
    
    # 启动应用
    print("\n🎯 启动Web应用...")
    try:
        # 导入并启动应用
        from app import app
        from deploy_config import deploy_config
        
        # 获取服务器配置
        server_config = deploy_config.get_server_config()
        
        # 如果端口被占用，使用替代端口
        if isinstance(port_result, int) and port_result != 5000:
            server_config['port'] = port_result
        
        print(f"🌐 服务器地址: http://0.0.0.0:{server_config['port']}")
        print("🎯 AI功能已就绪，可以进行车辆检测和计数")
        print("=" * 70)
        
        # 启动Flask应用
        app.run(**server_config)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保在正确的目录中运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 启动失败，请检查上述错误信息")
        input("按回车键退出...")
        sys.exit(1)
