import argparse
import os
from ultralytics.models.yolo.detect import DetectionTrainer

def main():
    parser = argparse.ArgumentParser(description='YOLOv11n 高级训练脚本 (UltraThink)')
    parser.add_argument('--data', type=str, default='../configs/data.yaml', help='数据集配置文件')
    parser.add_argument('--weights', type=str, default='../models/weights/yolo11n.pt', help='预训练权重文件')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮数')
    parser.add_argument('--patience', type=int, default=200, help='提前停止patience')
    parser.add_argument('--batch', type=int, default=4, help='批次大小')
    parser.add_argument('--img', type=int, default=1024, help='输入图片尺寸')
    parser.add_argument('--project', type=str, default="../results", help='训练结果输出目录')
    parser.add_argument('--name', type=str, default="exp_yolov11n", help='实验名称')
    parser.add_argument('--resume', action='store_true', help='断点续训')
    parser.add_argument('--device', type=str, default='', help='训练设备（如0,1或cpu）')
    args = parser.parse_args()

    # 构建训练参数
    train_args = dict(
        model=args.weights,
        data=args.data,
        epochs=args.epochs,
        patience=args.patience,
        batch=args.batch,
        imgsz=args.img,
        project=args.project,
        name=args.name,
        resume=args.resume,
        device=args.device,
        exist_ok=True,
        verbose=True,
    )

    print("=== UltraThink YOLOv11n 训练参数 ===")
    for k, v in train_args.items():
        print(f"{k}: {v}")
    print("==============================")

    # 启动训练
    trainer = DetectionTrainer(overrides=train_args)
    trainer.train()

if __name__ == '__main__':
    main() 