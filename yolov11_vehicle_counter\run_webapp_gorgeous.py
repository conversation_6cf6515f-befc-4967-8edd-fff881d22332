#!/usr/bin/env python3
"""
启动华丽版YOLOv11车辆计数器Web应用
现代化设计，华丽视觉效果，专业级用户体验
"""

import sys
import shutil
import webbrowser
import time
from pathlib import Path

def setup_gorgeous_webapp():
    """设置华丽版Web应用"""
    print("✨ 设置华丽版Web应用...")
    
    webapp_dir = Path("webapp")
    
    # 备份原始文件
    backup_files = [
        ("index.html", "index_backup_gorgeous.html"),
        ("static/js/main.js", "static/js/main_backup_gorgeous.js"),
        ("app.py", "app_backup_gorgeous.py")
    ]
    
    for original, backup in backup_files:
        original_path = webapp_dir / original
        backup_path = webapp_dir / backup
        if original_path.exists():
            shutil.copy(original_path, backup_path)
            print(f"✅ 已备份 {original}")
    
    # 使用华丽版文件
    gorgeous_files = [
        ("index_gorgeous.html", "index.html"),
        ("static/js/main_gorgeous.js", "static/js/main.js"),
        ("app_golden_web.py", "app.py")  # 使用黄金版后端
    ]
    
    for source, target in gorgeous_files:
        source_path = webapp_dir / source
        target_path = webapp_dir / target
        if source_path.exists():
            shutil.copy(source_path, target_path)
            print(f"✅ 已应用华丽版 {target}")
        else:
            print(f"⚠️  华丽版文件不存在: {source}")
    
    # 创建模板目录链接
    templates_dir = webapp_dir / "templates"
    if not templates_dir.exists():
        templates_dir.mkdir(exist_ok=True)
    
    # 复制华丽版模板
    gorgeous_template = webapp_dir / "index_gorgeous.html"
    template_target = templates_dir / "index.html"
    if gorgeous_template.exists():
        shutil.copy(gorgeous_template, template_target)
        print("✅ 已创建华丽版模板")
    
    return True

def main():
    """主函数"""
    print("🌟 YOLOv11车辆计数器 - 华丽版Web应用")
    print("=" * 70)
    print("✨ 华丽版特色:")
    print("   🎨 现代化玻璃拟态设计")
    print("   🌈 动态渐变背景和粒子效果")
    print("   💫 流畅的动画和过渡效果")
    print("   🎯 专业级AI分析界面")
    print("   📱 响应式布局适配")
    print("   🔥 华丽的视觉效果")
    print("   ⚡ 高性能用户体验")
    print("=" * 70)
    
    # 检查webapp目录
    webapp_dir = Path("webapp")
    if not webapp_dir.exists():
        print(f"❌ webapp目录不存在: {webapp_dir}")
        return
    
    # 设置华丽版
    if not setup_gorgeous_webapp():
        print("❌ 华丽版设置失败")
        return
    
    print("\\n🚀 启动华丽版Web服务器...")
    print("🌐 访问地址: http://localhost:5000")
    print("\\n🎨 界面布局:")
    print("   🎛️  左侧: 智能控制中心")
    print("   🎬 中央: 视频预览和对比分析")
    print("   📊 右侧: 实时统计和数据下载")
    
    print("\\n✨ 视觉特效:")
    print("   🌟 动态粒子背景")
    print("   🌈 多层渐变色彩")
    print("   💎 玻璃拟态卡片")
    print("   ⚡ 流畅动画过渡")
    print("   🎯 悬停交互效果")
    print("   📱 响应式适配")
    
    print("\\n🤖 AI功能:")
    print("   🔍 YOLOv11深度学习检测")
    print("   🚗 多类型车辆识别")
    print("   📏 智能轨迹跟踪")
    print("   📊 实时统计分析")
    print("   🎨 可视化标注输出")
    print("   📹 高质量视频处理")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5000')
            print("\\n🌐 浏览器已打开，准备体验华丽界面！")
        except:
            print("\\n⚠️  无法自动打开浏览器，请手动访问 http://localhost:5000")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 切换到webapp目录并启动
        import os
        os.chdir("webapp")
        
        # 导入并启动华丽版app
        sys.path.insert(0, ".")
        from app import app
        
        print("\\n" + "="*70)
        print("🌟 华丽版Web服务器已启动")
        print("✨ 享受现代化的AI分析体验")
        print("按 Ctrl+C 停止服务器")
        print("="*70)
        
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        print("\\n🛑 华丽版服务器已停止")
    except Exception as e:
        print(f"\\n❌ 启动失败: {e}")
        print("💡 请检查:")
        print("   - 依赖包是否正确安装 (pip install ultralytics opencv-python flask)")
        print("   - 端口5000是否被占用")
        print("   - 华丽版文件是否存在")
        print("   - 现代浏览器支持 (Chrome/Firefox/Edge)")

if __name__ == "__main__":
    main()