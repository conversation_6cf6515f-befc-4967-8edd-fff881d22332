#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 完整版Web应用后端
真正的视频处理，输出带检测框和计数线的结果视频
"""

import os
import sys
import uuid
import time
import json
import threading
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, send_file, render_template
from werkzeug.utils import secure_filename
import cv2
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️  警告: ultralytics未安装，将使用模拟模式")

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 200 * 1024 * 1024  # 200MB限制

# 全局变量
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv'}

# 任务状态存储
task_status = {}
task_results = {}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class VehicleCounter:
    """车辆计数器类"""
    
    def __init__(self, model_name='yolov11n', confidence=0.5):
        self.model_name = model_name
        self.confidence = confidence
        self.model = None
        self.vehicle_classes = [2, 3, 5, 7]  # car, motorcycle, bus, truck
        
        # 计数线位置 (相对于视频宽度的比例)
        self.count_line_position = 0.5
        
        # 跟踪相关
        self.tracked_objects = {}
        self.next_id = 1
        self.crossed_ids = set()
        self.left_count = 0
        self.right_count = 0
        
        self.load_model()
    
    def load_model(self):
        """加载YOLO模型"""
        if not YOLO_AVAILABLE:
            print("⚠️  YOLO不可用，使用模拟模式")
            return
        
        try:
            # 尝试加载预训练模型
            model_path = f"{self.model_name}.pt"
            self.model = YOLO(model_path)
            print(f"✅ 成功加载模型: {model_path}")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def simple_tracker(self, detections, frame_shape):
        """简单的目标跟踪器"""
        current_objects = {}
        
        for detection in detections:
            x1, y1, x2, y2 = detection[:4]
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            # 寻找最近的已跟踪对象
            min_distance = float('inf')
            matched_id = None
            
            for obj_id, (prev_x, prev_y) in self.tracked_objects.items():
                distance = np.sqrt((center_x - prev_x)**2 + (center_y - prev_y)**2)
                if distance < min_distance and distance < 100:  # 距离阈值
                    min_distance = distance
                    matched_id = obj_id
            
            if matched_id is not None:
                current_objects[matched_id] = (center_x, center_y)
                # 检查是否穿越计数线
                self.check_line_crossing(matched_id, center_x, frame_shape[1])
            else:
                # 新对象
                current_objects[self.next_id] = (center_x, center_y)
                self.next_id += 1
        
        self.tracked_objects = current_objects
        return current_objects
    
    def check_line_crossing(self, obj_id, center_x, frame_width):
        """检查对象是否穿越计数线"""
        line_x = frame_width * self.count_line_position
        
        if obj_id in self.crossed_ids:
            return
        
        # 简单的穿越检测（实际应用中需要更复杂的逻辑）
        if abs(center_x - line_x) < 20:  # 接近计数线
            self.crossed_ids.add(obj_id)
            if center_x < line_x:
                self.left_count += 1
            else:
                self.right_count += 1
    
    def draw_annotations(self, frame, detections, tracked_objects, show_trajectory=True, show_count_line=True):
        """在帧上绘制检测框、轨迹和计数线"""
        height, width = frame.shape[:2]
        
        # 绘制计数线
        if show_count_line:
            line_x = int(width * self.count_line_position)
            cv2.line(frame, (line_x, 0), (line_x, height), (0, 255, 255), 3)
            cv2.putText(frame, 'COUNT LINE', (line_x + 10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # 绘制检测框
        for detection in detections:
            x1, y1, x2, y2, conf, cls = detection
            
            # 只绘制车辆类别
            if int(cls) in self.vehicle_classes:
                # 绘制边界框
                cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                
                # 绘制置信度和类别
                label = f'Vehicle {conf:.2f}'
                cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 绘制跟踪轨迹
        if show_trajectory:
            for obj_id, (center_x, center_y) in tracked_objects.items():
                cv2.circle(frame, (int(center_x), int(center_y)), 5, (255, 0, 0), -1)
                cv2.putText(frame, f'ID:{obj_id}', (int(center_x) + 10, int(center_y)), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
        
        # 绘制计数信息
        cv2.putText(frame, f'Left: {self.left_count}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(frame, f'Right: {self.right_count}', (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(frame, f'Total: {self.left_count + self.right_count}', (10, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        return frame
    
    def process_video(self, input_path, output_path, task_id, settings):
        """处理视频文件"""
        try:
            # 更新任务状态
            task_status[task_id] = {
                'status': 'processing',
                'progress': 0,
                'message': '正在初始化...',
                'vehicle_count': 0,
                'current_fps': 0
            }
            
            # 打开视频
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 设置输出视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            frame_count = 0
            start_time = time.time()
            
            # 根据速度设置跳帧
            skip_frames = 1
            if settings.get('speed') == 'fast':
                skip_frames = 3
            elif settings.get('speed') == 'accurate':
                skip_frames = 1
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                
                # 跳帧处理
                if frame_count % skip_frames != 0:
                    continue
                
                # 更新进度
                progress = (frame_count / total_frames) * 100
                current_time = time.time()
                elapsed_time = current_time - start_time
                current_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                task_status[task_id].update({
                    'progress': progress,
                    'message': f'处理第 {frame_count}/{total_frames} 帧',
                    'vehicle_count': self.left_count + self.right_count,
                    'current_fps': current_fps
                })
                
                # 进行检测
                detections = []
                if self.model is not None:
                    try:
                        results = self.model(frame, conf=self.confidence)
                        for result in results:
                            boxes = result.boxes
                            if boxes is not None:
                                for box in boxes:
                                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                    conf = box.conf[0].cpu().numpy()
                                    cls = box.cls[0].cpu().numpy()
                                    
                                    # 只处理车辆类别
                                    if int(cls) in self.vehicle_classes:
                                        detections.append([x1, y1, x2, y2, conf, cls])
                    except Exception as e:
                        print(f"检测错误: {e}")
                else:
                    # 模拟检测结果
                    if frame_count % 30 == 0:  # 每30帧模拟一个检测
                        x1, y1 = np.random.randint(0, width//2), np.random.randint(0, height//2)
                        x2, y2 = x1 + 100, y1 + 60
                        detections.append([x1, y1, x2, y2, 0.8, 2])
                
                # 跟踪对象
                tracked_objects = self.simple_tracker(detections, frame.shape)
                
                # 绘制注释
                annotated_frame = self.draw_annotations(
                    frame, detections, tracked_objects,
                    settings.get('show_trajectory', True),
                    settings.get('show_count_line', True)
                )
                
                # 写入输出视频
                out.write(annotated_frame)
            
            # 清理资源
            cap.release()
            out.release()
            
            # 计算最终结果
            processing_time = time.time() - start_time
            avg_fps = frame_count / processing_time if processing_time > 0 else 0
            
            # 保存结果
            task_results[task_id] = {
                'vehicle_left': self.left_count,
                'vehicle_right': self.right_count,
                'processed_frames': frame_count,
                'processing_time': f"{processing_time:.2f}s",
                'avg_fps': avg_fps,
                'completion_time': datetime.now().isoformat()
            }
            
            # 更新最终状态
            task_status[task_id] = {
                'status': 'completed',
                'progress': 100,
                'message': '处理完成',
                'vehicle_count': self.left_count + self.right_count,
                'current_fps': avg_fps
            }
            
            print(f"✅ 视频处理完成: {task_id}")
            
        except Exception as e:
            print(f"❌ 视频处理失败: {e}")
            task_status[task_id] = {
                'status': 'error',
                'progress': 0,
                'message': f'处理失败: {str(e)}',
                'vehicle_count': 0,
                'current_fps': 0
            }

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_video():
    """上传视频文件"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 生成任务ID和安全文件名
        task_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{filename}")
        
        # 保存文件
        file.save(file_path)
        
        # 初始化任务状态
        task_status[task_id] = {
            'status': 'uploaded',
            'progress': 0,
            'message': '文件上传成功',
            'vehicle_count': 0,
            'current_fps': 0,
            'file_path': file_path
        }
        
        return jsonify({
            'task_id': task_id,
            'message': '文件上传成功'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/process', methods=['POST'])
def process_video():
    """开始处理视频"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        settings = data.get('settings', {})
        
        if not task_id or task_id not in task_status:
            return jsonify({'error': '无效的任务ID'}), 400
        
        if task_status[task_id]['status'] != 'uploaded':
            return jsonify({'error': '任务状态错误'}), 400
        
        # 获取输入文件路径
        input_path = task_status[task_id]['file_path']
        output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_processed.mp4")
        
        # 创建车辆计数器
        counter = VehicleCounter(
            model_name=settings.get('model', 'yolov11n'),
            confidence=settings.get('confidence', 0.5)
        )
        
        # 在后台线程中处理视频
        thread = threading.Thread(
            target=counter.process_video,
            args=(input_path, output_path, task_id, settings)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'message': '开始处理视频',
            'task_id': task_id
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """获取任务状态"""
    if task_id not in task_status:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(task_status[task_id])

@app.route('/api/results/<task_id>')
def get_results(task_id):
    """获取处理结果"""
    if task_id not in task_results:
        return jsonify({'error': '结果不存在'}), 404
    
    return jsonify(task_results[task_id])

@app.route('/api/download/<task_id>')
def download_video(task_id):
    """下载处理后的视频"""
    output_path = os.path.join(OUTPUT_FOLDER, f"{task_id}_processed.mp4")
    
    if not os.path.exists(output_path):
        return jsonify({'error': '文件不存在'}), 404
    
    return send_file(
        output_path,
        as_attachment=True,
        download_name=f"processed_video_{task_id}.mp4",
        mimetype='video/mp4'
    )

@app.route('/results/<task_id>')
def view_results(task_id):
    """查看结果页面"""
    if task_id not in task_results:
        return "结果不存在", 404
    
    results = task_results[task_id]
    return f"""
    <html>
    <head><title>处理结果</title></head>
    <body>
        <h1>YOLOv11车辆计数结果</h1>
        <p>任务ID: {task_id}</p>
        <p>左侧穿越: {results['vehicle_left']} 辆</p>
        <p>右侧穿越: {results['vehicle_right']} 辆</p>
        <p>总计: {results['vehicle_left'] + results['vehicle_right']} 辆</p>
        <p>处理帧数: {results['processed_frames']}</p>
        <p>处理时间: {results['processing_time']}</p>
        <p>平均FPS: {results['avg_fps']:.2f}</p>
        <p><a href="/api/download/{task_id}">下载处理视频</a></p>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("🚗 YOLOv11车辆计数器 - 完整版Web服务器")
    print("=" * 50)
    print("✨ 功能特色:")
    print("   🎯 真实的YOLO检测")
    print("   📹 带检测框的输出视频")
    print("   📊 实时计数和跟踪")
    print("   🎬 计数线可视化")
    print("   📈 详细统计报告")
    print("=" * 50)
    print("🌐 访问地址: http://localhost:5000")
    print("📁 上传目录:", os.path.abspath(UPLOAD_FOLDER))
    print("📁 输出目录:", os.path.abspath(OUTPUT_FOLDER))
    
    if not YOLO_AVAILABLE:
        print("\n⚠️  注意: YOLO不可用，运行在模拟模式")
        print("💡 安装ultralytics以启用真实检测:")
        print("   pip install ultralytics")
    
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)