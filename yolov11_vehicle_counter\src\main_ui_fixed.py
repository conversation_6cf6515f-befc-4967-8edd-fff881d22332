#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - UI修复版本
修复计数为0的问题，基于golden版本优化UI
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import argparse
from datetime import datetime

# --- Configuration ---
track_history = {}
counted_ids = set()

def create_enhanced_ui_overlay(frame, left_count, right_count, line_x):
    """创建增强的UI覆盖层"""
    h, w = frame.shape[:2]
    
    # 创建半透明覆盖层用于显示计数信息
    overlay = frame.copy()
    
    # 左侧计数区域 - 深蓝色背景
    cv2.rectangle(overlay, (10, 10), (350, 150), (139, 69, 19), -1)  # 深蓝色
    # 右侧计数区域 - 深红色背景  
    cv2.rectangle(overlay, (w-360, 10), (w-10, 150), (19, 69, 139), -1)  # 深红色
    
    # 混合原图和覆盖层
    alpha = 0.8
    frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
    
    # 绘制超大字体计数信息
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 2.0  # 更大的字体
    thickness = 3
    
    # 左侧计数 - 白色大字
    cv2.putText(frame, "LEFT", (25, 60), font, 1.2, (255, 255, 255), thickness)
    cv2.putText(frame, f"{left_count:03d}", (25, 120), font, font_scale, (255, 255, 255), thickness+1)
    
    # 右侧计数 - 白色大字
    cv2.putText(frame, "RIGHT", (w-200, 60), font, 1.2, (255, 255, 255), thickness)
    cv2.putText(frame, f"{right_count:03d}", (w-200, 120), font, font_scale, (255, 255, 255), thickness+1)
    
    # 绘制增强的计数线
    # 主线 - 亮绿色粗线
    cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 6)
    
    # 添加计数线标签
    cv2.rectangle(frame, (line_x-80, h//2-30), (line_x+80, h//2+30), (0, 255, 0), -1)
    cv2.putText(frame, "COUNT", (line_x-70, h//2-5), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    cv2.putText(frame, "LINE", (line_x-50, h//2+20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    # 添加方向箭头
    # 左箭头
    arrow_left = np.array([[line_x-120, h//2], [line_x-90, h//2-15], [line_x-90, h//2+15]], np.int32)
    cv2.fillPoly(frame, [arrow_left], (255, 255, 0))
    
    # 右箭头  
    arrow_right = np.array([[line_x+120, h//2], [line_x+90, h//2-15], [line_x+90, h//2+15]], np.int32)
    cv2.fillPoly(frame, [arrow_right], (255, 255, 0))
    
    return frame

def run_tracker(model_path, video_path, save_path, show_video=True):
    """运行增强UI的车辆跟踪器 - 基于golden版本"""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    print(f"🚗 加载模型: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ 无法打开视频文件: {video_path}")
        return

    # 获取视频属性
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
    
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"💾 输出视频: {save_path}")

    frame_count = 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("✅ 视频处理完成")
            break

        frame_count += 1
        
        # 使用YOLO内置跟踪 - 和golden版本一样，不限制类别
        results = model.track(frame, persist=True, verbose=False)
        
        # 使用YOLO内置的plot方法获得最佳可视化效果
        annotated_frame = results[0].plot()
        
        # 处理跟踪结果 - 和golden版本完全一样的逻辑
        if results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, track_id in zip(boxes, track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                
                # 简单直接的计数逻辑 - 和golden版本一样
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        # 从左到右穿越
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                            print(f"🚙 车辆 {track_id} 从左到右穿越，右侧计数: {right_count}")
                        # 从右到左穿越
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                            print(f"🚗 车辆 {track_id} 从右到左穿越，左侧计数: {left_count}")
                
                # 更新历史位置
                track_history[track_id] = (center_x, center_y)

        # 创建增强UI覆盖层 - 这是新增的UI改进
        annotated_frame = create_enhanced_ui_overlay(annotated_frame, left_count, right_count, line_x)
        
        # 添加进度条
        progress = frame_count / total_frames if total_frames > 0 else 0
        progress_width = int(w * 0.6)
        progress_x = int(w * 0.2)
        progress_y = h - 50
        
        # 进度条背景
        cv2.rectangle(annotated_frame, (progress_x, progress_y), (progress_x + progress_width, progress_y + 30), (50, 50, 50), -1)
        # 进度条填充
        cv2.rectangle(annotated_frame, (progress_x, progress_y), (progress_x + int(progress_width * progress), progress_y + 30), (0, 255, 0), -1)
        # 进度文字
        progress_text = f"Progress: {frame_count}/{total_frames} ({progress*100:.1f}%)"
        cv2.putText(annotated_frame, progress_text, (progress_x + 10, progress_y + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 显示视频 - 改进错误处理
        if show_video:
            try:
                cv2.imshow("YOLOv11 Enhanced Vehicle Counter", annotated_frame)
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    print("🛑 用户请求退出")
                    break
            except cv2.error:
                print("⚠️ GUI不可用，继续处理但不显示窗口")
                show_video = False

        # 保存帧
        out.write(annotated_frame)
        
        # 进度显示
        if frame_count % 100 == 0:
            print(f"📊 进度: {frame_count}/{total_frames} ({progress*100:.1f}%) | 左侧: {left_count} | 右侧: {right_count}")

    # 清理资源
    cap.release()
    out.release()
    
    # 安全关闭窗口
    try:
        cv2.destroyAllWindows()
    except:
        pass
    
    # 最终结果
    print("=" * 70)
    print("🎉 车辆计数完成！")
    print(f"📊 最终统计:")
    print(f"   ⬅️  左侧通过: {left_count:3d} 辆")
    print(f"   ➡️  右侧通过: {right_count:3d} 辆")
    print(f"   🚗 总计车辆: {left_count + right_count:3d} 辆")
    print(f"📹 处理帧数: {frame_count:,} 帧")
    print(f"💾 输出文件: {save_path}")
    print("=" * 70)

if __name__ == '__main__':
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_ui_fixed_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - UI修复版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - UI修复版本")
    print("✨ 特性:")
    print("   📱 超大字体清晰计数显示")
    print("   🔧 修复计数为0的问题")
    print("   🎨 增强可视化效果")
    print("   📊 实时进度显示")
    print("=" * 50)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 50)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)