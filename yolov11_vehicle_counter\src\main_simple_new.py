#!/usr/bin/env python3
"""
简化版车辆计数器 - 兼容新版ultralytics
基于原始逻辑，但使用新版本ultralytics API
"""

import os
# 修复OpenMP冲突
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"

import sys
import cv2
import torch
import numpy as np
from pathlib import Path
from ultralytics import YOLO
from tracker.deep_sort import DeepSort
from tracker.utils.parser import get_config

# 全局计数变量
count = 0
count2 = 0
data = []

def count_obj(box, w, h, id):
    """计数函数 - 与原版完全相同"""
    global count, count2, data
    center_coor = (int(box[0] + (box[2]-box[0])/2 ), int(box[1] + (box[3] - box[1])/2 ))
    if int(box[1] + (box[3] - box[1])/2) > h - 350 and id not in data:
        if int(box[0] + (box[2]-box[0])/2) > int(w/2):
            count2 += 1
        else:
            count += 1
        data.append(id)

def main():
    """主函数"""
    global count, count2, data
    
    # 重置计数
    count = 0
    count2 = 0
    data = []
    
    # 路径配置
    model_path = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    video_path = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    reid_model_path = "tracker/deep/osnet模型/osnet_x1_0_imagenet.pth"
    output_path = "../runs/track/simple_new/output.mp4"
    
    print(f"🤖 加载YOLO模型: {model_path}")
    
    # 加载YOLO模型
    model = YOLO(model_path)
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    print(f"📱 使用设备: {device}")
    
    # 加载DeepSORT
    print(f"🔍 加载DeepSORT模型: {reid_model_path}")
    cfg = get_config()
    cfg.merge_from_file("tracker/configs/deep_sort.yaml")
    
    deepsort = DeepSort(
        reid_model_path,
        device,
        max_dist=cfg.DEEPSORT.MAX_DIST,
        max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
        max_age=cfg.DEEPSORT.MAX_AGE,
        n_init=cfg.DEEPSORT.N_INIT,
        nn_budget=cfg.DEEPSORT.NN_BUDGET,
    )
    
    # 打开视频
    print(f"🎥 打开视频: {video_path}")
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ 无法打开视频文件")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # 创建输出目录
    output_dir = Path(output_path).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (w, h))
    
    print(f"📊 视频信息: {w}x{h} @ {fps}fps")
    print(f"💾 输出路径: {output_path}")
    print("🔄 开始处理...")
    
    frame_idx = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        frame_idx += 1
        
        # YOLO检测
        results = model.predict(
            frame,
            conf=0.6,      # 置信度阈值
            iou=0.7,       # IOU阈值
            max_det=100,   # 最大检测数
            verbose=False
        )
        
        # 处理检测结果
        if results and len(results) > 0:
            result = results[0]
            if result.boxes is not None and len(result.boxes) > 0:
                # 获取检测框
                boxes = result.boxes.data.cpu().numpy()
                
                if len(boxes) > 0:
                    # 转换为DeepSORT需要的格式
                    xywhs = boxes[:, :4].copy()
                    # 转换xyxy到xywh
                    xywhs[:, 2] = xywhs[:, 2] - xywhs[:, 0]  # width
                    xywhs[:, 3] = xywhs[:, 3] - xywhs[:, 1]  # height
                    xywhs[:, 0] = xywhs[:, 0] + xywhs[:, 2] / 2  # center x
                    xywhs[:, 1] = xywhs[:, 1] + xywhs[:, 3] / 2  # center y
                    
                    confs = boxes[:, 4]
                    clss = boxes[:, 5]
                    
                    # DeepSORT跟踪
                    try:
                        tracks = deepsort.update(xywhs, confs, clss, frame)
                        
                        # 处理跟踪结果
                        for track in tracks:
                            bbox = track[:4]
                            track_id = int(track[4])
                            cls_id = int(track[5])
                            
                            # 计数
                            count_obj(bbox, w, h, track_id)
                            
                            # 绘制边界框
                            x1, y1, x2, y2 = map(int, bbox)
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                            cv2.putText(frame, f'ID:{track_id}', (x1, y1-10), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    
                    except Exception as e:
                        print(f"⚠️  帧 {frame_idx} DeepSORT错误: {e}")
        
        # 绘制计数线和计数
        # 左侧线（绿色）
        color = (0, 255, 0)
        start_point = (0, h - 350)
        end_point = (int(w/2) - 50, h - 350)
        cv2.line(frame, start_point, end_point, color, thickness=2)
        
        # 右侧线（红色）
        color = (255, 0, 0)
        start_point = (int(w/2) + 50, h - 350)
        end_point = (w, h - 350)
        cv2.line(frame, start_point, end_point, color, thickness=2)
        
        # 显示计数
        cv2.putText(frame, str(count), (150, 150), cv2.FONT_HERSHEY_COMPLEX, 3, (0, 255, 0), 3)
        cv2.putText(frame, str(count2), (w - 150, 150), cv2.FONT_HERSHEY_COMPLEX, 3, (255, 0, 0), 3)
        
        # 写入输出视频
        out.write(frame)
        
        # 显示进度
        if frame_idx % 100 == 0:
            print(f"📊 处理进度: 帧 {frame_idx}, 左侧计数: {count}, 右侧计数: {count2}")
    
    # 清理资源
    cap.release()
    out.release()
    cv2.destroyAllWindows()
    
    print("✅ 处理完成！")
    print(f"📊 最终结果: 左侧 {count}, 右侧 {count2}, 总计 {count + count2}")
    print(f"💾 输出视频: {output_path}")

if __name__ == '__main__':
    main()