#!/usr/bin/env python3
"""
测试修复版华丽Web应用
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_file_selection():
    """测试文件选择功能"""
    print("🧪 测试文件选择功能...")
    
    # 检查JavaScript文件是否包含修复
    js_file = Path("webapp/static/js/main_gorgeous.js")
    if js_file.exists():
        content = js_file.read_text(encoding='utf-8')
        if "uploadBtn.addEventListener('click'" in content:
            print("✅ JavaScript文件选择修复已应用")
            return True
        else:
            print("❌ JavaScript文件选择修复未找到")
            return False
    else:
        print("❌ JavaScript文件不存在")
        return False

def test_html_button():
    """测试HTML按钮修复"""
    print("🧪 测试HTML按钮修复...")
    
    # 检查HTML文件是否移除了内联onclick
    html_files = [
        Path("webapp/index_gorgeous.html"),
        Path("webapp/templates/index.html")
    ]
    
    all_fixed = True
    for html_file in html_files:
        if html_file.exists():
            content = html_file.read_text(encoding='utf-8')
            if 'onclick="document.getElementById' in content:
                print(f"❌ {html_file} 仍包含内联onclick")
                all_fixed = False
            else:
                print(f"✅ {html_file} 内联onclick已移除")
        else:
            print(f"⚠️  {html_file} 不存在")
    
    return all_fixed

def test_backend_fixes():
    """测试后端修复"""
    print("🧪 测试后端修复...")
    
    backend_file = Path("webapp/app_gorgeous_fixed.py")
    if backend_file.exists():
        content = backend_file.read_text(encoding='utf-8')
        
        fixes_found = 0
        
        # 检查调试信息
        if "self.detection_count" in content:
            print("✅ 检测计数调试信息已添加")
            fixes_found += 1
        
        # 检查增强的模型加载
        if "测试模型" in content:
            print("✅ 模型测试功能已添加")
            fixes_found += 1
        
        # 检查无跟踪模式支持
        if "has_tracking" in content:
            print("✅ 无跟踪模式支持已添加")
            fixes_found += 1
        
        # 检查错误处理
        if "traceback.print_exc" in content:
            print("✅ 增强错误处理已添加")
            fixes_found += 1
        
        if fixes_found >= 3:
            print(f"✅ 后端修复完成 ({fixes_found}/4 项修复)")
            return True
        else:
            print(f"⚠️  后端修复不完整 ({fixes_found}/4 项修复)")
            return False
    else:
        print("❌ 修复版后端文件不存在")
        return False

def test_webapp_connection():
    """测试Web应用连接"""
    print("🧪 测试Web应用连接...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: run_webapp_gorgeous.bat")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 YOLOv11车辆计数器 - 修复验证测试")
    print("=" * 60)
    
    tests = [
        ("文件选择功能", test_file_selection),
        ("HTML按钮修复", test_html_button),
        ("后端修复", test_backend_fixes),
        ("Web应用连接", test_webapp_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！")
        print("\n✨ 修复内容:")
        print("   🖱️  文件选择按钮现在可以正常点击")
        print("   🤖 AI检测增加了调试信息和错误处理")
        print("   📊 支持无跟踪模式的车辆检测")
        print("   🔍 增强的模型加载和测试")
        
        print("\n🚀 现在可以运行:")
        print("   run_webapp_gorgeous.bat")
        
    else:
        print("⚠️  部分修复可能未完全应用")
        print("\n💡 建议:")
        print("   1. 检查文件是否正确保存")
        print("   2. 重新运行 run_webapp_gorgeous.py")
        print("   3. 清除浏览器缓存")

if __name__ == "__main__":
    main()