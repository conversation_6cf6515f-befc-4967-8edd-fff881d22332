#!/bin/bash

# YOLOv11车辆计数系统快速部署脚本
# 域名: vehicle.lkr666.online
# 服务器: Ubuntu 22.04 + Apache

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_NAME="vehicle-counter"
PROJECT_DIR="/var/www/$PROJECT_NAME"
DOMAIN_NAME="vehicle.smart-traffic.top"  # 使用正式域名
PYTHON_VERSION="python3"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示欢迎信息
show_welcome() {
    clear
    echo "=================================================="
    echo "🚗 YOLOv11车辆计数系统 - 快速部署"
    echo "=================================================="
    echo "域名: $DOMAIN_NAME"
    echo "项目目录: $PROJECT_DIR"
    echo "Python版本: $PYTHON_VERSION"
    echo "=================================================="
    echo
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查是否为root
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查Apache
    if ! command -v apache2 &> /dev/null; then
        log_error "Apache2未安装，请先安装Apache2"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包列表
    sudo apt update
    
    # 安装必要的包
    sudo apt install -y \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        libgl1-mesa-glx \
        libglib2.0-0 \
        libsm6 \
        libxext6 \
        ffmpeg \
        supervisor
    
    log_success "依赖安装完成"
}

# 创建项目目录
setup_project_directory() {
    log_info "设置项目目录..."
    
    # 备份现有目录
    if [ -d "$PROJECT_DIR" ]; then
        log_warning "项目目录已存在，创建备份..."
        sudo mv "$PROJECT_DIR" "${PROJECT_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 创建新目录
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown $USER:$USER "$PROJECT_DIR"
    
    log_success "项目目录创建完成: $PROJECT_DIR"
}

# 提示上传文件
prompt_file_upload() {
    log_warning "请上传项目文件到服务器"
    echo
    echo "方法1 - 使用scp命令:"
    echo "scp -r ./yolov11_vehicle_counter/webapp/* admin@你的服务器IP:$PROJECT_DIR/"
    echo
    echo "方法2 - 使用SFTP工具 (如WinSCP, FileZilla):"
    echo "上传 yolov11_vehicle_counter/webapp/ 目录下的所有文件到 $PROJECT_DIR/"
    echo
    echo "需要上传的文件:"
    echo "- app.py"
    echo "- index.html"
    echo "- requirements.txt"
    echo "- static/ 目录"
    echo
    
    while true; do
        read -p "文件是否已上传完成？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            break
        elif [[ $REPLY =~ ^[Nn]$ ]]; then
            log_info "请完成文件上传后再继续..."
            continue
        else
            echo "请输入 y 或 n"
        fi
    done
    
    # 验证关键文件
    if [ ! -f "$PROJECT_DIR/app.py" ]; then
        log_error "app.py文件不存在，请检查上传"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/requirements.txt" ]; then
        log_error "requirements.txt文件不存在，请检查上传"
        exit 1
    fi
    
    log_success "文件上传验证通过"
}

# 设置Python环境
setup_python_environment() {
    log_info "设置Python虚拟环境..."
    
    cd "$PROJECT_DIR"
    
    # 创建虚拟环境
    $PYTHON_VERSION -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装项目依赖
    pip install -r requirements.txt
    
    # 安装生产环境依赖
    pip install gunicorn
    
    log_success "Python环境设置完成"
}

# 配置Apache虚拟主机
configure_apache_vhost() {
    log_info "配置Apache虚拟主机..."
    
    # 启用必要模块
    sudo a2enmod rewrite proxy proxy_http headers
    
    # 创建虚拟主机配置文件
    sudo tee /etc/apache2/sites-available/${PROJECT_NAME}.conf > /dev/null <<EOF
<VirtualHost *:80>
    ServerName $DOMAIN_NAME
    ServerAlias www.$DOMAIN_NAME
    
    DocumentRoot $PROJECT_DIR
    
    # 静态文件直接服务
    Alias /static $PROJECT_DIR/static
    <Directory $PROJECT_DIR/static>
        Require all granted
        # 缓存静态文件
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    # 代理API请求到Flask应用
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    # 代理主页面到Flask
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # 设置上传文件大小限制
    LimitRequestBody 104857600  # 100MB
    
    # 日志配置
    ErrorLog \${APACHE_LOG_DIR}/${PROJECT_NAME}_error.log
    CustomLog \${APACHE_LOG_DIR}/${PROJECT_NAME}_access.log combined
    
    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>
EOF
    
    # 启用站点
    sudo a2ensite ${PROJECT_NAME}.conf
    
    # 重新加载Apache配置
    sudo systemctl reload apache2
    
    log_success "Apache虚拟主机配置完成"
}

# 创建应用配置文件
create_app_configs() {
    log_info "创建应用配置文件..."
    
    # Gunicorn配置
    cat > "$PROJECT_DIR/gunicorn.conf.py" <<EOF
# Gunicorn生产环境配置
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
user = "www-data"
group = "www-data"
EOF
    
    # 启动脚本
    cat > "$PROJECT_DIR/start.sh" <<EOF
#!/bin/bash
cd $PROJECT_DIR
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF
    
    chmod +x "$PROJECT_DIR/start.sh"
    
    log_success "应用配置文件创建完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor进程管理..."
    
    sudo tee /etc/supervisor/conf.d/${PROJECT_NAME}.conf > /dev/null <<EOF
[program:$PROJECT_NAME]
command=$PROJECT_DIR/start.sh
directory=$PROJECT_DIR
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/${PROJECT_NAME}.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="$PROJECT_DIR/venv/bin"
EOF
    
    # 重新加载Supervisor配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor配置完成"
}

# 启动服务
start_application() {
    log_info "启动应用服务..."
    
    # 启动应用
    sudo supervisorctl start $PROJECT_NAME
    
    # 等待启动
    sleep 5
    
    # 检查状态
    if sudo supervisorctl status $PROJECT_NAME | grep -q "RUNNING"; then
        log_success "应用启动成功"
    else
        log_error "应用启动失败，检查日志:"
        sudo supervisorctl status $PROJECT_NAME
        sudo tail -20 /var/log/${PROJECT_NAME}.log
        exit 1
    fi
}

# 测试部署
test_deployment() {
    log_info "测试部署结果..."
    
    # 测试本地API
    if curl -s http://localhost:5000/api/health > /dev/null; then
        log_success "本地API测试通过"
    else
        log_warning "本地API测试失败"
    fi
    
    # 显示状态信息
    echo
    echo "=================================================="
    echo "🎉 部署完成！"
    echo "=================================================="
    echo "网站地址: http://$DOMAIN_NAME"
    echo "项目目录: $PROJECT_DIR"
    echo "日志文件: /var/log/${PROJECT_NAME}.log"
    echo "=================================================="
    echo
    echo "下一步操作:"
    echo "1. 配置域名DNS解析: $DOMAIN_NAME -> 你的服务器IP"
    echo "2. 测试网站访问: http://$DOMAIN_NAME"
    echo "3. 配置SSL证书: ./ssl_setup.sh"
    echo "=================================================="
}

# 主函数
main() {
    show_welcome
    check_environment
    install_dependencies
    setup_project_directory
    prompt_file_upload
    setup_python_environment
    configure_apache_vhost
    create_app_configs
    configure_supervisor
    start_application
    test_deployment
}

# 运行主函数
main "$@"
