@echo off
chcp 65001 >nul
echo ============================================================
echo   YOLOv11 Vehicle Counter - Complete Web Application
echo ============================================================
echo.
echo COMPLETE FEATURES:
echo   - 5-panel elegant layout design
echo   - Original video preview + result comparison
echo   - Real YOLO detection and tracking
echo   - Output video with detection boxes and count lines
echo   - Real-time statistics and progress display
echo   - Detailed data analysis and downloads
echo   - Original vs processed video comparison view
echo.
echo LAYOUT:
echo   - Top Left: Video Upload and Settings
echo   - Top Center: Original Video Preview
echo   - Top Right: Real-time Stats and Progress
echo   - Bottom Left: Processed Result Video
echo   - Bottom Center: Original vs Result Comparison
echo   - Bottom Right: Data Analysis and Downloads
echo.
echo PROCESSING FEATURES:
echo   - Real YOLO object detection
echo   - Vehicle recognition and classification
echo   - Intelligent object tracking
echo   - Crossing count statistics
echo   - Detection boxes and trajectory visualization
echo   - High-quality video output
echo.
echo Access URL: http://localhost:5000
echo.
echo Starting complete web application...
echo.

python run_webapp_complete.py

echo.
echo Complete web application stopped.
pause