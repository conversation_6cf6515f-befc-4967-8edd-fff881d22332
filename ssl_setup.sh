#!/bin/bash

# SSL证书自动配置脚本
# 使用Let's Encrypt为网站配置HTTPS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN_NAME=""
EMAIL=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取用户输入
get_user_input() {
    echo "=== SSL证书配置 ==="
    echo "默认域名: vehicle.lkr666.online"
    echo

    read -p "使用默认域名？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        DOMAIN_NAME="vehicle.lkr666.online"
    else
        read -p "请输入域名: " DOMAIN_NAME
        if [ -z "$DOMAIN_NAME" ]; then
            log_error "域名不能为空"
            exit 1
        fi
    fi

    read -p "请输入邮箱地址: " EMAIL
    if [ -z "$EMAIL" ]; then
        log_error "邮箱不能为空"
        exit 1
    fi

    log_info "域名: $DOMAIN_NAME"
    log_info "邮箱: $EMAIL"

    read -p "确认配置？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "用户取消操作"
        exit 1
    fi
}

# 检查域名解析
check_dns() {
    log_info "检查域名解析..."
    
    # 获取服务器公网IP
    SERVER_IP=$(curl -s ifconfig.me)
    log_info "服务器IP: $SERVER_IP"
    
    # 检查域名解析
    DOMAIN_IP=$(dig +short $DOMAIN_NAME)
    if [ -z "$DOMAIN_IP" ]; then
        log_error "域名解析失败，请检查DNS配置"
        exit 1
    fi
    
    log_info "域名解析IP: $DOMAIN_IP"
    
    if [ "$SERVER_IP" != "$DOMAIN_IP" ]; then
        log_warning "域名解析IP与服务器IP不匹配"
        log_warning "请确保域名已正确解析到服务器IP: $SERVER_IP"
        read -p "是否继续？(y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_success "域名解析检查通过"
}

# 安装Certbot
install_certbot() {
    log_info "安装Certbot..."
    
    # 更新包列表
    sudo apt update
    
    # 安装Certbot和Apache插件
    sudo apt install -y certbot python3-certbot-apache
    
    log_success "Certbot安装完成"
}

# 获取SSL证书
obtain_certificate() {
    log_info "获取SSL证书..."
    
    # 使用Certbot获取证书
    sudo certbot --apache \
        --non-interactive \
        --agree-tos \
        --email "$EMAIL" \
        --domains "$DOMAIN_NAME" \
        --redirect
    
    if [ $? -eq 0 ]; then
        log_success "SSL证书获取成功"
    else
        log_error "SSL证书获取失败"
        exit 1
    fi
}

# 配置自动续期
setup_auto_renewal() {
    log_info "配置证书自动续期..."
    
    # 测试续期
    sudo certbot renew --dry-run
    
    if [ $? -eq 0 ]; then
        log_success "自动续期配置成功"
        log_info "证书将在到期前自动续期"
    else
        log_warning "自动续期测试失败，请手动检查"
    fi
}

# 测试HTTPS
test_https() {
    log_info "测试HTTPS连接..."
    
    # 等待Apache重新加载
    sleep 5
    
    # 测试HTTPS连接
    if curl -s -I "https://$DOMAIN_NAME" | grep -q "200 OK"; then
        log_success "HTTPS连接测试成功"
        log_success "网站现在可以通过 https://$DOMAIN_NAME 访问"
    else
        log_warning "HTTPS连接测试失败，请检查配置"
    fi
}

# 显示证书信息
show_certificate_info() {
    log_info "证书信息:"
    sudo certbot certificates
}

# 主函数
main() {
    log_info "开始配置SSL证书..."
    
    get_user_input
    check_dns
    install_certbot
    obtain_certificate
    setup_auto_renewal
    test_https
    show_certificate_info
    
    log_success "SSL配置完成！"
    echo
    echo "=== 配置完成 ==="
    echo "网站地址: https://$DOMAIN_NAME"
    echo "证书有效期: 90天"
    echo "自动续期: 已配置"
    echo
    echo "注意事项:"
    echo "1. 证书会在到期前自动续期"
    echo "2. 如需手动续期: sudo certbot renew"
    echo "3. 查看证书状态: sudo certbot certificates"
}

# 运行主函数
main "$@"
