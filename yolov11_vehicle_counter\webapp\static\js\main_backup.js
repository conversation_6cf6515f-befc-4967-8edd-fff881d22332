// 全局变量
let currentVideo = null;
let processingInterval = null;
let startTime = null;
let isProcessing = false;
let isPaused = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    setupNavigation();
    setupFileUpload();
    setupVideoPlayer();
    setupConfidenceSlider();
    showSection('home');
}

// 设置导航
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSection = this.getAttribute('href').substring(1);
            showSection(targetSection);
            
            // 更新导航状态
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// 显示指定部分
function showSection(sectionId) {
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + sectionId) {
            link.classList.add('active');
        }
    });
}

// 设置文件上传
function setupFileUpload() {
    const uploadZone = document.getElementById('uploadZone');
    const fileInput = document.getElementById('videoFile');
    const processButton = document.getElementById('processButton');
    const fileInfo = document.getElementById('fileInfo');

    // 拖拽上传
    uploadZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.style.borderColor = 'rgba(255, 255, 255, 0.6)';
        this.style.background = 'rgba(255, 255, 255, 0.1)';
    });

    uploadZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        this.style.background = 'transparent';
    });

    uploadZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        this.style.background = 'transparent';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // 处理文件选择
    function handleFileSelect(file) {
        if (!isVideoFile(file)) {
            showNotification('请选择有效的视频文件', 'error');
            return;
        }

        currentVideo = file;
        displayFileInfo(file);
        processButton.disabled = false;
        
        // 更新上传区域显示
        const uploadIcon = uploadZone.querySelector('.upload-icon i');
        const uploadText = uploadZone.querySelector('h3');
        
        uploadIcon.className = 'fas fa-check-circle';
        uploadIcon.style.color = '#4ECDC4';
        uploadText.textContent = `已选择: ${file.name}`;
        
        showNotification('文件上传成功', 'success');
    }

    // 检查是否为视频文件
    function isVideoFile(file) {
        const videoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/mkv', 'video/webm'];
        return videoTypes.includes(file.type);
    }

    // 显示文件信息
    function displayFileInfo(file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        document.getElementById('estimatedTime').textContent = estimateProcessingTime(file.size);
        fileInfo.style.display = 'block';
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 估算处理时间
    function estimateProcessingTime(fileSize) {
        const mbSize = fileSize / (1024 * 1024);
        const estimatedMinutes = Math.ceil(mbSize / 10); // 假设每10MB需要1分钟
        return `约 ${estimatedMinutes} 分钟`;
    }
}

// 设置置信度滑块
function setupConfidenceSlider() {
    const slider = document.getElementById('confidenceSlider');
    const valueDisplay = document.getElementById('confidenceValue');
    
    slider.addEventListener('input', function() {
        valueDisplay.textContent = this.value;
    });
}

// 开始处理
function startProcessing() {
    if (!currentVideo) {
        showNotification('请先选择视频文件', 'error');
        return;
    }

    isProcessing = true;
    isPaused = false;
    startTime = new Date();
    
    // 显示处理页面
    showSection('processing');
    
    // 开始模拟处理过程
    simulateProcessing();
    
    // 显示加载遮罩
    showLoadingOverlay();
    
    showNotification('开始处理视频...', 'info');
}

// 模拟处理过程
function simulateProcessing() {
    let progress = 0;
    let vehicleCount = 0;
    let peopleCount = 0;
    
    const progressCircle = document.querySelector('.progress-ring-circle');
    const circumference = 2 * Math.PI * 90; // r = 90
    progressCircle.style.strokeDasharray = circumference;
    progressCircle.style.strokeDashoffset = circumference;
    
    processingInterval = setInterval(() => {
        if (isPaused) return;
        
        progress += Math.random() * 3; // 随机增长
        if (progress > 100) progress = 100;
        
        // 更新进度圆环
        const offset = circumference - (progress / 100) * circumference;
        progressCircle.style.strokeDashoffset = offset;
        progressCircle.style.stroke = '#4ECDC4';
        
        // 更新进度文本
        document.getElementById('progressPercent').textContent = Math.round(progress) + '%';
        
        // 模拟检测结果
        if (Math.random() > 0.7) {
            vehicleCount += Math.floor(Math.random() * 2);
            document.getElementById('vehicleCount').textContent = vehicleCount;
        }
        
        if (Math.random() > 0.8) {
            peopleCount += Math.floor(Math.random() * 3);
            document.getElementById('peopleCount').textContent = peopleCount;
        }
        
        // 更新处理时间
        updateProcessingTime();
        
        // 添加日志
        if (Math.random() > 0.6) {
            addProcessingLog(generateRandomLog());
        }
        
        // 处理完成
        if (progress >= 100) {
            clearInterval(processingInterval);
            completeProcessing(vehicleCount, peopleCount);
        }
    }, 500);
}

// 生成随机日志
function generateRandomLog() {
    const logs = [
        '检测到新车辆...',
        '更新跟踪状态...',
        '计算穿越轨迹...',
        '保存检测结果...',
        '优化跟踪算法...',
        '分析运动模式...'
    ];
    return logs[Math.floor(Math.random() * logs.length)];
}

// 添加处理日志
function addProcessingLog(message) {
    const logContainer = document.getElementById('logContainer');
    const logItem = document.createElement('div');
    logItem.className = 'log-item';
    
    const currentTime = new Date();
    const timeStr = currentTime.toLocaleTimeString();
    
    logItem.innerHTML = `
        <span class="log-time">${timeStr}</span>
        <span class="log-message">${message}</span>
    `;
    
    logContainer.appendChild(logItem);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // 限制日志数量
    if (logContainer.children.length > 10) {
        logContainer.removeChild(logContainer.firstChild);
    }
}

// 更新处理时间
function updateProcessingTime() {
    if (!startTime) return;
    
    const currentTime = new Date();
    const elapsed = Math.floor((currentTime - startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('processingTime').textContent = timeStr;
}

// 完成处理
function completeProcessing(vehicleCount, peopleCount) {
    isProcessing = false;
    hideLoadingOverlay();
    
    // 更新结果页面数据
    updateResultsPage(vehicleCount, peopleCount);
    
    // 显示结果页面
    setTimeout(() => {
        showSection('results');
        showNotification('视频处理完成！', 'success');
    }, 1000);
    
    // 生成模拟视频
    generateSimulatedVideo();
}

// 更新结果页面
function updateResultsPage(vehicleCount, peopleCount) {
    const leftVehicles = Math.floor(vehicleCount * 0.6);
    const rightVehicles = vehicleCount - leftVehicles;
    
    document.getElementById('totalVehicles').textContent = vehicleCount;
    document.getElementById('leftVehicles').textContent = leftVehicles;
    document.getElementById('rightVehicles').textContent = rightVehicles;
    document.getElementById('totalPeople').textContent = peopleCount;
    
    // 生成详细数据表格
    generateDetailsTable(vehicleCount, peopleCount);
    
    // 生成图表
    generateTimelineChart();
    generateHeatmap();
}

// 生成详细数据表格
function generateDetailsTable(vehicleCount, peopleCount) {
    const tableBody = document.getElementById('detailsTableBody');
    tableBody.innerHTML = '';
    
    // 生成模拟数据
    for (let i = 0; i < vehicleCount + peopleCount; i++) {
        const row = document.createElement('tr');
        const isVehicle = i < vehicleCount;
        const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString();
        const id = i + 1;
        const type = isVehicle ? ['car', 'truck', 'bus'][Math.floor(Math.random() * 3)] : 'person';
        const direction = isVehicle ? ['左→右', '右→左'][Math.floor(Math.random() * 2)] : '-';
        const confidence = (0.7 + Math.random() * 0.3).toFixed(2);
        
        row.innerHTML = `
            <td>${time}</td>
            <td>${id}</td>
            <td>${type}</td>
            <td>${direction}</td>
            <td>${confidence}</td>
        `;
        
        tableBody.appendChild(row);
    }
}

// 生成时间线图表
function generateTimelineChart() {
    const ctx = document.getElementById('timelineChart').getContext('2d');
    
    // 生成模拟数据
    const labels = [];
    const vehicleData = [];
    const peopleData = [];
    
    for (let i = 0; i < 24; i++) {
        labels.push(`${i.toString().padStart(2, '0')}:00`);
        vehicleData.push(Math.floor(Math.random() * 50));
        peopleData.push(Math.floor(Math.random() * 100));
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '车辆数量',
                data: vehicleData,
                borderColor: '#4ECDC4',
                backgroundColor: 'rgba(78, 205, 196, 0.1)',
                tension: 0.4
            }, {
                label: '人员数量',
                data: peopleData,
                borderColor: '#FF6B6B',
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: 'white'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: 'white'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: 'white'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });
}

// 生成热力图
function generateHeatmap() {
    const canvas = document.getElementById('heatmapCanvas');
    const ctx = canvas.getContext('2d');
    
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 生成热点
    for (let i = 0; i < 20; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const intensity = Math.random();
        
        const gradient = ctx.createRadialGradient(x, y, 0, x, y, 50);
        gradient.addColorStop(0, `rgba(255, 107, 107, ${intensity})`);
        gradient.addColorStop(1, 'rgba(255, 107, 107, 0)');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x - 50, y - 50, 100, 100);
    }
    
    // 绘制计数线
    ctx.strokeStyle = '#4ECDC4';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2, 0);
    ctx.lineTo(canvas.width / 2, canvas.height);
    ctx.stroke();
}

// 生成模拟视频
function generateSimulatedVideo() {
    // 这里应该是处理后的视频URL
    // 在实际应用中，这会是服务器返回的视频文件路径
    const videoElement = document.getElementById('resultVideo');
    
    // 使用原始视频作为示例（实际应该是处理后的视频）
    if (currentVideo) {
        const videoURL = URL.createObjectURL(currentVideo);
        videoElement.src = videoURL;
    }
}

// 设置视频播放器
function setupVideoPlayer() {
    const video = document.getElementById('resultVideo');
    const progressBar = document.getElementById('progressBar');
    const playIcon = document.getElementById('playIcon');
    
    // 更新进度条
    video.addEventListener('timeupdate', function() {
        const progress = (video.currentTime / video.duration) * 100;
        progressBar.value = progress;
    });
    
    // 进度条控制
    progressBar.addEventListener('input', function() {
        const time = (this.value / 100) * video.duration;
        video.currentTime = time;
    });
    
    // 播放状态变化
    video.addEventListener('play', function() {
        playIcon.className = 'fas fa-pause';
    });
    
    video.addEventListener('pause', function() {
        playIcon.className = 'fas fa-play';
    });
}

// 切换播放/暂停
function togglePlay() {
    const video = document.getElementById('resultVideo');
    if (video.paused) {
        video.play();
    } else {
        video.pause();
    }
}

// 切换全屏
function toggleFullscreen() {
    const video = document.getElementById('resultVideo');
    if (video.requestFullscreen) {
        video.requestFullscreen();
    } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen();
    } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen();
    }
}

// 暂停处理
function pauseProcessing() {
    isPaused = !isPaused;
    const pauseButton = document.getElementById('pauseButton');
    
    if (isPaused) {
        pauseButton.innerHTML = '<i class="fas fa-play"></i> 继续';
        addProcessingLog('处理已暂停');
    } else {
        pauseButton.innerHTML = '<i class="fas fa-pause"></i> 暂停';
        addProcessingLog('处理已继续');
    }
}

// 停止处理
function stopProcessing() {
    if (processingInterval) {
        clearInterval(processingInterval);
    }
    
    isProcessing = false;
    isPaused = false;
    hideLoadingOverlay();
    
    showSection('upload');
    showNotification('处理已停止', 'warning');
}

// 重置上传
function resetUpload() {
    currentVideo = null;
    document.getElementById('processButton').disabled = true;
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('videoFile').value = '';
    
    // 重置上传区域显示
    const uploadZone = document.getElementById('uploadZone');
    const uploadIcon = uploadZone.querySelector('.upload-icon i');
    const uploadText = uploadZone.querySelector('h3');
    
    uploadIcon.className = 'fas fa-cloud-upload-alt';
    uploadIcon.style.color = '#4ECDC4';
    uploadText.textContent = '拖拽视频文件到此处';
    
    showNotification('已重置上传', 'info');
}

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签页内容
    const tabPanes = document.querySelectorAll('.tab-pane');
    tabPanes.forEach(pane => pane.classList.remove('active'));
    
    // 显示选中的标签页
    document.getElementById(tabName).classList.add('active');
    
    // 更新标签按钮状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => button.classList.remove('active'));
    event.target.classList.add('active');
}

// 下载功能
function downloadVideo() {
    if (currentVideo) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(currentVideo);
        link.download = 'processed_' + currentVideo.name;
        link.click();
        showNotification('开始下载处理后的视频', 'success');
    }
}

function downloadReport() {
    // 生成PDF报告的模拟
    showNotification('正在生成分析报告...', 'info');
    setTimeout(() => {
        showNotification('分析报告下载完成', 'success');
    }, 2000);
}

function downloadData() {
    // 下载CSV数据的模拟
    const csvContent = "data:text/csv;charset=utf-8,时间,ID,类型,方向,置信度\n";
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', 'detection_data.csv');
    link.click();
    showNotification('数据文件下载完成', 'success');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 获取通知图标
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 获取通知颜色
function getNotificationColor(type) {
    const colors = {
        success: '#4ECDC4',
        error: '#EF5350',
        warning: '#FFA726',
        info: '#42A5F5'
    };
    return colors[type] || '#42A5F5';
}

// 显示/隐藏加载遮罩
function showLoadingOverlay() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoadingOverlay() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);