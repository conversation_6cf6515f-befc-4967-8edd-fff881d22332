#!/usr/bin/env python3
"""
启动终极完美版Web应用
基于最好的追踪+撞线计数实现
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def setup_webapp():
    """设置Web应用环境"""
    print("🏆 YOLOv11车辆计数器 - 终极完美版Web应用启动器")
    print("=" * 80)
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    webapp_dir = current_dir / "webapp"
    
    print(f"📁 当前目录: {current_dir}")
    print(f"📁 Web应用目录: {webapp_dir}")
    
    # 确保webapp目录存在
    if not webapp_dir.exists():
        print("❌ webapp目录不存在")
        return False
    
    # 复制文件到webapp目录
    files_to_copy = [
        ("app_ultimate_perfect.py", "app.py")  # 使用终极完美版后端
    ]
    
    for src_file, dst_file in files_to_copy:
        src_path = webapp_dir / src_file
        dst_path = webapp_dir / dst_file
        
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✅ 复制 {src_file} -> {dst_file}")
        else:
            print(f"⚠️  文件不存在: {src_file}")
    
    # 检查模板文件
    templates_dir = webapp_dir / "templates"
    if not templates_dir.exists():
        templates_dir.mkdir()
        print("📁 创建templates目录")
    
    index_template = templates_dir / "index.html"
    if not index_template.exists():
        # 使用现有的模板
        for template_name in ["index_gorgeous.html", "index_complete.html", "index.html"]:
            template_path = webapp_dir / template_name
            if template_path.exists():
                shutil.copy2(template_path, index_template)
                print(f"✅ 使用模板: {template_name}")
                break
    
    # 检查静态文件
    static_dir = webapp_dir / "static"
    if not static_dir.exists():
        static_dir.mkdir()
        print("📁 创建static目录")
    
    return True

def start_webapp():
    """启动Web应用"""
    webapp_dir = Path(__file__).parent / "webapp"
    
    # 切换到webapp目录
    os.chdir(webapp_dir)
    
    print("🚀 启动终极完美版Web服务器...")
    print("🌐 访问地址: http://localhost:5000")
    print("💡 按 Ctrl+C 停止服务器")
    print("=" * 80)
    
    try:
        # 启动Flask应用
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🏆 YOLOv11车辆计数器 - 终极完美版Web应用")
    print("✨ 特色功能:")
    print("   🎯 基于main_final_perfect.py最佳实现")
    print("   🚗 完整的车辆追踪+撞线计数")
    print("   👥 人员检测和统计")
    print("   🔍 实时调试信息显示")
    print("   🎨 完美的UI覆盖层")
    print("   📊 详细的统计结果")
    print("   🌐 友好的Web界面")
    print("=" * 80)
    
    # 设置环境
    if not setup_webapp():
        print("❌ 环境设置失败")
        return
    
    # 启动应用
    start_webapp()

if __name__ == "__main__":
    main()