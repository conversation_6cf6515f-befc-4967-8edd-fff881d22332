#!/usr/bin/env python3
"""
创建包含所有文件的部署包
这样只需要上传一个文件就能完成部署
"""

import os
import base64
import zipfile
from pathlib import Path

def create_deployment_package():
    """创建包含所有文件的部署脚本"""
    
    # 检查必要文件
    webapp_dir = Path("yolov11_vehicle_counter/webapp")
    required_files = ["app.py", "requirements.txt", "index.html"]
    
    print("检查本地文件...")
    for file in required_files:
        file_path = webapp_dir / file
        if not file_path.exists():
            print(f"错误: 缺少文件 {file}")
            return False
    
    if not (webapp_dir / "static").exists():
        print("错误: 缺少static目录")
        return False
    
    print("✅ 本地文件检查通过")
    
    # 创建临时zip文件
    zip_path = "webapp_files.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # 添加主要文件
        for file in required_files:
            file_path = webapp_dir / file
            zipf.write(file_path, file)
        
        # 添加static目录
        static_dir = webapp_dir / "static"
        for root, dirs, files in os.walk(static_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(webapp_dir)
                zipf.write(file_path, arc_path)
    
    # 读取zip文件并编码为base64
    with open(zip_path, 'rb') as f:
        zip_data = f.read()
    
    zip_base64 = base64.b64encode(zip_data).decode('utf-8')
    
    # 删除临时zip文件
    os.remove(zip_path)
    
    # 创建自解压部署脚本
    deployment_script = f'''#!/bin/bash

# YOLOv11车辆计数系统一键部署脚本
# 包含所有必要文件和配置

set -e

# 颜色定义
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
NC='\\033[0m'

log_info() {{ echo -e "${{BLUE}}[INFO]${{NC}} $1"; }}
log_success() {{ echo -e "${{GREEN}}[SUCCESS]${{NC}} $1"; }}
log_warning() {{ echo -e "${{YELLOW}}[WARNING]${{NC}} $1"; }}
log_error() {{ echo -e "${{RED}}[ERROR]${{NC}} $1"; }}

echo "========================================"
echo "🚗 YOLOv11车辆计数系统一键部署"
echo "========================================"
echo "域名: vehicle.smart-traffic.top"
echo "服务器IP: ************"
echo "========================================"
echo

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
    log_error "请不要使用root用户运行此脚本"
    exit 1
fi

# 解压文件
log_info "解压项目文件..."
echo "{zip_base64}" | base64 -d > webapp_files.zip

# 创建项目目录
log_info "创建项目目录..."
sudo mkdir -p /var/www/vehicle-counter
sudo chown $USER:$USER /var/www/vehicle-counter

# 解压文件到项目目录
cd /var/www/vehicle-counter
unzip -o ~/webapp_files.zip
rm ~/webapp_files.zip

log_success "项目文件解压完成"

# 安装系统依赖
log_info "安装系统依赖..."
sudo apt update
sudo apt install -y python3-pip python3-venv python3-dev build-essential
sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg
sudo apt install -y supervisor apache2
sudo apt install -y certbot python3-certbot-apache

# 设置Python环境
log_info "设置Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn

# 创建Gunicorn配置
log_info "创建应用配置..."
cat > gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF

# 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
cd /var/www/vehicle-counter
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF

chmod +x start.sh

# 配置Apache虚拟主机
log_info "配置Apache虚拟主机..."
sudo a2enmod rewrite proxy proxy_http headers ssl

sudo tee /etc/apache2/sites-available/vehicle-counter.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    ServerAlias www.vehicle.smart-traffic.top
    
    DocumentRoot /var/www/vehicle-counter
    
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    LimitRequestBody 104857600
    
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${{APACHE_LOG_DIR}}/vehicle-counter_error.log
    CustomLog ${{APACHE_LOG_DIR}}/vehicle-counter_access.log combined
</VirtualHost>
EOF

sudo a2ensite vehicle-counter.conf
sudo systemctl reload apache2

# 配置Supervisor
log_info "配置Supervisor..."
sudo tee /etc/supervisor/conf.d/vehicle-counter.conf > /dev/null << 'EOF'
[program:vehicle-counter]
command=/var/www/vehicle-counter/start.sh
directory=/var/www/vehicle-counter
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/var/www/vehicle-counter/venv/bin"
EOF

sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start vehicle-counter

# 测试部署
log_info "测试部署..."
sleep 5
sudo supervisorctl status vehicle-counter
curl -s http://localhost:5000/api/health || echo "API测试失败"

echo
echo "========================================"
echo "🎉 部署完成！"
echo "========================================"
echo "🌐 网站地址: http://vehicle.smart-traffic.top"
echo "📊 健康检查: http://vehicle.smart-traffic.top/api/health"
echo "📝 应用日志: /var/log/vehicle-counter.log"
echo "========================================"
echo
echo "下一步:"
echo "1. 配置域名DNS解析: vehicle -> ************"
echo "2. 配置SSL证书: sudo certbot --apache -d vehicle.smart-traffic.top"
echo "========================================"
'''
    
    # 保存部署脚本
    with open("one_click_deploy.sh", 'w', encoding='utf-8') as f:
        f.write(deployment_script)
    
    print("✅ 一键部署脚本创建完成: one_click_deploy.sh")
    print("\n现在你只需要:")
    print("1. 上传这个文件到服务器")
    print("2. 运行: chmod +x one_click_deploy.sh && ./one_click_deploy.sh")
    
    return True

if __name__ == "__main__":
    create_deployment_package()
