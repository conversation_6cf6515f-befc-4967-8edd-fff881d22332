"""
性能监控器模块
监控系统性能，防止内存泄漏和溢出
"""

import logging
import time
import psutil
import gc
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    frame_idx: int
    processing_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    gpu_memory_mb: Optional[float] = None
    fps: float = 0.0
    
class PerformanceMonitor:
    """性能监控器 - 监控和优化系统性能"""
    
    def __init__(self, log_interval: int = 100):
        self.log_interval = log_interval
        self.start_time = time.time()
        self.frame_times = []
        self.memory_usage_history = []
        self.last_log_frame = 0
        self.peak_memory = 0.0
        self.total_frames = 0
        
        # 性能阈值
        self.memory_warning_threshold = 4096  # MB
        self.memory_critical_threshold = 6144  # MB
        self.fps_warning_threshold = 5.0  # FPS
        
    def start_frame_processing(self) -> float:
        """开始帧处理计时"""
        return time.time()
    
    def end_frame_processing(self, start_time: float, frame_idx: int) -> PerformanceMetrics:
        """结束帧处理并记录性能指标"""
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 获取内存使用情况
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_usage_mb = memory_info.rss / 1024 / 1024  # 转换为MB
        
        # 获取CPU使用率
        cpu_usage = psutil.cpu_percent()
        
        # 计算FPS
        self.frame_times.append(processing_time)
        if len(self.frame_times) > 30:  # 保持最近30帧的时间
            self.frame_times.pop(0)
        
        avg_frame_time = sum(self.frame_times) / len(self.frame_times)
        fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0.0
        
        # 更新统计信息
        self.memory_usage_history.append(memory_usage_mb)
        if len(self.memory_usage_history) > 100:  # 保持最近100帧的内存使用
            self.memory_usage_history.pop(0)
        
        self.peak_memory = max(self.peak_memory, memory_usage_mb)
        self.total_frames = frame_idx
        
        # 创建性能指标
        metrics = PerformanceMetrics(
            frame_idx=frame_idx,
            processing_time=processing_time,
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_usage,
            fps=fps
        )
        
        # 检查性能警告
        self._check_performance_warnings(metrics)
        
        # 定期日志记录
        if frame_idx - self.last_log_frame >= self.log_interval:
            self._log_performance_summary(metrics)
            self.last_log_frame = frame_idx
        
        return metrics
    
    def _check_performance_warnings(self, metrics: PerformanceMetrics):
        """检查性能警告并采取措施"""
        
        # 内存使用警告
        if metrics.memory_usage_mb > self.memory_critical_threshold:
            logger.error(f"帧 {metrics.frame_idx} 内存使用过高: {metrics.memory_usage_mb:.1f}MB")
            self._emergency_memory_cleanup()
        elif metrics.memory_usage_mb > self.memory_warning_threshold:
            logger.warning(f"帧 {metrics.frame_idx} 内存使用警告: {metrics.memory_usage_mb:.1f}MB")
            self._gentle_memory_cleanup()
        
        # FPS警告
        if metrics.fps < self.fps_warning_threshold and metrics.frame_idx > 10:
            logger.warning(f"帧 {metrics.frame_idx} FPS过低: {metrics.fps:.1f}")
        
        # CPU使用率警告
        if metrics.cpu_usage_percent > 90:
            logger.warning(f"帧 {metrics.frame_idx} CPU使用率过高: {metrics.cpu_usage_percent:.1f}%")
    
    def _gentle_memory_cleanup(self):
        """温和的内存清理"""
        try:
            gc.collect()
            logger.info("执行内存垃圾回收")
        except Exception as e:
            logger.warning(f"内存清理失败: {e}")
    
    def _emergency_memory_cleanup(self):
        """紧急内存清理"""
        try:
            # 强制垃圾回收
            for _ in range(3):
                gc.collect()
            
            # 清理PyTorch缓存（如果使用GPU）
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    logger.info("清理GPU内存缓存")
            except ImportError:
                pass
            
            logger.warning("执行紧急内存清理")
            
        except Exception as e:
            logger.error(f"紧急内存清理失败: {e}")
    
    def _log_performance_summary(self, metrics: PerformanceMetrics):
        """记录性能摘要"""
        avg_memory = sum(self.memory_usage_history) / len(self.memory_usage_history)
        
        logger.info(
            f"性能摘要 [帧 {metrics.frame_idx}] - "
            f"FPS: {metrics.fps:.1f}, "
            f"内存: {metrics.memory_usage_mb:.1f}MB (平均: {avg_memory:.1f}MB, 峰值: {self.peak_memory:.1f}MB), "
            f"CPU: {metrics.cpu_usage_percent:.1f}%, "
            f"处理时间: {metrics.processing_time:.3f}s"
        )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取完整的性能报告"""
        total_time = time.time() - self.start_time
        avg_fps = self.total_frames / total_time if total_time > 0 else 0
        avg_memory = sum(self.memory_usage_history) / len(self.memory_usage_history) if self.memory_usage_history else 0
        
        return {
            'total_frames': self.total_frames,
            'total_time_seconds': total_time,
            'average_fps': avg_fps,
            'peak_memory_mb': self.peak_memory,
            'average_memory_mb': avg_memory,
            'memory_usage_history': self.memory_usage_history.copy(),
            'frame_times': self.frame_times.copy()
        }
    
    def optimize_for_performance(self) -> Dict[str, Any]:
        """根据当前性能状况提供优化建议"""
        suggestions = []
        
        # 内存优化建议
        if self.peak_memory > self.memory_warning_threshold:
            suggestions.append("考虑减少batch_size或降低图像分辨率")
            suggestions.append("启用更频繁的垃圾回收")
        
        # FPS优化建议
        avg_fps = len(self.frame_times) / sum(self.frame_times) if self.frame_times else 0
        if avg_fps < self.fps_warning_threshold:
            suggestions.append("考虑使用更简单的跟踪器配置")
            suggestions.append("减少YOLO检测的最大数量")
            suggestions.append("降低视频分辨率")
        
        return {
            'suggestions': suggestions,
            'current_performance': self.get_performance_report()
        }

# 全局性能监控器实例
_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def reset_performance_monitor():
    """重置性能监控器"""
    global _performance_monitor
    _performance_monitor = None