<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLOv11 车辆计数器 - 华丽版</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --secondary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --accent-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            color: var(--text-primary);
            overflow-x: hidden;
            position: relative;
        }

        /* 动态背景粒子效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(180deg); }
        }

        /* 浮动粒子 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 15s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 20px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header h1 {
            font-size: 3em;
            font-weight: 800;
            background: linear-gradient(135deg, #fff, #f093fb, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        .header p {
            font-size: 1.2em;
            color: var(--text-secondary);
            font-weight: 300;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr 350px;
            grid-template-rows: 1fr 1fr;
            gap: 25px;
            height: calc(100vh - 200px);
            min-height: 700px;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            padding: 30px;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .glass-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .card-title {
            font-size: 1.4em;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-icon {
            width: 32px;
            height: 32px;
            background: var(--secondary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }

        /* 左侧控制面板 */
        .control-panel {
            grid-row: span 2;
        }

        .upload-zone {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 40px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .upload-zone::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .upload-zone:hover::before {
            opacity: 1;
        }

        .upload-zone:hover {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.02);
        }

        @keyframes rotate {
            100% { transform: rotate(360deg); }
        }

        .upload-icon {
            font-size: 4em;
            margin-bottom: 20px;
            background: var(--accent-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .upload-text {
            font-size: 1.1em;
            color: var(--text-secondary);
            margin-bottom: 20px;
            font-weight: 500;
        }

        .upload-btn {
            background: var(--secondary-gradient);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(79, 172, 254, 0.4);
        }

        .upload-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(79, 172, 254, 0.6);
        }

        .settings-section {
            margin-top: 30px;
        }

        .setting-group {
            margin-bottom: 25px;
        }

        .setting-label {
            display: block;
            font-size: 0.95em;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .setting-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 0.95em;
            transition: all 0.3s ease;
        }

        .setting-input:focus {
            outline: none;
            border-color: rgba(79, 172, 254, 0.6);
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }

        .setting-input option {
            background: #2c3e50;
            color: white;
            padding: 8px;
        }

        .process-btn {
            width: 100%;
            background: var(--accent-gradient);
            color: white;
            border: none;
            padding: 18px;
            border-radius: 16px;
            font-size: 1.1em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            box-shadow: 0 6px 25px rgba(250, 112, 154, 0.4);
        }

        .process-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 35px rgba(250, 112, 154, 0.6);
        }

        .process-btn:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 中央视频区域 */
        .video-main {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .video-container {
            flex: 1;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .video-placeholder {
            text-align: center;
            color: var(--text-secondary);
            font-size: 1.1em;
        }

        .video-placeholder-icon {
            font-size: 4em;
            margin-bottom: 15px;
            opacity: 0.6;
        }

        .video-player {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 20px;
        }



        /* 右侧统计面板 */
        .stats-panel {
            grid-row: span 2;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--secondary-gradient);
            padding: 25px 20px;
            border-radius: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
            50% { transform: scale(1.1) rotate(180deg); opacity: 1; }
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 800;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 1.0em;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        /* 特定统计卡片样式 */
        .car-stat {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .people-stat {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
        }

        .fps-stat {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }

        .progress-section {
            margin-bottom: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: var(--accent-gradient);
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 30%, rgba(255, 255, 255, 0.4) 50%, transparent 70%);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            font-size: 0.95em;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .results-summary {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.95em;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .summary-value {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.1em;
        }

        .download-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .download-btn {
            background: var(--dark-gradient);
            color: white;
            border: none;
            padding: 14px 12px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(52, 73, 94, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 73, 94, 0.5);
        }

        .download-btn:disabled {
            background: rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 状态消息 */
        .status-message {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.95em;
            color: var(--text-primary);
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .main-grid {
                grid-template-columns: 300px 1fr 300px;
            }
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                gap: 20px;
            }
            
            .control-panel {
                grid-row: span 1;
            }
            
            .stats-panel {
                grid-row: span 1;
            }
            
            .video-main {
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto;
            }

            .fps-stat {
                grid-column: span 2;
            }
            
            .download-grid {
                grid-template-columns: 1fr;
            }
            
            .comparison-view {
                grid-template-columns: 1fr;
                height: auto;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 消息样式 */
        .message {
            padding: 15px 20px;
            border-radius: 12px;
            margin: 15px 0;
            font-weight: 500;
            font-size: 0.95em;
            border-left: 4px solid;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
            color: #4caf50;
        }

        .message.error {
            background: rgba(244, 67, 54, 0.2);
            border-color: #f44336;
            color: #f44336;
        }

        .message.info {
            background: rgba(33, 150, 243, 0.2);
            border-color: #2196f3;
            color: #2196f3;
        }

        /* 隐藏文件输入 */
        .file-input {
            display: none;
        }

        /* 复选框样式 */
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #4facfe;
        }

        .checkbox-group label {
            font-size: 0.95em;
            color: var(--text-secondary);
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <div class="header">
        <div class="container">
            <h1>🚗 YOLOv11 车辆计数器</h1>
            <p>AI驱动的智能视频分析系统 · 实时追踪 · 精准计数</p>
        </div>
    </div>

    <div class="container">
        <div class="main-grid">
            <!-- 左侧控制面板 -->
            <div class="glass-card control-panel">
                <div class="card-title">
                    <div class="card-icon">⚙️</div>
                    控制中心
                </div>
                
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">📹</div>
                    <div class="upload-text">拖拽视频文件到此处<br>或点击选择文件</div>
                    <button class="upload-btn">
                        选择视频文件
                    </button>
                    <input type="file" id="videoFile" class="file-input" accept="video/*">
                </div>

                <div class="settings-section">
                    <div class="setting-group">
                        <label class="setting-label">检测模型</label>
                        <select id="modelSelect" class="setting-input">
                            <option value="yolov11n">YOLOv11n (快速)</option>
                            <option value="yolov11s">YOLOv11s (平衡)</option>
                            <option value="yolov11m" selected>YOLOv11m (推荐)</option>
                            <option value="yolov11l">YOLOv11l (高精度)</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">置信度阈值: <span id="confidenceValue">0.5</span></label>
                        <input type="range" id="confidenceSlider" class="setting-input" min="0.1" max="0.9" step="0.1" value="0.5">
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">处理模式</label>
                        <select id="speedSelect" class="setting-input">
                            <option value="fast">快速模式 (跳帧处理)</option>
                            <option value="normal" selected>标准模式 (全帧处理)</option>
                            <option value="accurate">精确模式 (高质量)</option>
                        </select>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="showTrajectory" checked>
                        <label for="showTrajectory">显示运动轨迹</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="showCountLine" checked>
                        <label for="showCountLine">显示计数线</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="showBoundingBox" checked>
                        <label for="showBoundingBox">显示检测框</label>
                    </div>

                    <button class="process-btn" id="processBtn" disabled>
                        <span class="loading-spinner" id="loadingIcon" style="display: none;"></span>
                        开始智能分析
                    </button>
                </div>
            </div>

            <!-- 中央视频区域 -->
            <div class="video-main">
                <div class="glass-card" style="flex: 1;">
                    <div class="card-title">
                        <div class="card-icon">📹</div>
                        原始视频
                    </div>

                    <div class="video-container" id="mainVideoContainer">
                        <div class="video-placeholder" id="mainVideoPlaceholder">
                            <div class="video-placeholder-icon">📺</div>
                            <div>上传视频后将在此显示预览</div>
                        </div>
                        <video class="video-player" id="mainVideo" controls style="display: none;"></video>
                    </div>
                </div>

                <div class="glass-card" style="flex: 1;">
                    <div class="card-title">
                        <div class="card-icon">🎯</div>
                        AI分析结果
                    </div>

                    <div class="video-container" id="processedVideoContainer">
                        <div class="video-placeholder" id="processedVideoPlaceholder">
                            <div class="video-placeholder-icon">🤖</div>
                            <div>AI处理完成后将在此显示结果</div>
                        </div>
                        <video class="video-player" id="processedVideo" controls style="display: none;"></video>
                    </div>
                </div>
            </div>

            <!-- 右侧统计面板 -->
            <div class="glass-card stats-panel">
                <div class="card-title">
                    <div class="card-icon">📊</div>
                    实时统计
                </div>

                <div class="stats-grid">
                    <div class="stat-card car-stat">
                        <div class="stat-number" id="vehicleCount">0</div>
                        <div class="stat-label">🚗 Car</div>
                    </div>
                    <div class="stat-card people-stat">
                        <div class="stat-number" id="peopleCount">0</div>
                        <div class="stat-label">👥 People</div>
                    </div>
                    <div class="stat-card fps-stat">
                        <div class="stat-number" id="currentFps">0.0</div>
                        <div class="stat-label">⚡ FPS</div>
                    </div>
                </div>

                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="progressText">等待开始处理...</div>
                </div>

                <div class="results-summary" id="resultsSummary" style="display: none;">
                    <div class="summary-item">
                        <span class="summary-label">左侧穿越</span>
                        <span class="summary-value" id="leftCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">右侧穿越</span>
                        <span class="summary-value" id="rightCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">处理帧数</span>
                        <span class="summary-value" id="frameCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">处理时间</span>
                        <span class="summary-value" id="processTime">0s</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">平均精度</span>
                        <span class="summary-value" id="avgAccuracy">0%</span>
                    </div>
                </div>

                <div class="download-grid">
                    <button class="download-btn" id="downloadVideo" disabled>
                        <span>📹</span>
                        处理视频
                    </button>
                    <button class="download-btn" id="downloadReport" disabled>
                        <span>📄</span>
                        分析报告
                    </button>
                    <button class="download-btn" id="downloadData" disabled>
                        <span>📊</span>
                        原始数据
                    </button>
                    <button class="download-btn" id="shareResults" disabled>
                        <span>🔗</span>
                        分享结果
                    </button>
                </div>

                <div class="status-message" id="statusMessage" style="display: none;">
                    <div id="statusText">系统就绪</div>
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/main.js"></script>
</body>
</html>