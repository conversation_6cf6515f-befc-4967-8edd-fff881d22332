# 基于原版main.py的简单UI优化版本
import os
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import copy
import sys
sys.path.insert(0, './ultralytics')

import argparse
import platform
import shutil
import time
from pathlib import Path
import numpy as np
import cv2
import torch
import torch.backends.cudnn as cudnn
from datetime import datetime

from ultralytics import YOLO
from ultralytics.data.loaders import LoadImages, LoadStreams
from ultralytics.data.utils import VID_FORMATS
from ultralytics.utils import (LOGGER, ops, check_imshow, colorstr)
from ultralytics.utils.torch_utils import select_device, time_sync
from ultralytics.utils.plotting import Annotator, colors
from tracker.utils.parser import get_config
from tracker.deep_sort import DeepSort

FILE = Path(__file__).resolve()
ROOT = FILE.parents[0].parents[0]
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))

# 全局计数变量
count = 0
count2 = 0
data = []

# UI优化配置
class UIConfig:
    """UI美化配置"""
    # 颜色配置 (BGR格式)
    LEFT_LINE_COLOR = (0, 255, 0)      # 绿色 - 左侧计数线
    RIGHT_LINE_COLOR = (0, 0, 255)     # 红色 - 右侧计数线
    LEFT_COUNT_COLOR = (0, 255, 0)     # 绿色 - 左侧计数文字
    RIGHT_COUNT_COLOR = (0, 0, 255)    # 红色 - 右侧计数文字
    BBOX_COLOR = (255, 255, 0)         # 青色 - 检测框
    ID_COLOR = (255, 255, 255)         # 白色 - ID文字
    
    # 仪表板配置
    DASHBOARD_HEIGHT = 120
    DASHBOARD_COLOR = (40, 40, 40)     # 深灰色背景
    
    # 文字配置
    FONT = cv2.FONT_HERSHEY_SIMPLEX
    COUNT_FONT_SCALE = 2.0
    COUNT_FONT_THICKNESS = 3
    ID_FONT_SCALE = 0.6
    ID_FONT_THICKNESS = 2
    
    # 线条配置
    LINE_THICKNESS = 3
    BBOX_THICKNESS = 2
    
    # 位置配置
    LINE_Y_OFFSET = 350  # 计数线距离底部的距离

def create_enhanced_canvas(frame, dashboard_height=120):
    """创建增强的画布，包含仪表板"""
    h, w = frame.shape[:2]
    
    # 创建新画布
    canvas_height = h + dashboard_height
    canvas = np.zeros((canvas_height, w, 3), dtype=np.uint8)
    
    # 设置仪表板背景
    canvas[:dashboard_height, :] = UIConfig.DASHBOARD_COLOR
    
    # 放置原始帧
    canvas[dashboard_height:, :] = frame
    
    return canvas

def draw_enhanced_ui(canvas, left_count, right_count, frame_info=None):
    """绘制增强的UI元素"""
    h, w = canvas.shape[:2]
    dashboard_height = UIConfig.DASHBOARD_HEIGHT
    
    # 在仪表板区域绘制信息
    # 标题
    title_text = "Vehicle Counter - Enhanced UI"
    title_size = cv2.getTextSize(title_text, UIConfig.FONT, 1.0, 2)[0]
    title_x = (w - title_size[0]) // 2
    cv2.putText(canvas, title_text, (title_x, 35), UIConfig.FONT, 1.0, (255, 255, 255), 2)
    
    # 左侧计数
    left_text = f"Left: {left_count}"
    cv2.putText(canvas, left_text, (50, 80), UIConfig.FONT, 1.2, UIConfig.LEFT_COUNT_COLOR, 2)
    
    # 右侧计数
    right_text = f"Right: {right_count}"
    right_size = cv2.getTextSize(right_text, UIConfig.FONT, 1.2, 2)[0]
    cv2.putText(canvas, right_text, (w - right_size[0] - 50, 80), UIConfig.FONT, 1.2, UIConfig.RIGHT_COUNT_COLOR, 2)
    
    # 总计数（居中）
    total_text = f"Total: {left_count + right_count}"
    total_size = cv2.getTextSize(total_text, UIConfig.FONT, 1.0, 2)[0]
    total_x = (w - total_size[0]) // 2
    cv2.putText(canvas, total_text, (total_x, 105), UIConfig.FONT, 1.0, (255, 255, 255), 2)
    
    # 在视频区域绘制计数线
    video_h = h - dashboard_height
    line_y = dashboard_height + video_h - UIConfig.LINE_Y_OFFSET
    
    # 左侧计数线
    left_start = (0, line_y)
    left_end = (w//2 - 50, line_y)
    cv2.line(canvas, left_start, left_end, UIConfig.LEFT_LINE_COLOR, UIConfig.LINE_THICKNESS)
    
    # 右侧计数线
    right_start = (w//2 + 50, line_y)
    right_end = (w, line_y)
    cv2.line(canvas, right_start, right_end, UIConfig.RIGHT_LINE_COLOR, UIConfig.LINE_THICKNESS)
    
    # 在视频区域显示大号计数
    # 左侧大号计数
    cv2.putText(canvas, str(left_count), (100, dashboard_height + 100), 
                UIConfig.FONT, UIConfig.COUNT_FONT_SCALE, UIConfig.LEFT_COUNT_COLOR, UIConfig.COUNT_FONT_THICKNESS)
    
    # 右侧大号计数
    right_count_size = cv2.getTextSize(str(right_count), UIConfig.FONT, UIConfig.COUNT_FONT_SCALE, UIConfig.COUNT_FONT_THICKNESS)[0]
    cv2.putText(canvas, str(right_count), (w - right_count_size[0] - 100, dashboard_height + 100), 
                UIConfig.FONT, UIConfig.COUNT_FONT_SCALE, UIConfig.RIGHT_COUNT_COLOR, UIConfig.COUNT_FONT_THICKNESS)
    
    return canvas

def safe_deepsort_update(deepsort, xywhs, confs, clss, frame):
    """安全的DeepSORT更新，包含错误处理"""
    try:
        return deepsort.update(xywhs.cpu(), confs.cpu(), clss.cpu(), frame)
    except Exception as e:
        error_str = str(e)
        if "positive definite" in error_str or "leading minor" in error_str:
            print(f"DeepSORT Matrix错误，跳过此帧: {error_str}")
        elif error_str.isdigit():
            print(f"DeepSORT数值错误(错误码:{error_str})，跳过此帧")
        else:
            print(f"DeepSORT其他错误，跳过此帧: {error_str}")
        return []

def detect(opt):
    global count, count2, data
    
    out, source, yolo_model, deep_sort_model, show_vid, save_vid, save_txt, imgsz, evaluate, half, \
        project, exist_ok, update, save_crop = \
        opt.output, opt.source, opt.yolo_model, opt.deep_sort_model, opt.show_vid, opt.save_vid, \
        opt.save_txt, opt.imgsz, opt.evaluate, opt.half, opt.project, opt.exist_ok, opt.update, opt.save_crop
    
    webcam = source == '0' or source.startswith('rtsp') or source.startswith('http') or source.endswith('.txt')

    # Initialize
    device = select_device(opt.device)
    half &= device.type != 'cpu'

    # 在Windows环境下禁用视频显示以避免GUI错误
    if platform.system() == 'Windows':
        show_vid = False
        print("Windows环境检测到，已禁用视频显示以避免GUI错误")

    if not evaluate:
        if os.path.exists(out):
            pass
            # shutil.rmtree(out)  # 注释掉删除，避免意外删除
        os.makedirs(out, exist_ok=True)

    # Directories
    if type(yolo_model) is str:
        exp_name = yolo_model.split(".")[0]
    elif type(yolo_model) is list and len(yolo_model) == 1:
        exp_name = yolo_model[0].split(".")[0]
    else:
        exp_name = "ensemble"
    
    # 添加时间戳到输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_name = f"{exp_name}_{deep_sort_model.split('/')[-1].split('.')[0]}_{timestamp}"
    
    save_dir = increment_path(Path(project) / exp_name, exist_ok=exist_ok)
    (save_dir / 'tracks' if save_txt else save_dir).mkdir(parents=True, exist_ok=True)

    # 加载YOLO模型
    model = YOLO(yolo_model)
    names = model.names

    # Configure data loader
    vid_path, vid_writer = None, None
    if show_vid:
        show_vid = check_imshow()

    # Data loader
    if webcam:
        cudnn.benchmark = True
        dataset = LoadStreams(source, imgsz=imgsz, stride=model.stride, auto=model.pt)
        nr_sources = len(dataset)
    else:
        dataset = LoadImages(source, imgsz=imgsz, stride=model.stride, auto=model.pt)
        nr_sources = 1
    vid_path, vid_writer, txt_path = [None] * nr_sources, [None] * nr_sources, [None] * nr_sources

    # 初始化DeepSORT
    cfg = get_config()
    cfg.merge_from_file(opt.config_deepsort)

    # 使用更保守的DeepSORT配置来减少错误
    deepsort_list = []
    for i in range(nr_sources):
        deepsort_list.append(
            DeepSort(
                deep_sort_model,
                device,
                max_dist=min(cfg.DEEPSORT.MAX_DIST, 0.2),  # 更严格的距离阈值
                max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
                max_age=min(cfg.DEEPSORT.MAX_AGE, 50),     # 更短的跟踪生命周期
                n_init=max(cfg.DEEPSORT.N_INIT, 3),        # 更多的初始化帧
                nn_budget=min(cfg.DEEPSORT.NN_BUDGET, 50), # 更小的预算
            )
        )
    outputs = [None] * nr_sources

    # Run tracker
    model.warmup(imgsz=(1 if model.pt else nr_sources, 3, *imgsz))
    dt, seen = [0.0, 0.0, 0.0, 0.0], 0
    
    print(f"开始处理视频，输出将保存到: {save_dir}")
    
    for frame_idx, (path, im, im0s, vid_cap, s) in enumerate(dataset):
        t1 = time_sync()
        im = torch.from_numpy(im).to(device)
        im = im.half() if model.pt and half else im.float()
        im /= 255.0
        if len(im.shape) == 3:
            im = im[None]
        t2 = time_sync()
        dt[0] += t2 - t1

        # 预测
        visualize = increment_path(save_dir / Path(path[0]).stem, mkdir=True) if opt.visualize else False
        pred = model(im, augment=opt.augment, visualize=visualize)

        t3 = time_sync()
        dt[1] += t3 - t2

        # NMS - 使用更严格的参数减少检测数量
        pred = ops.non_max_suppression(pred, opt.conf_thres, opt.iou_thres, opt.classes, opt.agnostic_nms, 
                                     max_det=min(opt.max_det, 300))  # 限制最大检测数量
        dt[2] += time_sync() - t3

        # 处理检测结果
        for i, det in enumerate(pred):
            seen += 1
            if webcam:
                p, im0, _ = path[i], im0s[i].copy(), dataset.count
                p = Path(p)
                s += f'{i}: '
                txt_file_name = p.name
                save_path = str(save_dir / p.name)
            else:
                p, im0, _ = path, im0s.copy(), getattr(dataset, 'frame', 0)
                p = Path(p)
                if source.endswith(VID_FORMATS):
                    txt_file_name = p.stem
                    save_path = str(save_dir / p.name)
                else:
                    txt_file_name = p.parent.name
                    save_path = str(save_dir / p.parent.name)

            txt_path = str(save_dir / 'tracks' / txt_file_name)
            s += '%gx%g ' % im.shape[2:]
            imc = im0.copy() if save_crop else im0
            annotator = Annotator(im0, line_width=UIConfig.BBOX_THICKNESS, pil=not ascii)
            
            w, h = im0.shape[1], im0.shape[0]

            if det is not None and len(det):
                # Rescale boxes from img_size to im0 size
                det[:, :4] = ops.scale_coords(im.shape[2:], det[:, :4], im0.shape).round()

                # Print results
                for c in det[:, -1].unique():
                    n = (det[:, -1] == c).sum()
                    s += f"{n} {names[int(c)]}{'s' * (n > 1)}, "

                xywhs = ops.xyxy2xywh(det[:, 0:4])
                confs = det[:, 4]
                clss = det[:, 5]

                # 安全的DeepSORT更新
                t4 = time_sync()
                outputs[i] = safe_deepsort_update(deepsort_list[i], xywhs, confs, clss, im0)
                t5 = time_sync()
                dt[3] += t5 - t4

                # 保存结果
                if len(outputs[i]) > 0:
                    for j, (output, conf) in enumerate(zip(outputs[i], confs)):
                        bboxes = output[0:4]
                        id = output[4]
                        cls = output[5]
                        count_obj(bboxes, w, h, id)

                        if save_txt:
                            bbox_left = output[0]
                            bbox_top = output[1]
                            bbox_w = output[2] - output[0]
                            bbox_h = output[3] - output[1]
                            with open(txt_path + '.txt', 'a') as f:
                                f.write(('%g ' * 10 + '\n') % (frame_idx + 1, id, bbox_left,
                                                               bbox_top, bbox_w, bbox_h, -1, -1, -1, i))

                        if save_vid or save_crop or show_vid:
                            c = int(cls)
                            label = f'{id} {names[c]} {conf:.2f}'
                            # 使用自定义颜色
                            annotator.box_label(bboxes, label, color=UIConfig.BBOX_COLOR)
                            if save_crop:
                                txt_file_name = txt_file_name if (isinstance(path, list) and len(path) > 1) else ''
                                save_one_box(bboxes, imc, file=save_dir / 'crops' / txt_file_name / names[c] / f'{id}' / f'{p.stem}.jpg', BGR=True)

                if frame_idx % 100 == 0:  # 每100帧打印一次进度
                    print(f'帧 {frame_idx}: Left={count}, Right={count2}, Total={count+count2}')

            else:
                deepsort_list[i].increment_ages()

            # 创建增强的UI
            im0 = annotator.result()
            
            # 创建增强画布
            enhanced_canvas = create_enhanced_canvas(im0, UIConfig.DASHBOARD_HEIGHT)
            
            # 绘制增强UI
            enhanced_canvas = draw_enhanced_ui(enhanced_canvas, count, count2)
            
            if show_vid:
                cv2.imshow(str(p), enhanced_canvas)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break

            # 保存增强的视频
            if save_vid:
                if vid_path[i] != save_path:
                    vid_path[i] = save_path
                    if isinstance(vid_writer[i], cv2.VideoWriter):
                        vid_writer[i].release()
                    if vid_cap:
                        fps = vid_cap.get(cv2.CAP_PROP_FPS)
                        w_new = enhanced_canvas.shape[1]
                        h_new = enhanced_canvas.shape[0]
                    else:
                        fps, w_new, h_new = 30, enhanced_canvas.shape[1], enhanced_canvas.shape[0]
                    save_path = str(Path(save_path).with_suffix('.mp4'))
                    vid_writer[i] = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (w_new, h_new))
                vid_writer[i].write(enhanced_canvas)

    # 安全关闭OpenCV窗口
    try:
        cv2.destroyAllWindows()
    except Exception as e:
        print(f"关闭OpenCV窗口时出现错误（可忽略）: {e}")

    # Print results
    t = tuple(x / seen * 1E3 for x in dt)
    print(f'处理完成！')
    print(f'最终计数 -> 左侧: {count}, 右侧: {count2}, 总计: {count + count2}')
    print(f'速度: %.1fms 预处理, %.1fms 推理, %.1fms NMS, %.1fms DeepSORT更新' % t)
    if save_txt or save_vid:
        s = f"\n{len(list(save_dir.glob('tracks/*.txt')))} 轨迹保存到 {save_dir / 'tracks'}" if save_txt else ''
        print(f"结果保存到 {save_dir}{s}")

def count_obj(box, w, h, id):
    """计数函数 - 与原版保持一致"""
    global count, count2, data
    center_coor = (int(box[0] + (box[2]-box[0])/2), int(box[1] + (box[3] - box[1])/2))
    if int(box[1] + (box[3] - box[1])/2) > h - UIConfig.LINE_Y_OFFSET and id not in data:
        if int(box[0] + (box[2]-box[0])/2) > int(w/2):
            count2 += 1
        else:
            count += 1
        data.append(id)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--yolo_model', nargs='+', type=str, default='yolov5m.pt', help='model.pt path(s)')
    parser.add_argument('--deep_sort_model', type=str, default='osnet_ibn_x1_0_MSMT17')
    parser.add_argument('--source', type=str, default='example.mp4', help='source')
    parser.add_argument('--output', type=str, default='inference/output', help='output folder')
    parser.add_argument('--imgsz', '--img', '--img-size', nargs='+', type=int, default=[640], help='inference size h,w')
    parser.add_argument('--conf-thres', type=float, default=0.5, help='object confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.5, help='IOU threshold for NMS')
    parser.add_argument('--fourcc', type=str, default='mp4v', help='output video codec (verify ffmpeg support)')
    parser.add_argument('--device', default='', help='cuda device, i.e. 0 or 0,1,2,3 or cpu')
    parser.add_argument('--show-vid', action='store_true', help='display tracking video results', default=False)  # 默认关闭
    parser.add_argument('--save-vid', action='store_true', help='save video tracking results', default=True)   # 默认保存
    parser.add_argument('--save-txt', action='store_true', help='save MOT compliant results to *.txt')
    parser.add_argument('--classes', nargs='+', type=int, help='filter by class: --class 0, or --class 16 17')
    parser.add_argument('--agnostic-nms', action='store_true', help='class-agnostic NMS')
    parser.add_argument('--augment', action='store_true', help='augmented inference')
    parser.add_argument('--update', action='store_true', help='update all models')
    parser.add_argument('--evaluate', action='store_true', help='augmented inference')
    parser.add_argument("--config_deepsort", type=str, default="tracker/configs/deep_sort.yaml")
    parser.add_argument("--half", action="store_true", help="use FP16 half-precision inference")
    parser.add_argument('--visualize', action='store_true', help='visualize features')
    parser.add_argument('--max-det', type=int, default=300, help='maximum detection per image')  # 降低默认值
    parser.add_argument('--save-crop', action='store_true', help='save cropped prediction boxes')
    parser.add_argument('--dnn', action='store_true', help='use OpenCV DNN for ONNX inference')
    parser.add_argument('--project', default=ROOT / 'runs/track', help='save results to project/name')
    parser.add_argument('--name', default='exp', help='save results to project/name')
    parser.add_argument('--exist-ok', action='store_true', help='existing project/name ok, do not increment')
    opt = parser.parse_args()

    with torch.no_grad():
        detect(opt)