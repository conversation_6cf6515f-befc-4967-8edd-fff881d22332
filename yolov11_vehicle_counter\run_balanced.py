#!/usr/bin/env python3
"""
平衡优化版本 - 在性能和精度之间找到最佳平衡
目标：8-10 FPS，保持检测精度
"""

import os
import sys
from pathlib import Path
import subprocess

def main():
    print("⚖️  车辆计数系统 - 平衡优化版本")
    print("=" * 50)
    print("🎯 目标: 8-10 FPS + 高精度检测")
    print("⚡ 策略: 适度优化，保持质量")
    print()
    
    # 检测环境
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    
    if not local_model.exists():
        print("❌ 未找到模型文件")
        input("按回车键退出...")
        return
    
    print("✅ 检测到本地环境")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 设置平衡优化环境变量
    env = os.environ.copy()
    env['BALANCED_MODE'] = '1'
    env['YOLO_CONF_THRESHOLD'] = '0.6'      # 适中的置信度
    env['YOLO_IOU_THRESHOLD'] = '0.7'       # 标准IOU
    env['YOLO_MAX_DETECTIONS'] = '100'      # 适中的检测数量
    env['YOLO_IMG_SIZE'] = '640'            # 标准分辨率
    
    print("⚙️  平衡优化配置:")
    print("   • 置信度阈值: 0.6 (适中)")
    print("   • IOU阈值: 0.7 (标准)")
    print("   • 最大检测数: 100 (适中)")
    print("   • 输入尺寸: 640x640 (标准)")
    print("   • 使用GPU加速")
    print("   • 保持DeepSORT跟踪精度")
    print()
    
    # 使用改进的main_final_version.py
    cmd = [sys.executable, 'main_final_version.py']
    
    print("🔄 开始平衡优化处理...")
    print("📝 命令:", ' '.join(cmd))
    print()
    
    try:
        result = subprocess.run(cmd, env=env, check=True)
        print()
        print("✅ 平衡优化处理完成！")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()