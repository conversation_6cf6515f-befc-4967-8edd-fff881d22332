# YOLOv11 Vehicle Counter - File Upload Script
# Run this script on your Windows PC

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "YOLOv11 Vehicle Counter - File Upload" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Server IP: ************" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if local files exist
Write-Host "Checking local files..." -ForegroundColor Blue

$webappDir = "yolov11_vehicle_counter\webapp"
$requiredFiles = @("app.py", "requirements.txt", "index.html")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    $filePath = Join-Path $webappDir $file
    if (-not (Test-Path $filePath)) {
        $missingFiles += $file
    }
}

if (-not (Test-Path (Join-Path $webappDir "static"))) {
    $missingFiles += "static directory"
}

if ($missingFiles.Count -gt 0) {
    Write-Host "ERROR: Missing files: $($missingFiles -join ', ')" -ForegroundColor Red
    Write-Host "Please run this script in project root directory D:\lkr_yolo" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "SUCCESS: Local files check passed" -ForegroundColor Green
Write-Host ""

Write-Host "Starting file upload to server..." -ForegroundColor Blue
Write-Host "NOTE: You need to enter server password for each file" -ForegroundColor Yellow
Write-Host ""

# Upload files
$serverUser = "admin@************"

Write-Host "Uploading app.py..." -ForegroundColor Cyan
& scp "$webappDir\app.py" "${serverUser}:~/"

Write-Host ""
Write-Host "Uploading requirements.txt..." -ForegroundColor Cyan
& scp "$webappDir\requirements.txt" "${serverUser}:~/"

Write-Host ""
Write-Host "Uploading index.html..." -ForegroundColor Cyan
& scp "$webappDir\index.html" "${serverUser}:~/"

Write-Host ""
Write-Host "Uploading static directory..." -ForegroundColor Cyan
& scp -r "$webappDir\static" "${serverUser}:~/"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "File upload completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Login to server: ssh admin@************" -ForegroundColor White
Write-Host "2. Upload server script: scp server_deploy.sh admin@************:~/" -ForegroundColor White
Write-Host "3. Run deployment: chmod +x server_deploy.sh && ./server_deploy.sh" -ForegroundColor White
Write-Host ""
Write-Host "If upload fails, use WinSCP:" -ForegroundColor Yellow
Write-Host "https://winscp.net/eng/download.php" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
