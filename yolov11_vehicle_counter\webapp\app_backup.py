#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - Web应用后端
Flask API服务器
"""

import os
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root / "src"))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from flask import Flask, request, jsonify, send_file, render_template_string
from flask_cors import CORS
import cv2
import numpy as np
from ultralytics import YOLO
import tempfile
import threading
import time
import json
from datetime import datetime
import uuid

app = Flask(__name__)
CORS(app)

# 全局变量
processing_status = {}
results_storage = {}

# COCO类别映射
CLASS_NAMES = {
    0: 'person',
    1: 'bicycle', 
    2: 'car',
    3: 'motorcycle',
    4: 'airplane',
    5: 'bus',
    6: 'train',
    7: 'truck',
    8: 'boat'
}

def is_vehicle(class_id):
    """判断是否为车辆"""
    vehicle_classes = {1, 2, 3, 5, 7}  # bicycle, car, motorcycle, bus, truck
    return int(class_id) in vehicle_classes

def get_clean_id(original_id, id_mapping, next_id_counter):
    """获取清洁的ID"""
    if original_id not in id_mapping:
        id_mapping[original_id] = next_id_counter[0]
        next_id_counter[0] += 1
    return id_mapping[original_id]

def process_video_task(task_id, video_path, model_path, settings):
    """后台处理视频任务 - 修复版本"""
    try:
        # 更新状态
        processing_status[task_id] = {
            'status': 'processing',
            'progress': 0,
            'message': '正在加载模型...',
            'vehicle_count': 0,
            'people_count': 0,
            'start_time': datetime.now().isoformat()
        }
        
        print(f"🚗 开始处理任务 {task_id}")
        print(f"📹 视频路径: {video_path}")
        print(f"🤖 模型路径: {model_path}")
        
        # 加载模型
        model = YOLO(model_path)
        print("✅ 模型加载成功")
        
        # 打开视频
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            processing_status[task_id]['status'] = 'error'
            processing_status[task_id]['message'] = '无法打开视频文件'
            return
        
        # 获取视频属性
        w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📊 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
        
        line_x = w // 2
        
        # 设置输出视频
        output_path = Path(tempfile.gettempdir()) / f"processed_{task_id}.mp4"
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (w, h))
        
        # 初始化跟踪变量
        track_history = {}
        vehicle_crossed_ids = set()
        person_seen_ids = set()
        original_to_clean_id = {}
        next_clean_id = [1]
        
        frame_count = 0
        vehicle_left_count = 0
        vehicle_right_count = 0
        
        processing_status[task_id]['message'] = '开始处理视频帧...'
        
        # 处理每一帧
        while cap.isOpened():
            success, frame = cap.read()
            if not success:
                break
            
            frame_count += 1
            
            # 更新进度
            progress = (frame_count / total_frames) * 100
            processing_status[task_id]['progress'] = progress
            processing_status[task_id]['message'] = f'处理第 {frame_count}/{total_frames} 帧'
            
            # YOLO跟踪 - 使用和main_beautiful_1080p.py相同的参数
            results = model.track(frame, persist=True, verbose=False)
            
            # 绘制美观的检测框
            annotated_frame = draw_beautiful_detections(frame.copy(), results, line_x, track_history)
            
            # 处理跟踪结果 - 使用我们的计数逻辑
            if results[0].boxes is not None and results[0].boxes.id is not None:
                boxes = results[0].boxes.xywh.cpu()
                class_ids = results[0].boxes.cls.cpu().numpy()
                original_track_ids = results[0].boxes.id.int().cpu().tolist()

                for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
                    center_x, center_y = int(box[0]), int(box[1])
                    clean_id = get_clean_id(original_id, original_to_clean_id, next_clean_id)
                    class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
                    
                    # 人员统计
                    if class_name == 'person':
                        if original_id not in person_seen_ids:
                            person_seen_ids.add(original_id)
                            print(f"👤 发现新人员 ID:{clean_id}")
                    
                    # 车辆穿越统计
                    elif is_vehicle(cls_id):
                        if original_id in track_history:
                            prev_x, _ = track_history[original_id]
                            if original_id not in vehicle_crossed_ids:
                                # 从左到右穿越
                                if prev_x < line_x and center_x >= line_x:
                                    vehicle_right_count += 1
                                    vehicle_crossed_ids.add(original_id)
                                    print(f"🚙 车辆 ID:{clean_id} ({class_name}) 从左到右穿越，右侧计数: {vehicle_right_count}")
                                # 从右到左穿越
                                elif prev_x > line_x and center_x <= line_x:
                                    vehicle_left_count += 1
                                    vehicle_crossed_ids.add(original_id)
                                    print(f"🚗 车辆 ID:{clean_id} ({class_name}) 从右到左穿越，左侧计数: {vehicle_left_count}")
                    
                    # 更新历史位置
                    track_history[original_id] = (center_x, center_y)
            
            # 创建美观UI覆盖层
            annotated_frame = create_beautiful_ui_overlay(
                annotated_frame, 
                vehicle_left_count, 
                vehicle_right_count, 
                len(person_seen_ids), 
                line_x
            )
            
            # 保存帧
            out.write(annotated_frame)
            
            # 更新实时统计
            processing_status[task_id]['vehicle_count'] = vehicle_left_count + vehicle_right_count
            processing_status[task_id]['people_count'] = len(person_seen_ids)
            
            # 控制处理速度
            speed = settings.get('speed', 'normal')
            if speed == 'fast' and frame_count % 10 != 0:
                continue
            elif speed == 'normal' and frame_count % 5 != 0:
                continue
            # detailed模式处理每一帧
        
        # 清理资源
        cap.release()
        out.release()
        
        print(f"✅ 处理完成: 车辆{vehicle_left_count + vehicle_right_count}辆, 人员{len(person_seen_ids)}人")
        
        # 保存结果
        results_storage[task_id] = {
            'video_path': str(output_path),
            'vehicle_left': vehicle_left_count,
            'vehicle_right': vehicle_right_count,
            'vehicle_total': vehicle_left_count + vehicle_right_count,
            'people_total': len(person_seen_ids),
            'processed_frames': frame_count,
            'completion_time': datetime.now().isoformat()
        }
        
        # 更新最终状态
        processing_status[task_id]['status'] = 'completed'
        processing_status[task_id]['progress'] = 100
        processing_status[task_id]['message'] = '处理完成'
        
    except Exception as e:
        print(f"❌ 处理错误: {str(e)}")
        processing_status[task_id]['status'] = 'error'
        processing_status[task_id]['message'] = f'处理错误: {str(e)}'

@app.route('/')
def index():
    """返回主页"""
    return send_file('index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_file(f'static/{filename}')

@app.route('/api/upload', methods=['POST'])
def upload_video():
    """上传视频文件"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 保存上传的文件
        task_id = str(uuid.uuid4())
        temp_path = Path(tempfile.gettempdir()) / f"upload_{task_id}_{file.filename}"
        file.save(str(temp_path))
        
        return jsonify({
            'task_id': task_id,
            'filename': file.filename,
            'message': '文件上传成功'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/process', methods=['POST'])
def start_processing():
    """开始处理视频"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        settings = data.get('settings', {})
        
        if not task_id:
            return jsonify({'error': '缺少任务ID'}), 400
        
        # 获取上传的文件路径
        temp_files = list(Path(tempfile.gettempdir()).glob(f"upload_{task_id}_*"))
        if not temp_files:
            return jsonify({'error': '找不到上传的文件'}), 404
        
        video_path = temp_files[0]
        
        # 使用我们训练的模型
        model_path = str(Path(__file__).parent.parent / "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt")
        
        # 如果自定义模型不存在，使用预训练模型
        if not Path(model_path).exists():
            model_path = "yolov11m.pt"
        
        # 启动后台处理任务
        thread = threading.Thread(
            target=process_video_task,
            args=(task_id, video_path, model_path, settings)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'message': '开始处理视频'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """获取处理状态"""
    if task_id not in processing_status:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(processing_status[task_id])

@app.route('/api/results/<task_id>')
def get_results(task_id):
    """获取处理结果"""
    if task_id not in results_storage:
        return jsonify({'error': '结果不存在'}), 404
    
    return jsonify(results_storage[task_id])

@app.route('/api/download/<task_id>')
def download_video(task_id):
    """下载处理后的视频"""
    if task_id not in results_storage:
        return jsonify({'error': '结果不存在'}), 404
    
    video_path = results_storage[task_id]['video_path']
    if not Path(video_path).exists():
        return jsonify({'error': '视频文件不存在'}), 404
    
    return send_file(video_path, as_attachment=True, download_name=f'processed_{task_id}.mp4')

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'active_tasks': len(processing_status)
    })

# 添加缺失的函数
def draw_beautiful_detections(frame, results, line_x):
    """绘制美观的检测框"""
    if results[0].boxes is not None:
        for box in results[0].boxes:
            # 获取边界框坐标
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)

            # 绘制边界框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 绘制类别标签
            if box.cls is not None:
                class_id = int(box.cls[0])
                class_name = CLASS_NAMES.get(class_id, f'class_{class_id}')
                cv2.putText(frame, class_name, (x1, y1-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # 绘制计数线
    cv2.line(frame, (line_x, 0), (line_x, frame.shape[0]), (255, 0, 0), 2)

    return frame

def create_beautiful_ui_overlay(frame, left_count, right_count, people_count, line_x):
    """创建美观的UI覆盖层"""
    h, w = frame.shape[:2]

    # 创建半透明背景
    overlay = frame.copy()
    cv2.rectangle(overlay, (10, 10), (300, 120), (0, 0, 0), -1)
    frame = cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)

    # 添加文字信息
    cv2.putText(frame, f"Left: {left_count}", (20, 40),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
    cv2.putText(frame, f"Right: {right_count}", (20, 70),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
    cv2.putText(frame, f"People: {people_count}", (20, 100),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)

    return frame

if __name__ == '__main__':
    print("🚗 启动YOLOv11车辆计数器Web应用...")
    print("🌐 访问地址: http://localhost:5000")
    print("📱 支持功能:")
    print("   - 视频上传和处理")
    print("   - 实时处理进度")
    print("   - 结果可视化")
    print("   - 视频下载")
    print("=" * 50)

    # 生产环境配置
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    app.run(host='0.0.0.0', port=5000, debug=debug_mode)