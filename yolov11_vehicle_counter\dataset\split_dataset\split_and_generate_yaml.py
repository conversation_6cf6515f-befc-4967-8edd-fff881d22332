import os
import shutil
import yaml

# 路径配置（全部相对路径，便于迁移）
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
IMG_DIR = os.path.join(ROOT, 'image')
LABEL_DIR = os.path.join(ROOT, 'label')
SPLIT_ROOT = os.path.join(ROOT, 'split_dataset')
IMG_OUT = os.path.join(SPLIT_ROOT, 'images')
LABEL_OUT = os.path.join(SPLIT_ROOT, 'labels')

# 检查目录是否存在
if not os.path.exists(IMG_DIR):
    raise FileNotFoundError(f"图片目录不存在: {IMG_DIR}")
if not os.path.exists(LABEL_DIR):
    raise FileNotFoundError(f"标签目录不存在: {LABEL_DIR}")

# 创建输出目录结构
for split in ['train', 'val', 'test']:
    os.makedirs(os.path.join(IMG_OUT, split), exist_ok=True)
    os.makedirs(os.path.join(LABEL_OUT, split), exist_ok=True)

# 交错分配比例
SPLIT_PATTERN = ['train']*7 + ['val']*2 + ['test']

img_files = sorted([f for f in os.listdir(IMG_DIR) if f.lower().endswith('.jpg')])

for idx, img_name in enumerate(img_files):
    split = SPLIT_PATTERN[idx % len(SPLIT_PATTERN)]
    label_name = img_name.replace('.jpg', '.txt')
    # 目标路径
    img_dst = os.path.join(IMG_OUT, split, img_name)
    label_dst = os.path.join(LABEL_OUT, split, label_name)
    # 源路径
    img_src = os.path.join(IMG_DIR, img_name)
    label_src = os.path.join(LABEL_DIR, label_name)
    # 复制图片
    shutil.copy2(img_src, img_dst)
    # 复制标签（如标签不存在则跳过）
    if os.path.exists(label_src):
        shutil.copy2(label_src, label_dst)
    else:
        print(f'警告: 未找到标签 {label_src}')

# 生成data.yaml（相对路径，便于迁移）
yaml_dict = {
    'train': 'images/train',
    'val': 'images/val',
    'test': 'images/test',
    'nc': 2,
    'names': ['car', 'people']
}
with open(os.path.join(SPLIT_ROOT, 'data.yaml'), 'w', encoding='utf-8') as f:
    yaml.dump(yaml_dict, f, allow_unicode=True)

print('分割与配置完成，原始数据未做任何删除！') 