#!/usr/bin/env python3
"""
GPU优化运行脚本 - 自动检测并使用GPU加速
"""

import os
import sys
from pathlib import Path
import subprocess

def detect_environment():
    """自动检测环境并返回相应的路径"""
    # 本地环境路径
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    # 服务器环境路径
    server_model = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt')
    server_video = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    if local_model.exists():
        print("✅ 检测到本地环境")
        return {
            'model': str(local_model),
            'video': str(local_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'local'
        }
    elif server_model.exists():
        print("✅ 检测到服务器环境")
        return {
            'model': str(server_model),
            'video': str(server_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'server'
        }
    else:
        print("❌ 未找到有效的环境配置")
        return None

def check_gpu():
    """检查GPU可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"🚀 检测到GPU: {gpu_name}")
            print(f"💾 GPU内存: {gpu_memory:.1f}GB")
            return True
        else:
            print("⚠️  未检测到可用GPU，将使用CPU")
            return False
    except ImportError:
        print("⚠️  PyTorch未安装，无法检测GPU")
        return False

def main():
    print("🚀 车辆计数系统 - GPU优化版本")
    print("=" * 60)
    print()
    
    # 检测环境
    config = detect_environment()
    if not config:
        input("按回车键退出...")
        return
    
    # 检测GPU
    gpu_available = check_gpu()
    
    print(f"🌍 环境类型: {config['env']}")
    print(f"🤖 YOLO模型: {config['model']}")
    print(f"🎥 视频文件: {config['video']}")
    print(f"🔍 ReID模型: {config['reid']}")
    print(f"⚡ GPU加速: {'启用' if gpu_available else '禁用'}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 使用改进的main_final_version.py
    cmd = [sys.executable, 'main_final_version.py']
    
    print("🔄 开始GPU优化处理...")
    print("📝 命令:", ' '.join(cmd))
    print()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True)
        print()
        print("✅ 处理完成！")
        
        if gpu_available:
            print("🚀 GPU加速已启用，处理速度应该更快")
        
        print("📁 检查以下位置查看结果:")
        print("   • video/ 目录 - 输出视频")
        print("   • logs/ 目录 - 详细日志")
        print("   • results/ 目录 - 实验记录")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
        print("\n💡 故障排除建议:")
        print("   1. 检查GPU驱动是否正确安装")
        print("   2. 确认PyTorch GPU版本已安装")
        print("   3. 检查CUDA版本兼容性")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == '__main__':
    main()