#!/usr/bin/env python3
"""
环境管理器 - 自动检测环境并配置路径
解决跨环境兼容性问题
"""

import os
import sys
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Optional
import logging

@dataclass
class EnvironmentConfig:
    """环境配置数据类"""
    environment_type: str  # 'local' or 'server'
    base_path: Path
    model_paths: Dict[str, Path]
    video_paths: Dict[str, Path]
    output_paths: Dict[str, Path]
    has_gui: bool = True

class EnvironmentManager:
    """环境管理器 - 自动检测环境并配置路径"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = None
        
    def detect_environment(self) -> EnvironmentConfig:
        """自动检测当前运行环境"""
        try:
            # 检测GUI支持
            has_gui = self._check_gui_support()
            
            # 检测环境类型
            current_path = Path.cwd()
            
            # 检查是否在本地开发环境
            if "D:" in str(current_path) or "lkr_yolo" in str(current_path):
                environment_type = "local"
                base_path = Path("D:/lkr_yolo/yolov11_vehicle_counter")
            else:
                environment_type = "server"
                base_path = Path("C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter")
            
            # 如果基础路径不存在，尝试当前目录
            if not base_path.exists():
                base_path = current_path
                if "yolov11_vehicle_counter" not in str(base_path):
                    # 尝试找到项目根目录
                    for parent in current_path.parents:
                        if "yolov11_vehicle_counter" in str(parent):
                            base_path = parent
                            break
            
            # 配置路径
            model_paths = self._get_model_paths(base_path)
            video_paths = self._get_video_paths(base_path)
            output_paths = self._get_output_paths(base_path)
            
            config = EnvironmentConfig(
                environment_type=environment_type,
                base_path=base_path,
                model_paths=model_paths,
                video_paths=video_paths,
                output_paths=output_paths,
                has_gui=has_gui
            )
            
            self.config = config
            self.logger.info(f"检测到环境: {environment_type}, 基础路径: {base_path}")
            self.logger.info(f"GUI支持: {has_gui}")
            
            return config
            
        except Exception as e:
            self.logger.error(f"环境检测失败: {e}")
            # 返回默认配置
            return self._get_default_config()
    
    def _check_gui_support(self) -> bool:
        """检查GUI支持"""
        try:
            import cv2
            # 尝试创建一个测试窗口
            test_img = cv2.imread("test", cv2.IMREAD_COLOR)
            if test_img is None:
                # 创建一个小的测试图像
                import numpy as np
                test_img = np.zeros((100, 100, 3), dtype=np.uint8)
            
            cv2.namedWindow("test_gui", cv2.WINDOW_NORMAL)
            cv2.imshow("test_gui", test_img)
            cv2.waitKey(1)
            cv2.destroyWindow("test_gui")
            return True
        except Exception as e:
            self.logger.warning(f"GUI不可用: {e}")
            return False
    
    def _get_model_paths(self, base_path: Path) -> Dict[str, Path]:
        """获取模型路径"""
        model_paths = {}
        
        # 主要模型路径
        main_model = base_path / "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
        if main_model.exists():
            model_paths["main"] = main_model
        
        # 备用模型路径
        backup_model = base_path / "runs/train/yolov11m_vehicle_detection/weights/best.pt"
        if backup_model.exists():
            model_paths["backup"] = backup_model
            
        # 如果没有找到模型，使用预训练模型
        if not model_paths:
            model_paths["pretrained"] = "yolov11m.pt"
            
        return model_paths
    
    def _get_video_paths(self, base_path: Path) -> Dict[str, Path]:
        """获取视频路径"""
        video_dir = base_path / "video"
        video_paths = {}
        
        if video_dir.exists():
            # 查找视频文件
            for ext in ['.mp4', '.avi', '.mov', '.mkv']:
                videos = list(video_dir.glob(f"*{ext}"))
                if videos:
                    video_paths["default"] = videos[0]
                    break
        
        return video_paths
    
    def _get_output_paths(self, base_path: Path) -> Dict[str, Path]:
        """获取输出路径"""
        output_dir = base_path / "video"
        output_dir.mkdir(exist_ok=True)
        
        return {
            "video": output_dir,
            "logs": base_path / "logs"
        }
    
    def _get_default_config(self) -> EnvironmentConfig:
        """获取默认配置"""
        current_path = Path.cwd()
        return EnvironmentConfig(
            environment_type="unknown",
            base_path=current_path,
            model_paths={"pretrained": "yolov11m.pt"},
            video_paths={},
            output_paths={"video": current_path, "logs": current_path},
            has_gui=False
        )
    
    def validate_dependencies(self) -> List[str]:
        """验证依赖项"""
        missing_deps = []
        
        try:
            import cv2
        except ImportError:
            missing_deps.append("opencv-python")
            
        try:
            from ultralytics import YOLO
        except ImportError:
            missing_deps.append("ultralytics")
            
        try:
            import numpy as np
        except ImportError:
            missing_deps.append("numpy")
            
        return missing_deps
    
    def get_config(self) -> Optional[EnvironmentConfig]:
        """获取当前配置"""
        if self.config is None:
            self.config = self.detect_environment()
        return self.config
    
    def safe_cv2_destroy_windows(self):
        """安全地销毁OpenCV窗口"""
        try:
            if self.config and self.config.has_gui:
                import cv2
                cv2.destroyAllWindows()
        except Exception as e:
            self.logger.warning(f"销毁窗口时出错（可忽略）: {e}")