/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-size: 18px; /* 进一步增大基础字体 */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 2rem; /* 增大导航logo字体 */
    font-weight: bold;
    color: #667eea;
}

.nav-logo i {
    margin-right: 0.8rem;
    font-size: 2.5rem; /* 增大图标 */
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 600; /* 增加字体粗细 */
    font-size: 1.1rem; /* 增大导航链接字体 */
    padding: 0.8rem 1.5rem; /* 增大内边距 */
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: #667eea;
    color: white;
}

/* 主要内容 */
.main-content {
    margin-top: 80px;
}

.section {
    display: none;
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.section.active {
    display: block;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title i {
    margin-right: 1rem;
}

/* 首页样式 */
.hero {
    text-align: center;
    padding: 4rem 0;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: white;
    margin-bottom: 1rem;
}

.feature-card p {
    color: rgba(255, 255, 255, 0.8);
}

.cta-button {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 上传区域 */
.upload-area {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 2rem;
    border: 2px dashed rgba(255, 255, 255, 0.3);
}

.upload-zone {
    text-align: center;
    padding: 2rem;
    transition: all 0.3s ease;
}

.upload-zone:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.05);
}

.upload-icon i {
    font-size: 4rem;
    color: #4ECDC4;
    margin-bottom: 1rem;
}

.upload-zone h3 {
    color: white;
    margin-bottom: 0.5rem;
}

.upload-zone p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.upload-hint {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

.upload-button {
    background: #4ECDC4;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.upload-button:hover {
    background: #45B7B8;
    transform: translateY(-2px);
}

/* 设置区域 */
.upload-settings {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.upload-settings h3 {
    color: white;
    margin-bottom: 1.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.setting-item label {
    color: white;
    font-weight: 500;
}

.setting-item select,
.setting-item input[type="range"] {
    padding: 0.5rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
}

#confidenceValue {
    color: #4ECDC4;
    font-weight: bold;
}

/* 文件信息 */
.file-info {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.file-info h3 {
    color: white;
    margin-bottom: 1.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.info-label {
    color: rgba(255, 255, 255, 0.8);
}

.info-item span:last-child {
    color: white;
    font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.process-button,
.reset-button {
    padding: 1rem 2rem;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.process-button {
    background: linear-gradient(45deg, #4ECDC4, #44A08D);
    color: white;
}

.process-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.reset-button {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.reset-button:hover {
    background: white;
    color: #667eea;
}

/* 处理进度 */
.processing-container {
    display: flex;
    gap: 3rem;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
}

.progress-circle {
    position: relative;
    width: 200px;
    height: 200px;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 565.48;
    stroke-dashoffset: 565.48;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.progress-text span {
    font-size: 2rem;
    font-weight: bold;
}

.progress-text small {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
}

.processing-info {
    flex: 1;
    max-width: 400px;
}

.processing-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
}

.stat-item i {
    font-size: 1.5rem;
    color: #4ECDC4;
    margin-bottom: 0.5rem;
}

.stat-label {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.processing-log {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 1rem;
}

.processing-log h4 {
    color: white;
    margin-bottom: 1rem;
}

.log-container {
    max-height: 150px;
    overflow-y: auto;
}

.log-item {
    display: flex;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-time {
    color: #4ECDC4;
    font-size: 0.9rem;
    min-width: 60px;
}

.log-message {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

.processing-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.pause-button,
.stop-button {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.pause-button {
    background: #FFA726;
    color: white;
}

.stop-button {
    background: #EF5350;
    color: white;
}

/* 结果页面 */
.results-container {
    max-width: 1200px;
    margin: 0 auto;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 3rem;
    color: #4ECDC4;
    margin-bottom: 1rem;
}

.stat-content h3 {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-content p {
    color: rgba(255, 255, 255, 0.8);
}

/* 视频播放器 */
.video-player-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
}

.video-player {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.video-player video {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.video-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
}

.video-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.video-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.video-progress {
    flex: 1;
}

.video-progress input[type="range"] {
    width: 100%;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    outline: none;
}

/* 分析容器 */
.analysis-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
}

.analysis-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 1rem 1.5rem;
    cursor: pointer;
    border-radius: 10px 10px 0 0;
    transition: all 0.3s ease;
}

.tab-button.active,
.tab-button:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.heatmap-container {
    text-align: center;
}

.details-table {
    overflow-x: auto;
}

.details-table table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.details-table th,
.details-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.details-table th {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: bold;
}

.details-table td {
    color: rgba(255, 255, 255, 0.9);
}

/* 下载区域 */
.download-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
}

.download-section h3 {
    color: white;
    margin-bottom: 2rem;
}

.download-options {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.download-btn {
    background: linear-gradient(45deg, #4ECDC4, #44A08D);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-width: 180px;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 关于页面 */
.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.about-text {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
}

.about-text h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.about-text ul {
    list-style: none;
    margin-bottom: 2rem;
}

.about-text li {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.about-text li:before {
    content: "▶";
    color: #4ECDC4;
    position: absolute;
    left: 0;
}

.tech-stack {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
}

.tech-stack h3 {
    color: white;
    margin-bottom: 1.5rem;
    text-align: center;
}

.tech-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateX(5px);
}

.tech-item i {
    font-size: 1.5rem;
    color: #4ECDC4;
}

.tech-item span {
    color: white;
    font-weight: 500;
}

/* 页脚 */
.footer {
    background: rgba(0, 0, 0, 0.3);
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid #4ECDC4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-features {
        grid-template-columns: 1fr;
    }
    
    .processing-container {
        flex-direction: column;
        text-align: center;
    }
    
    .about-content {
        grid-template-columns: 1fr;
    }
    
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .download-options {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
}