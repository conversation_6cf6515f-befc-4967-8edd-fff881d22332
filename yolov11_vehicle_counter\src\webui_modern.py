#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 现代化Web界面
基于main_beautiful_1080p.py的Web版本
使用Gradio创建用户友好的Web界面
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
import gradio as gr
import tempfile
import threading
import time
from pathlib import Path
from datetime import datetime
from ultralytics import YOLO

# 导入我们的核心功能
import sys
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# --- Global Variables ---
track_history = {}
vehicle_crossed_ids = set()
person_seen_ids = set()
original_to_clean_id = {}
next_clean_id = 1

# COCO类别映射
CLASS_NAMES = {
    0: 'person',
    1: 'bicycle', 
    2: 'car',
    3: 'motorcycle',
    4: 'airplane',
    5: 'bus',
    6: 'train',
    7: 'truck',
    8: 'boat'
}

def get_clean_id(original_id):
    """获取清洁的ID（从1开始按顺序分配）"""
    global next_clean_id, original_to_clean_id
    
    if original_id not in original_to_clean_id:
        original_to_clean_id[original_id] = next_clean_id
        next_clean_id += 1
    
    return original_to_clean_id[original_id]

def is_vehicle(class_id):
    """判断是否为车辆"""
    vehicle_classes = {1, 2, 3, 5, 7}  # bicycle, car, motorcycle, bus, truck
    return int(class_id) in vehicle_classes

def reset_counters():
    """重置所有计数器"""
    global track_history, vehicle_crossed_ids, person_seen_ids, original_to_clean_id, next_clean_id
    track_history.clear()
    vehicle_crossed_ids.clear()
    person_seen_ids.clear()
    original_to_clean_id.clear()
    next_clean_id = 1

def create_beautiful_ui_overlay(frame, vehicle_left, vehicle_right, person_total, line_x):
    """创建美观的UI覆盖层"""
    h, w = frame.shape[:2]
    
    # 创建半透明覆盖层
    overlay = frame.copy()
    
    # 左上角 - 车辆穿越统计区域
    cv2.rectangle(overlay, (10, 10), (320, 120), (40, 40, 139), -1)  # 深蓝色
    # 右上角 - 人员总数统计区域
    cv2.rectangle(overlay, (w-330, 10), (w-10, 120), (40, 139, 40), -1)  # 深绿色
    
    # 混合原图和覆盖层
    alpha = 0.8
    frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
    
    # 字体设置
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 1.2
    thickness = 2
    
    # 左上角 - 车辆穿越统计
    cv2.putText(frame, "VEHICLES", (20, 35), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"L:{vehicle_left:02d} R:{vehicle_right:02d}", (20, 65), font, 0.9, (255, 255, 255), 2)
    cv2.putText(frame, f"Total: {vehicle_left + vehicle_right:02d}", (20, 95), font, font_scale, (0, 255, 255), thickness+1)
    
    # 右上角 - 人员总数统计
    cv2.putText(frame, "PEOPLE", (w-310, 35), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"Total: {person_total:02d}", (w-250, 75), font, font_scale, (255, 255, 255), thickness+1)
    
    # 绘制计数线
    cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 5)
    
    # 计数线标签
    label_y = 10
    cv2.rectangle(frame, (line_x-60, label_y), (line_x+60, label_y+30), (0, 255, 0), -1)
    cv2.putText(frame, "COUNT LINE", (line_x-55, label_y+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
    
    return frame

def draw_beautiful_detections(frame, results, line_x):
    """绘制美观的检测框"""
    if results[0].boxes is None or results[0].boxes.id is None:
        return frame
    
    boxes = results[0].boxes.xyxy.cpu().numpy()
    confidences = results[0].boxes.conf.cpu().numpy()
    class_ids = results[0].boxes.cls.cpu().numpy()
    original_track_ids = results[0].boxes.id.int().cpu().tolist()
    
    for box, conf, cls_id, original_id in zip(boxes, confidences, class_ids, original_track_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # 获取清洁的ID
        clean_id = get_clean_id(original_id)
        
        # 获取正确的类别名称
        class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
        
        # 为不同类别选择鲜艳颜色
        if class_name == 'car':
            color = (0, 255, 255)  # 亮黄色
        elif class_name == 'truck':
            color = (255, 0, 255)  # 亮紫色
        elif class_name == 'bus':
            color = (0, 165, 255)  # 亮橙色
        elif class_name == 'motorcycle':
            color = (0, 255, 0)    # 亮绿色
        elif class_name == 'bicycle':
            color = (255, 192, 203)  # 亮粉色
        elif class_name == 'person':
            color = (255, 255, 255)  # 白色
        else:
            color = (128, 128, 255)  # 亮灰色
        
        # 绘制检测框
        thickness = 4 if is_vehicle(cls_id) else 3
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
        
        # 计算中心点
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # 绘制中心点
        cv2.circle(frame, (center_x, center_y), 8, color, -1)
        cv2.circle(frame, (center_x, center_y), 8, (0, 0, 0), 2)
        
        # 绘制轨迹
        if original_id in track_history:
            prev_x, prev_y = track_history[original_id]
            cv2.line(frame, (prev_x, prev_y), (center_x, center_y), color, 3)
        
        # 绘制标签
        label = f"ID:{clean_id} {class_name}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
        
        # 标签背景
        cv2.rectangle(frame, (x1, y1-35), (x1 + label_size[0] + 15, y1), color, -1)
        cv2.rectangle(frame, (x1, y1-35), (x1 + label_size[0] + 15, y1), (0, 0, 0), 2)
        
        # 标签文字
        cv2.putText(frame, label, (x1+7, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        # 如果靠近计数线，高亮显示
        if is_vehicle(cls_id) and abs(center_x - line_x) < 50:
            cv2.rectangle(frame, (x1-8, y1-8), (x2+8, y2+8), (0, 0, 255), 4)
            cv2.putText(frame, "COUNTING!", (x1, y2+25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
    
    return frame

def process_video_stream(video_path, model_path, progress_callback=None):
    """处理视频流并生成结果帧"""
    global track_history, vehicle_crossed_ids, person_seen_ids
    
    # 重置计数器
    reset_counters()
    
    # 加载模型
    model = YOLO(model_path)
    
    # 打开视频
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        yield None, "无法打开视频文件", 0, 0, 0
        return
    
    # 获取视频属性
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    line_x = w // 2
    frame_count = 0
    vehicle_left_count, vehicle_right_count = 0, 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            break
        
        frame_count += 1
        
        # YOLO跟踪
        results = model.track(frame, persist=True, verbose=False)
        
        # 绘制检测结果
        annotated_frame = draw_beautiful_detections(frame.copy(), results, line_x)
        
        # 处理跟踪结果
        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            class_ids = results[0].boxes.cls.cpu().numpy()
            original_track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                clean_id = get_clean_id(original_id)
                class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
                
                # 人员统计
                if class_name == 'person':
                    if original_id not in person_seen_ids:
                        person_seen_ids.add(original_id)
                
                # 车辆穿越统计
                elif is_vehicle(cls_id):
                    if original_id in track_history:
                        prev_x, _ = track_history[original_id]
                        if original_id not in vehicle_crossed_ids:
                            # 从左到右穿越
                            if prev_x < line_x and center_x >= line_x:
                                vehicle_right_count += 1
                                vehicle_crossed_ids.add(original_id)
                            # 从右到左穿越
                            elif prev_x > line_x and center_x <= line_x:
                                vehicle_left_count += 1
                                vehicle_crossed_ids.add(original_id)
                
                # 更新历史位置
                track_history[original_id] = (center_x, center_y)

        # 创建UI覆盖层
        annotated_frame = create_beautiful_ui_overlay(
            annotated_frame, 
            vehicle_left_count, 
            vehicle_right_count, 
            len(person_seen_ids), 
            line_x
        )
        
        # 添加底部状态栏
        progress = frame_count / total_frames if total_frames > 0 else 0
        status_y = h - 40
        cv2.rectangle(annotated_frame, (0, status_y), (w, h), (0, 0, 0), -1)
        
        status_text = f"Frame: {frame_count}/{total_frames} ({progress*100:.1f}%)"
        cv2.putText(annotated_frame, status_text, (10, status_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 转换颜色空间用于Gradio显示
        annotated_frame_rgb = cv2.cvtColor(annotated_frame, cv2.COLOR_BGR2RGB)
        
        # 生成统计信息
        stats = f"""
        📊 实时统计:
        🚗 车辆穿越: {vehicle_left_count + vehicle_right_count} 辆
           ⬅️ 左侧: {vehicle_left_count} 辆
           ➡️ 右侧: {vehicle_right_count} 辆
        👥 人员总数: {len(person_seen_ids)} 人
        📹 处理进度: {frame_count}/{total_frames} ({progress*100:.1f}%)
        """
        
        yield annotated_frame_rgb, stats, vehicle_left_count + vehicle_right_count, len(person_seen_ids), progress*100
        
        # 控制处理速度，避免过快
        if frame_count % 5 == 0:  # 每5帧yield一次
            time.sleep(0.01)
    
    cap.release()

def process_video_gradio(video_file, model_choice):
    """Gradio接口函数"""
    if video_file is None:
        return None, "请上传视频文件", 0, 0, 0
    
    # 模型路径映射
    model_paths = {
        "YOLOv11m (推荐)": "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
        "YOLOv11m 备用": "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt",
        "YOLOv11m 预训练": "yolov11m.pt"
    }
    
    model_path = model_paths.get(model_choice, model_paths["YOLOv11m (推荐)"])
    
    # 处理视频并返回最后一帧的结果
    last_frame = None
    last_stats = ""
    last_vehicles = 0
    last_people = 0
    last_progress = 0
    
    for frame, stats, vehicles, people, progress in process_video_stream(video_file, model_path):
        if frame is not None:
            last_frame = frame
            last_stats = stats
            last_vehicles = vehicles
            last_people = people
            last_progress = progress
    
    return last_frame, last_stats, last_vehicles, last_people, last_progress

def create_gradio_interface():
    """创建Gradio Web界面"""
    
    # 自定义CSS样式
    css = """
    .gradio-container {
        font-family: 'Arial', sans-serif;
    }
    .stats-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin: 10px 0;
    }
    """
    
    with gr.Blocks(css=css, title="YOLOv11 车辆计数器 - Web版") as demo:
        gr.Markdown("""
        # 🚗 YOLOv11 车辆计数器 - Web版
        
        ## ✨ 功能特点
        - 🎯 **智能车辆检测**: 基于YOLOv11的高精度车辆识别
        - 📊 **实时计数统计**: 分别统计左右穿越车辆和人员总数
        - 🎨 **美观可视化**: 1080p优化的界面设计
        - 🆔 **清洁ID管理**: 从1开始的连续ID编号
        - 🌐 **Web端访问**: 随时随地通过浏览器使用
        
        ## 📝 使用说明
        1. 上传视频文件（支持MP4、AVI等格式）
        2. 选择YOLO模型（推荐使用默认模型）
        3. 点击"开始处理"按钮
        4. 等待处理完成，查看结果
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # 输入区域
                gr.Markdown("### 📤 输入设置")
                
                video_input = gr.Video(
                    label="上传视频文件",
                    sources=["upload"],
                    height=300
                )
                
                model_choice = gr.Dropdown(
                    choices=["YOLOv11m (推荐)", "YOLOv11m 备用", "YOLOv11m 预训练"],
                    value="YOLOv11m (推荐)",
                    label="选择YOLO模型"
                )
                
                process_btn = gr.Button("🚀 开始处理", variant="primary", size="lg")
                
                # 统计信息显示
                gr.Markdown("### 📊 统计结果")
                
                with gr.Row():
                    vehicle_count = gr.Number(label="🚗 车辆总数", value=0)
                    people_count = gr.Number(label="👥 人员总数", value=0)
                
                progress_bar = gr.Slider(
                    minimum=0, 
                    maximum=100, 
                    value=0, 
                    label="📈 处理进度 (%)",
                    interactive=False
                )
            
            with gr.Column(scale=2):
                # 输出区域
                gr.Markdown("### 📺 处理结果")
                
                output_image = gr.Image(
                    label="处理后的视频帧",
                    height=500
                )
                
                stats_text = gr.Textbox(
                    label="详细统计信息",
                    lines=8,
                    max_lines=10
                )
        
        # 示例和帮助
        gr.Markdown("""
        ### 💡 使用提示
        - **支持格式**: MP4, AVI, MOV, MKV等主流视频格式
        - **最佳分辨率**: 1080p视频效果最佳
        - **处理时间**: 根据视频长度和复杂度，通常需要几分钟
        - **计数原理**: 车辆穿越中央绿线时计数，人员按出现的唯一ID计数
        
        ### 🔧 技术说明
        - **检测模型**: YOLOv11m 车辆检测专用模型
        - **跟踪算法**: 内置跟踪器，支持ID持续跟踪
        - **计数逻辑**: 基于轨迹的穿越检测，避免重复计数
        """)
        
        # 绑定事件
        process_btn.click(
            fn=process_video_gradio,
            inputs=[video_input, model_choice],
            outputs=[output_image, stats_text, vehicle_count, people_count, progress_bar]
        )
    
    return demo

def main():
    """主函数"""
    print("🚗 启动YOLOv11车辆计数器Web界面...")
    print("🌐 Web界面特性:")
    print("   📱 响应式设计，支持各种设备")
    print("   🎨 美观的用户界面")
    print("   📊 实时统计显示")
    print("   🔄 进度跟踪")
    print("=" * 50)
    
    # 创建并启动Gradio界面
    demo = create_gradio_interface()
    
    # 启动服务器
    demo.launch(
        server_name="0.0.0.0",  # 允许外部访问
        server_port=7860,       # 端口号
        share=False,            # 本地测试时设为False，部署时可设为True
        debug=True,             # 调试模式
        show_error=True         # 显示错误信息
    )

if __name__ == "__main__":
    main()