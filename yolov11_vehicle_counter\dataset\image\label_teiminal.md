(yoloobb) D:\lkr_yolo>labelimg
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00000.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00000.xml
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00001.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00001.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00001.jpg -> Annotation:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00001.txt
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00000.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00000.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00000.jpg -> Annotation:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00000.txt
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00002.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00002.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00003.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00003.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00003.jpg -> Annotation:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00003.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00004.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00004.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00005.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00005.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00006.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00006.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00007.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00007.txt
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00008.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00008.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00009.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00009.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00011.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/CX.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00011.jpg -> Annotation:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00011.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00011.jpg -> Annotation:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00011.txt
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
[('car', [(3758, 1193), (3840, 1193), (3840, 1472), (3758, 1472)], None, None, False), ('people', [(3645, 1135), (3763, 1135), (3763, 1462), (3645, 1462)], None, None, False), ('people', [(3398, 1148), (3518, 1148), (3518, 1426), (3398, 1426)], None, None, False), ('people', [(3237, 1151), (3349, 1151), (3349, 1403), (3237, 1403)], None, None, False), ('people', [(2782, 1174), (2869, 1174), (2869, 1266), (2782, 1266)], None, None, False), ('people', [(2770, 1131), (2861, 1131), (2861, 1192), (2770, 1192)], None, None, False), ('people', [(2739, 1126), (2796, 1126), (2796, 1194), (2739, 1194)], None, None, False), ('people', [(2698, 1144), (2764, 1144), (2764, 1316), (2698, 1316)], None, None, False), ('people', [(2639, 1158), (2704, 1158), (2704, 1302), (2639, 1302)], None, None, False), ('people', [(2584, 1166), (2639, 1166), (2639, 1255), (2584, 1255)], None, None, False), ('people', [(2603, 1128), (2645, 1128), (2645, 1169), (2603, 1169)], None, None, False), ('people', [(2541, 1152), (2583, 1152), (2583, 1240), (2541, 1240)], None, None, False), ('people', [(2508, 1174), (2567, 1174), (2567, 1235), (2508, 1235)], None, None, False), ('car', [(1933, 1223), (3079, 1223), (3079, 1564), (1933, 1564)], None, None, False), ('people', [(1952, 1171), (2010, 1171), (2010, 1317), (1952, 1317)], None, None, False), ('people', [(1529, 1182), (1602, 1182), (1602, 1405), (1529, 1405)], None, None, False), ('people', [(1483, 1194), (1548, 1194), (1548, 1416), (1483, 1416)], None, None, False), ('people', [(1414, 1188), (1488, 1188), (1488, 1414), (1414, 1414)], None, None, False), ('people', [(1365, 1181), (1418, 1181), (1418, 1363), (1365, 1363)], None, None, False), ('people', [(1316, 1181), (1374, 1181), (1374, 1368), (1316, 1368)], None, None, False), ('people', [(1243, 1166), (1307, 1166), (1307, 1369), (1243, 1369)], None, None, False), ('car', [(1, 1087), (316, 1087), (316, 1600), (1, 1600)], None, None, False), ('people', [(935, 1191), (988, 1191), (988, 1310), (935, 1310)], None, None, False)]
[('car', [(3758, 1193), (3840, 1193), (3840, 1472), (3758, 1472)], None, None, False), ('people', [(3645, 1135), (3763, 1135), (3763, 1462), (3645, 1462)], None, None, False), ('people', [(3398, 1148), (3518, 1148), (3518, 1426), (3398, 1426)], None, None, False), ('people', [(3237, 1151), (3349, 1151), (3349, 1403), (3237, 1403)], None, None, False), ('people', [(2782, 1174), (2869, 1174), (2869, 1266), (2782, 1266)], None, None, False), ('people', [(2770, 1131), (2861, 1131), (2861, 1192), (2770, 1192)], None, None, False), ('people', [(2739, 1126), (2796, 1126), (2796, 1194), (2739, 1194)], None, None, False), ('people', [(2698, 1144), (2764, 1144), (2764, 1316), (2698, 1316)], None, None, False), ('people', [(2639, 1158), (2704, 1158), (2704, 1302), (2639, 1302)], None, None, False), ('people', [(2584, 1166), (2639, 1166), (2639, 1255), (2584, 1255)], None, None, False), ('people', [(2603, 1128), (2645, 1128), (2645, 1169), (2603, 1169)], None, None, False), ('people', [(2541, 1152), (2583, 1152), (2583, 1240), (2541, 1240)], None, None, False), ('people', [(2508, 1174), (2567, 1174), (2567, 1235), (2508, 1235)], None, None, False), ('car', [(1933, 1223), (3079, 1223), (3079, 1564), (1933, 1564)], None, None, False), ('people', [(1952, 1171), (2010, 1171), (2010, 1317), (1952, 1317)], None, None, False), ('people', [(1529, 1182), (1602, 1182), (1602, 1405), (1529, 1405)], None, None, False), ('people', [(1483, 1194), (1548, 1194), (1548, 1416), (1483, 1416)], None, None, False), ('people', [(1414, 1188), (1488, 1188), (1488, 1414), (1414, 1414)], None, None, False), ('people', [(1365, 1181), (1418, 1181), (1418, 1363), (1365, 1363)], None, None, False), ('people', [(1316, 1181), (1374, 1181), (1374, 1368), (1316, 1368)], None, None, False), ('people', [(1243, 1166), (1307, 1166), (1307, 1369), (1243, 1369)], None, None, False), ('car', [(1, 1087), (316, 1087), (316, 1600), (1, 1600)], None, None, False), ('people', [(935, 1191), (988, 1191), (988, 1310), (935, 1310)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00010.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00010.txt
[]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00011.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00011.txt
Cancel creation.
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00012.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00012.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00013.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00013.txt
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00013.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00013.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00014.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/sframe_00014.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00014.jpg -> Annotation:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00014.txt
Cancel creation.
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00015.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00015.txt
[('people', [(1296, 1062), (2038, 1062), (2038, 2160), (1296, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00014.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00014.txt
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00016.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00016.txt
[('people', [(3670, 1140), (3782, 1140), (3782, 1447), (3670, 1447)], None, None, False), ('people', [(3256, 1154), (3337, 1154), (3337, 1453), (3256, 1453)], None, None, False), ('people', [(3145, 1143), (3247, 1143), (3247, 1452), (3145, 1452)], None, None, False), ('people', [(3012, 1154), (3096, 1154), (3096, 1447), (3012, 1447)], None, None, False), ('people', [(122, 1171), (211, 1171), (211, 1519), (122, 1519)], None, None, False), ('people', [(5, 1205), (106, 1205), (106, 1482), (5, 1482)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00000.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00000.txt
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
[('car', [(2402, 488), (3840, 488), (3840, 1574), (2402, 1574)], None, None, False), ('people', [(2259, 1173), (2378, 1173), (2378, 1436), (2259, 1436)], None, None, False), ('people', [(1527, 1186), (1587, 1186), (1587, 1407), (1527, 1407)], None, None, False), ('people', [(1469, 1188), (1529, 1188), (1529, 1419), (1469, 1419)], None, None, False), ('people', [(1409, 1183), (1473, 1183), (1473, 1415), (1409, 1415)], None, None, False), ('people', [(231, 1172), (299, 1172), (299, 1516), (231, 1516)], None, None, False), ('people', [(1958, 1158), (2010, 1158), (2010, 1400), (1958, 1400)], None, None, False)]
[('car', [(3143, 468), (3840, 468), (3840, 1526), (3143, 1526)], None, None, False), ('people', [(2788, 1170), (2841, 1170), (2841, 1407), (2788, 1407)], None, None, False), ('people', [(2774, 1124), (2840, 1124), (2840, 1372), (2774, 1372)], None, None, False), ('people', [(2724, 1141), (2771, 1141), (2771, 1351), (2724, 1351)], None, None, False), ('people', [(2700, 1178), (2765, 1178), (2765, 1405), (2700, 1405)], None, None, False), ('people', [(2643, 1124), (2685, 1124), (2685, 1278), (2643, 1278)], None, None, False), ('people', [(2656, 1166), (2709, 1166), (2709, 1396), (2656, 1396)], None, None, False), ('people', [(2584, 1130), (2653, 1130), (2653, 1371), (2584, 1371)], None, None, False), ('people', [(2503, 1158), (2573, 1158), (2573, 1391), (2503, 1391)], None, None, False), ('people', [(2329, 1171), (2411, 1171), (2411, 1421), (2329, 1421)], None, None, False), ('people', [(1935, 1168), (2007, 1168), (2007, 1385), (1935, 1385)], None, None, False), ('people', [(1533, 1180), (1591, 1180), (1591, 1408), (1533, 1408)], None, None, False), ('people', [(1481, 1186), (1533, 1186), (1533, 1416), (1481, 1416)], None, None, False), ('people', [(1415, 1182), (1487, 1182), (1487, 1410), (1415, 1410)], None, None, False), ('people', [(258, 1170), (315, 1170), (315, 1523), (258, 1523)], None, None, False), ('car', [(2, 1340), (156, 1340), (156, 1569), (2, 1569)], None, None, False)]
[('people', [(3650, 1141), (3769, 1141), (3769, 1453), (3650, 1453)], None, None, False), ('people', [(3281, 1168), (3374, 1168), (3374, 1467), (3281, 1467)], None, None, False), ('people', [(3167, 1145), (3290, 1145), (3290, 1440), (3167, 1440)], None, None, False), ('people', [(2774, 1164), (2843, 1164), (2843, 1403), (2774, 1403)], None, None, False), ('people', [(2756, 1127), (2853, 1127), (2853, 1374), (2756, 1374)], None, None, False), ('people', [(2687, 1133), (2751, 1133), (2751, 1351), (2687, 1351)], None, None, False), ('people', [(2674, 1168), (2731, 1168), (2731, 1393), (2674, 1393)], None, None, False), ('people', [(2626, 1173), (2699, 1173), (2699, 1402), (2626, 1402)], None, None, False), ('people', [(2584, 1132), (2645, 1132), (2645, 1364), (2584, 1364)], None, None, False), ('people', [(2627, 1129), (2677, 1129), (2677, 1175), (2627, 1175)], None, None, False), ('people', [(2500, 1155), (2584, 1155), (2584, 1389), (2500, 1389)], None, None, False), ('people', [(2299, 1173), (2373, 1173), (2373, 1435), (2299, 1435)], None, None, False), ('people', [(2235, 1157), (2292, 1157), (2292, 1416), (2235, 1416)], None, None, False), ('people', [(1987, 1179), (2025, 1179), (2025, 1357), (1987, 1357)], None, None, False), ('people', [(1937, 1173), (2000, 1173), (2000, 1364), (1937, 1364)], None, None, False), ('people', [(1681, 1166), (1741, 1166), (1741, 1391), (1681, 1391)], None, None, False), ('people', [(1583, 1163), (1675, 1163), (1675, 1393), (1583, 1393)], None, None, False), ('people', [(1592, 1173), (1620, 1173), (1620, 1217), (1592, 1217)], None, None, False), ('people', [(1534, 1182), (1587, 1182), (1587, 1406), (1534, 1406)], None, None, False), ('people', [(1491, 1197), (1552, 1197), (1552, 1410), (1491, 1410)], None, None, False), ('people', [(1411, 1180), (1491, 1180), (1491, 1414), (1411, 1414)], None, None, False), ('people', [(937, 1204), (980, 1204), (980, 1317), (937, 1317)], None, None, False), ('people', [(929, 1186), (988, 1186), (988, 1293), (929, 1293)], None, None, False), ('car', [(1, 1217), (1052, 1217), (1052, 1581), (1, 1581)], None, None, False)]
[('people', [(3654, 1146), (3774, 1146), (3774, 1471), (3654, 1471)], None, None, False), ('people', [(3311, 1163), (3417, 1163), (3417, 1463), (3311, 1463)], None, None, False), ('people', [(3214, 1149), (3316, 1149), (3316, 1439), (3214, 1439)], None, None, False), ('people', [(2773, 1167), (2847, 1167), (2847, 1400), (2773, 1400)], None, None, False), ('people', [(2755, 1125), (2853, 1125), (2853, 1373), (2755, 1373)], None, None, False), ('people', [(2708, 1133), (2763, 1133), (2763, 1364), (2708, 1364)], None, None, False), ('people', [(2676, 1163), (2749, 1163), (2749, 1397), (2676, 1397)], None, None, False), ('people', [(2607, 1174), (2690, 1174), (2690, 1402), (2607, 1402)], None, None, False), ('people', [(2648, 1160), (2690, 1160), (2690, 1371), (2648, 1371)], None, None, False), ('people', [(2644, 1122), (2692, 1122), (2692, 1173), (2644, 1173)], None, None, False), ('people', [(2579, 1127), (2654, 1127), (2654, 1370), (2579, 1370)], None, None, False), ('people', [(2500, 1151), (2582, 1151), (2582, 1395), (2500, 1395)], None, None, False), ('people', [(2312, 1161), (2382, 1161), (2382, 1413), (2312, 1413)], None, None, False), ('people', [(2253, 1172), (2327, 1172), (2327, 1437), (2253, 1437)], None, None, False), ('people', [(1942, 1175), (2018, 1175), (2018, 1359), (1942, 1359)], None, None, False), ('car', [(878, 1196), (1877, 1196), (1877, 1555), (878, 1555)], None, None, False), ('people', [(1656, 1177), (1711, 1177), (1711, 1316), (1656, 1316)], None, None, False), ('people', [(1636, 1175), (1662, 1175), (1662, 1212), (1636, 1212)], None, None, False), ('people', [(1589, 1163), (1646, 1163), (1646, 1300), (1589, 1300)], None, None, False), ('people', [(1527, 1186), (1594, 1186), (1594, 1262), (1527, 1262)], None, None, False), ('people', [(1487, 1164), (1537, 1164), (1537, 1239), (1487, 1239)], None, None, False), ('people', [(1425, 1177), (1477, 1177), (1477, 1219), (1425, 1219)], None, None, False), ('people', [(244, 1189), (322, 1189), (322, 1522), (244, 1522)], None, None, False), ('people', [(1, 1185), (62, 1185), (62, 1448), (1, 1448)], None, None, False)]
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
[('car', [(3114, 1171), (3840, 1171), (3840, 1541), (3114, 1541)], None, None, False), ('people', [(3687, 1143), (3737, 1143), (3737, 1189), (3687, 1189)], None, None, False), ('people', [(2848, 1130), (2901, 1130), (2901, 1262), (2848, 1262)], None, None, False), ('people', [(2777, 1127), (2853, 1127), (2853, 1195), (2777, 1195)], None, None, False), ('people', [(2776, 1171), (2864, 1171), (2864, 1402), (2776, 1402)], None, None, False), ('people', [(2721, 1151), (2789, 1151), (2789, 1393), (2721, 1393)], None, None, False), ('people', [(2663, 1159), (2726, 1159), (2726, 1401), (2663, 1401)], None, None, False), ('people', [(2598, 1128), (2665, 1128), (2665, 1198), (2598, 1198)], None, None, False), ('people', [(2583, 1162), (2655, 1162), (2655, 1411), (2583, 1411)], None, None, False), ('people', [(2539, 1154), (2587, 1154), (2587, 1361), (2539, 1361)], None, None, False), ('people', [(2504, 1175), (2566, 1175), (2566, 1401), (2504, 1401)], None, None, False), ('people', [(2353, 1317), (2478, 1317), (2478, 1440), (2353, 1440)], None, None, False), ('people', [(2235, 1174), (2316, 1174), (2316, 1365), (2235, 1365)], None, None, False), ('people', [(1228, 1223), (2343, 1223), (2343, 1568), (1228, 1568)], None, None, False), ('people', [(1362, 1186), (1408, 1186), (1408, 1285), (1362, 1285)], None, None, False), ('people', [(1525, 1185), (1586, 1185), (1586, 1229), (1525, 1229)], None, None, False), ('people', [(1472, 1187), (1523, 1187), (1523, 1249), (1472, 1249)], None, None, False), ('people', [(1416, 1179), (1469, 1179), (1469, 1268), (1416, 1268)], None, None, False), ('people', [(1241, 1168), (1319, 1168), (1319, 1323), (1241, 1323)], None, None, False), ('people', [(3, 1179), (112, 1179), (112, 1444), (3, 1444)], None, None, False), ('people', [(275, 1210), (319, 1210), (319, 1523), (275, 1523)], None, None, False), ('people', [(915, 1213), (981, 1213), (981, 1421), (915, 1421)], None, None, False), ('people', [(3316, 1146), (3365, 1146), (3365, 1186), (3316, 1186)], None, None, False)]
[('car', [(3758, 1193), (3840, 1193), (3840, 1472), (3758, 1472)], None, None, False), ('people', [(3645, 1135), (3763, 1135), (3763, 1462), (3645, 1462)], None, None, False), ('people', [(3398, 1148), (3518, 1148), (3518, 1426), (3398, 1426)], None, None, False), ('people', [(3237, 1151), (3349, 1151), (3349, 1403), (3237, 1403)], None, None, False), ('people', [(2782, 1174), (2869, 1174), (2869, 1266), (2782, 1266)], None, None, False), ('people', [(2770, 1131), (2861, 1131), (2861, 1192), (2770, 1192)], None, None, False), ('people', [(2739, 1126), (2796, 1126), (2796, 1194), (2739, 1194)], None, None, False), ('people', [(2698, 1144), (2764, 1144), (2764, 1316), (2698, 1316)], None, None, False), ('people', [(2639, 1158), (2704, 1158), (2704, 1302), (2639, 1302)], None, None, False), ('people', [(2584, 1166), (2639, 1166), (2639, 1255), (2584, 1255)], None, None, False), ('people', [(2603, 1128), (2645, 1128), (2645, 1169), (2603, 1169)], None, None, False), ('people', [(2541, 1152), (2583, 1152), (2583, 1240), (2541, 1240)], None, None, False), ('people', [(2508, 1174), (2567, 1174), (2567, 1235), (2508, 1235)], None, None, False), ('car', [(1933, 1223), (3079, 1223), (3079, 1564), (1933, 1564)], None, None, False), ('people', [(1952, 1171), (2010, 1171), (2010, 1317), (1952, 1317)], None, None, False), ('people', [(1529, 1182), (1602, 1182), (1602, 1405), (1529, 1405)], None, None, False), ('people', [(1483, 1194), (1548, 1194), (1548, 1416), (1483, 1416)], None, None, False), ('people', [(1414, 1188), (1488, 1188), (1488, 1414), (1414, 1414)], None, None, False), ('people', [(1365, 1181), (1418, 1181), (1418, 1363), (1365, 1363)], None, None, False), ('people', [(1316, 1181), (1374, 1181), (1374, 1368), (1316, 1368)], None, None, False), ('people', [(1243, 1166), (1307, 1166), (1307, 1369), (1243, 1369)], None, None, False), ('car', [(1, 1087), (316, 1087), (316, 1600), (1, 1600)], None, None, False), ('people', [(935, 1191), (988, 1191), (988, 1310), (935, 1310)], None, None, False)]
[('people', [(3644, 1135), (3764, 1135), (3764, 1372), (3644, 1372)], None, None, False), ('people', [(3374, 1152), (3451, 1152), (3451, 1328), (3374, 1328)], None, None, False), ('people', [(3190, 1143), (3272, 1143), (3272, 1254), (3190, 1254)], None, None, False), ('people', [(2799, 1167), (2854, 1167), (2854, 1233), (2799, 1233)], None, None, False), ('people', [(2768, 1123), (2863, 1123), (2863, 1236), (2768, 1236)], None, None, False), ('people', [(2665, 1154), (2742, 1154), (2742, 1273), (2665, 1273)], None, None, False), ('people', [(2642, 1136), (2736, 1136), (2736, 1210), (2642, 1210)], None, None, False), ('people', [(2597, 1131), (2660, 1131), (2660, 1191), (2597, 1191)], None, None, False), ('people', [(2581, 1163), (2640, 1163), (2640, 1306), (2581, 1306)], None, None, False), ('people', [(2538, 1154), (2575, 1154), (2575, 1198), (2538, 1198)], None, None, False), ('people', [(2506, 1175), (2573, 1175), (2573, 1400), (2506, 1400)], None, None, False), ('people', [(2356, 1320), (2475, 1320), (2475, 1444), (2356, 1444)], None, None, False), ('people', [(2227, 1176), (2296, 1176), (2296, 1433), (2227, 1433)], None, None, False), ('people', [(1955, 1178), (2008, 1178), (2008, 1348), (1955, 1348)], None, None, False), ('people', [(1528, 1181), (1593, 1181), (1593, 1407), (1528, 1407)], None, None, False), ('people', [(1471, 1193), (1538, 1193), (1538, 1412), (1471, 1412)], None, None, False), ('people', [(1415, 1182), (1486, 1182), (1486, 1412), (1415, 1412)], None, None, False), ('people', [(1332, 1184), (1386, 1184), (1386, 1354), (1332, 1354)], None, None, False), ('people', [(1286, 1183), (1335, 1183), (1335, 1363), (1286, 1363)], None, None, False), ('people', [(1285, 1165), (1313, 1165), (1313, 1279), (1285, 1279)], None, None, False), ('car', [(1, 1014), (1204, 1014), (1204, 1598), (1, 1598)], None, None, False)]
[('people', [(3671, 1133), (3766, 1133), (3766, 1222), (3671, 1222)], None, None, False), ('people', [(3300, 1154), (3357, 1154), (3357, 1238), (3300, 1238)], None, None, False), ('people', [(3114, 1143), (3244, 1143), (3244, 1301), (3114, 1301)], None, None, False), ('car', [(3051, 1201), (3840, 1201), (3840, 1563), (3051, 1563)], None, None, False), ('people', [(2779, 1167), (2873, 1167), (2873, 1398), (2779, 1398)], None, None, False), ('people', [(2770, 1125), (2859, 1125), (2859, 1201), (2770, 1201)], None, None, False), ('people', [(2652, 1151), (2726, 1151), (2726, 1414), (2652, 1414)], None, None, False), ('people', [(2615, 1127), (2663, 1127), (2663, 1192), (2615, 1192)], None, None, False), ('people', [(2575, 1149), (2650, 1149), (2650, 1413), (2575, 1413)], None, None, False), ('people', [(2537, 1147), (2577, 1147), (2577, 1196), (2537, 1196)], None, None, False), ('people', [(2507, 1174), (2578, 1174), (2578, 1407), (2507, 1407)], None, None, False), ('people', [(2353, 1318), (2480, 1318), (2480, 1439), (2353, 1439)], None, None, False), ('people', [(2231, 1167), (2295, 1167), (2295, 1435), (2231, 1435)], None, None, False), ('people', [(1956, 1176), (2009, 1176), (2009, 1349), (1956, 1349)], None, None, False), ('car', [(654, 994), (1955, 994), (1955, 1582), (654, 1582)], None, None, False), ('people', [(30, 1166), (141, 1166), (141, 1486), (30, 1486)], None, None, False)]
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False), ('people', [(1330, 1183), (1366, 1183), (1366, 1343), (1330, 1343)], None, None, False), ('people', [(1596, 1172), (1649, 1172), (1649, 1218), (1596, 1218)], None, None, False), ('people', [(285, 1207), (322, 1207), (322, 1522), (285, 1522)], None, None, False), ('people', [(206, 1183), (294, 1183), (294, 1379), (206, 1379)], None, None, False), ('people', [(1, 1161), (139, 1161), (139, 1464), (1, 1464)], None, None, False)]
[('people', [(1291, 1072), (2029, 1072), (2029, 2160), (1291, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False), ('people', [(1330, 1183), (1366, 1183), (1366, 1343), (1330, 1343)], None, None, False), ('people', [(1596, 1172), (1649, 1172), (1649, 1218), (1596, 1218)], None, None, False), ('people', [(285, 1207), (322, 1207), (322, 1522), (285, 1522)], None, None, False), ('people', [(206, 1183), (294, 1183), (294, 1379), (206, 1379)], None, None, False), ('people', [(1, 1161), (139, 1161), (139, 1464), (1, 1464)], None, None, False)]
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3671, 1133), (3766, 1133), (3766, 1222), (3671, 1222)], None, None, False), ('people', [(3300, 1154), (3357, 1154), (3357, 1238), (3300, 1238)], None, None, False), ('people', [(3114, 1143), (3244, 1143), (3244, 1301), (3114, 1301)], None, None, False), ('car', [(3051, 1201), (3840, 1201), (3840, 1563), (3051, 1563)], None, None, False), ('people', [(2779, 1167), (2873, 1167), (2873, 1398), (2779, 1398)], None, None, False), ('people', [(2770, 1125), (2859, 1125), (2859, 1201), (2770, 1201)], None, None, False), ('people', [(2652, 1151), (2726, 1151), (2726, 1414), (2652, 1414)], None, None, False), ('people', [(2615, 1127), (2663, 1127), (2663, 1192), (2615, 1192)], None, None, False), ('people', [(2575, 1149), (2650, 1149), (2650, 1413), (2575, 1413)], None, None, False), ('people', [(2537, 1147), (2577, 1147), (2577, 1196), (2537, 1196)], None, None, False), ('people', [(2507, 1174), (2578, 1174), (2578, 1407), (2507, 1407)], None, None, False), ('people', [(2353, 1318), (2480, 1318), (2480, 1439), (2353, 1439)], None, None, False), ('people', [(2231, 1167), (2295, 1167), (2295, 1435), (2231, 1435)], None, None, False), ('people', [(1956, 1176), (2009, 1176), (2009, 1349), (1956, 1349)], None, None, False), ('car', [(654, 994), (1955, 994), (1955, 1582), (654, 1582)], None, None, False), ('people', [(30, 1166), (141, 1166), (141, 1486), (30, 1486)], None, None, False)]
[('people', [(3644, 1135), (3764, 1135), (3764, 1372), (3644, 1372)], None, None, False), ('people', [(3374, 1152), (3451, 1152), (3451, 1328), (3374, 1328)], None, None, False), ('people', [(3190, 1143), (3272, 1143), (3272, 1254), (3190, 1254)], None, None, False), ('people', [(2799, 1167), (2854, 1167), (2854, 1233), (2799, 1233)], None, None, False), ('people', [(2768, 1123), (2863, 1123), (2863, 1236), (2768, 1236)], None, None, False), ('people', [(2665, 1154), (2742, 1154), (2742, 1273), (2665, 1273)], None, None, False), ('people', [(2642, 1136), (2736, 1136), (2736, 1210), (2642, 1210)], None, None, False), ('people', [(2597, 1131), (2660, 1131), (2660, 1191), (2597, 1191)], None, None, False), ('people', [(2581, 1163), (2640, 1163), (2640, 1306), (2581, 1306)], None, None, False), ('people', [(2538, 1154), (2575, 1154), (2575, 1198), (2538, 1198)], None, None, False), ('people', [(2506, 1175), (2573, 1175), (2573, 1400), (2506, 1400)], None, None, False), ('people', [(2356, 1320), (2475, 1320), (2475, 1444), (2356, 1444)], None, None, False), ('people', [(2227, 1176), (2296, 1176), (2296, 1433), (2227, 1433)], None, None, False), ('people', [(1955, 1178), (2008, 1178), (2008, 1348), (1955, 1348)], None, None, False), ('people', [(1528, 1181), (1593, 1181), (1593, 1407), (1528, 1407)], None, None, False), ('people', [(1471, 1193), (1538, 1193), (1538, 1412), (1471, 1412)], None, None, False), ('people', [(1415, 1182), (1486, 1182), (1486, 1412), (1415, 1412)], None, None, False), ('people', [(1332, 1184), (1386, 1184), (1386, 1354), (1332, 1354)], None, None, False), ('people', [(1286, 1183), (1335, 1183), (1335, 1363), (1286, 1363)], None, None, False), ('people', [(1285, 1165), (1313, 1165), (1313, 1279), (1285, 1279)], None, None, False), ('car', [(1, 1014), (1204, 1014), (1204, 1598), (1, 1598)], None, None, False)]
[('people', [(3671, 1133), (3766, 1133), (3766, 1222), (3671, 1222)], None, None, False), ('people', [(3300, 1154), (3357, 1154), (3357, 1238), (3300, 1238)], None, None, False), ('people', [(3114, 1143), (3244, 1143), (3244, 1301), (3114, 1301)], None, None, False), ('car', [(3051, 1201), (3840, 1201), (3840, 1563), (3051, 1563)], None, None, False), ('people', [(2779, 1167), (2873, 1167), (2873, 1398), (2779, 1398)], None, None, False), ('people', [(2770, 1125), (2859, 1125), (2859, 1201), (2770, 1201)], None, None, False), ('people', [(2652, 1151), (2726, 1151), (2726, 1414), (2652, 1414)], None, None, False), ('people', [(2615, 1127), (2663, 1127), (2663, 1192), (2615, 1192)], None, None, False), ('people', [(2575, 1149), (2650, 1149), (2650, 1413), (2575, 1413)], None, None, False), ('people', [(2537, 1147), (2577, 1147), (2577, 1196), (2537, 1196)], None, None, False), ('people', [(2507, 1174), (2578, 1174), (2578, 1407), (2507, 1407)], None, None, False), ('people', [(2353, 1318), (2480, 1318), (2480, 1439), (2353, 1439)], None, None, False), ('people', [(2231, 1167), (2295, 1167), (2295, 1435), (2231, 1435)], None, None, False), ('people', [(1956, 1176), (2009, 1176), (2009, 1349), (1956, 1349)], None, None, False), ('car', [(654, 994), (1955, 994), (1955, 1582), (654, 1582)], None, None, False), ('people', [(30, 1166), (141, 1166), (141, 1486), (30, 1486)], None, None, False)]
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False), ('people', [(1330, 1183), (1366, 1183), (1366, 1343), (1330, 1343)], None, None, False), ('people', [(1596, 1172), (1649, 1172), (1649, 1218), (1596, 1218)], None, None, False), ('people', [(285, 1207), (322, 1207), (322, 1522), (285, 1522)], None, None, False), ('people', [(206, 1183), (294, 1183), (294, 1379), (206, 1379)], None, None, False), ('people', [(1, 1161), (139, 1161), (139, 1464), (1, 1464)], None, None, False)]
[('people', [(1291, 1072), (2029, 1072), (2029, 2160), (1291, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00016.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00016.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00017.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00017.txt
Cancel creation.
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00018.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00018.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00019.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00019.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00020.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00020.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00021.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00021.txt
[('car', [(3102, 1128), (3840, 1128), (3840, 1541), (3102, 1541)], None, None, False), ('people', [(884, 949), (1242, 949), (1242, 2160), (884, 2160)], None, None, False), ('people', [(1, 1162), (45, 1162), (45, 1459), (1, 1459)], None, None, False), ('people', [(1238, 1160), (1279, 1160), (1279, 1243), (1238, 1243)], None, None, False), ('people', [(1271, 1161), (1398, 1161), (1398, 1432), (1271, 1432)], None, None, False), ('people', [(1413, 1176), (1472, 1176), (1472, 1405), (1413, 1405)], None, None, False), ('people', [(1471, 1196), (1533, 1196), (1533, 1406), (1471, 1406)], None, None, False), ('people', [(1520, 1174), (1589, 1174), (1589, 1420), (1520, 1420)], None, None, False), ('people', [(1622, 1158), (1669, 1158), (1669, 1215), (1622, 1215)], None, None, False), ('people', [(1991, 1177), (2024, 1177), (2024, 1358), (1991, 1358)], None, None, False), ('people', [(2194, 1173), (2279, 1173), (2279, 1423), (2194, 1423)], None, None, False), ('people', [(2279, 1172), (2321, 1172), (2321, 1422), (2279, 1422)], None, None, False), ('people', [(2319, 1152), (2366, 1152), (2366, 1412), (2319, 1412)], None, None, False), ('people', [(2359, 1172), (2411, 1172), (2411, 1381), (2359, 1381)], None, None, False), ('people', [(2428, 1176), (2497, 1176), (2497, 1342), (2428, 1342)], None, None, False), ('people', [(2534, 1138), (2606, 1138), (2606, 1421), (2534, 1421)], None, None, False), ('people', [(2598, 1156), (2650, 1156), (2650, 1403), (2598, 1403)], None, None, False), ('people', [(2621, 1158), (2709, 1158), (2709, 1414), (2621, 1414)], None, None, False), ('people', [(2678, 1143), (2747, 1143), (2747, 1409), (2678, 1409)], None, None, False), ('people', [(2602, 1131), (2637, 1131), (2637, 1158), (2602, 1158)], None, None, False), ('people', [(2739, 1157), (2786, 1157), (2786, 1395), (2739, 1395)], None, None, False), ('people', [(2779, 1125), (2838, 1125), (2838, 1167), (2779, 1167)], None, None, False), ('people', [(2791, 1163), (2875, 1163), (2875, 1417), (2791, 1417)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00020.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00020.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00021.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00021.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00022.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/label/frame_00022.txt
[('people', [(3670, 1140), (3782, 1140), (3782, 1447), (3670, 1447)], None, None, False), ('people', [(3256, 1154), (3337, 1154), (3337, 1453), (3256, 1453)], None, None, False), ('people', [(3145, 1143), (3247, 1143), (3247, 1452), (3145, 1452)], None, None, False), ('people', [(3012, 1154), (3096, 1154), (3096, 1447), (3012, 1447)], None, None, False), ('people', [(122, 1171), (211, 1171), (211, 1519), (122, 1519)], None, None, False), ('people', [(5, 1205), (106, 1205), (106, 1482), (5, 1482)], None, None, False), ('car', [(470, 561), (3051, 561), (3051, 1556), (470, 1556)], None, None, False)]
[('car', [(3102, 1128), (3840, 1128), (3840, 1541), (3102, 1541)], None, None, False), ('people', [(884, 949), (1242, 949), (1242, 2160), (884, 2160)], None, None, False), ('people', [(1, 1162), (45, 1162), (45, 1459), (1, 1459)], None, None, False), ('people', [(1238, 1160), (1279, 1160), (1279, 1243), (1238, 1243)], None, None, False), ('people', [(1271, 1161), (1398, 1161), (1398, 1432), (1271, 1432)], None, None, False), ('people', [(1413, 1176), (1472, 1176), (1472, 1405), (1413, 1405)], None, None, False), ('people', [(1471, 1196), (1533, 1196), (1533, 1406), (1471, 1406)], None, None, False), ('people', [(1520, 1174), (1589, 1174), (1589, 1420), (1520, 1420)], None, None, False), ('people', [(1622, 1158), (1669, 1158), (1669, 1215), (1622, 1215)], None, None, False), ('people', [(1991, 1177), (2024, 1177), (2024, 1358), (1991, 1358)], None, None, False), ('people', [(2194, 1173), (2279, 1173), (2279, 1423), (2194, 1423)], None, None, False), ('people', [(2279, 1172), (2321, 1172), (2321, 1422), (2279, 1422)], None, None, False), ('people', [(2319, 1152), (2366, 1152), (2366, 1412), (2319, 1412)], None, None, False), ('people', [(2359, 1172), (2411, 1172), (2411, 1381), (2359, 1381)], None, None, False), ('people', [(2428, 1176), (2497, 1176), (2497, 1342), (2428, 1342)], None, None, False), ('people', [(2534, 1138), (2606, 1138), (2606, 1421), (2534, 1421)], None, None, False), ('people', [(2598, 1156), (2650, 1156), (2650, 1403), (2598, 1403)], None, None, False), ('people', [(2621, 1158), (2709, 1158), (2709, 1414), (2621, 1414)], None, None, False), ('people', [(2678, 1143), (2747, 1143), (2747, 1409), (2678, 1409)], None, None, False), ('people', [(2602, 1131), (2637, 1131), (2637, 1158), (2602, 1158)], None, None, False), ('people', [(2739, 1157), (2786, 1157), (2786, 1395), (2739, 1395)], None, None, False), ('people', [(2779, 1125), (2838, 1125), (2838, 1167), (2779, 1167)], None, None, False), ('people', [(2791, 1163), (2875, 1163), (2875, 1417), (2791, 1417)], None, None, False)]
Cancel creation.
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False), ('people', [(2454, 1120), (2827, 1120), (2827, 1863), (2454, 1863)], None, None, False), ('people', [(2802, 1169), (2875, 1169), (2875, 1383), (2802, 1383)], None, None, False), ('people', [(2780, 1114), (2864, 1114), (2864, 1210), (2780, 1210)], None, None, False), ('people', [(2681, 1150), (2714, 1150), (2714, 1187), (2681, 1187)], None, None, False), ('people', [(2678, 1154), (2760, 1154), (2760, 1216), (2678, 1216)], None, None, False), ('people', [(2628, 1152), (2665, 1152), (2665, 1201), (2628, 1201)], None, None, False), ('people', [(2598, 1122), (2643, 1122), (2643, 1191), (2598, 1191)], None, None, False), ('people', [(2357, 1309), (2459, 1309), (2459, 1446), (2357, 1446)], None, None, False), ('people', [(2257, 1156), (2326, 1156), (2326, 1386), (2257, 1386)], None, None, False), ('people', [(2224, 1173), (2294, 1173), (2294, 1429), (2224, 1429)], None, None, False), ('people', [(1964, 1179), (2016, 1179), (2016, 1350), (1964, 1350)], None, None, False), ('people', [(1936, 1170), (1963, 1170), (1963, 1307), (1936, 1307)], None, None, False), ('people', [(1527, 1192), (1590, 1192), (1590, 1409), (1527, 1409)], None, None, False), ('people', [(1475, 1189), (1544, 1189), (1544, 1414), (1475, 1414)], None, None, False), ('people', [(1421, 1176), (1485, 1176), (1485, 1414), (1421, 1414)], None, None, False), ('people', [(1352, 1169), (1382, 1169), (1382, 1264), (1352, 1264)], None, None, False), ('people', [(917, 1188), (986, 1188), (986, 1443), (917, 1443)], None, None, False)]
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
[('people', [(1291, 1072), (2029, 1072), (2029, 2160), (1291, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False), ('people', [(1330, 1183), (1366, 1183), (1366, 1343), (1330, 1343)], None, None, False), ('people', [(1596, 1172), (1649, 1172), (1649, 1218), (1596, 1218)], None, None, False), ('people', [(285, 1207), (322, 1207), (322, 1522), (285, 1522)], None, None, False), ('people', [(206, 1183), (294, 1183), (294, 1379), (206, 1379)], None, None, False), ('people', [(1, 1161), (139, 1161), (139, 1464), (1, 1464)], None, None, False)]
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3671, 1133), (3766, 1133), (3766, 1222), (3671, 1222)], None, None, False), ('people', [(3300, 1154), (3357, 1154), (3357, 1238), (3300, 1238)], None, None, False), ('people', [(3114, 1143), (3244, 1143), (3244, 1301), (3114, 1301)], None, None, False), ('car', [(3051, 1201), (3840, 1201), (3840, 1563), (3051, 1563)], None, None, False), ('people', [(2779, 1167), (2873, 1167), (2873, 1398), (2779, 1398)], None, None, False), ('people', [(2770, 1125), (2859, 1125), (2859, 1201), (2770, 1201)], None, None, False), ('people', [(2652, 1151), (2726, 1151), (2726, 1414), (2652, 1414)], None, None, False), ('people', [(2615, 1127), (2663, 1127), (2663, 1192), (2615, 1192)], None, None, False), ('people', [(2575, 1149), (2650, 1149), (2650, 1413), (2575, 1413)], None, None, False), ('people', [(2537, 1147), (2577, 1147), (2577, 1196), (2537, 1196)], None, None, False), ('people', [(2507, 1174), (2578, 1174), (2578, 1407), (2507, 1407)], None, None, False), ('people', [(2353, 1318), (2480, 1318), (2480, 1439), (2353, 1439)], None, None, False), ('people', [(2231, 1167), (2295, 1167), (2295, 1435), (2231, 1435)], None, None, False), ('people', [(1956, 1176), (2009, 1176), (2009, 1349), (1956, 1349)], None, None, False), ('car', [(654, 994), (1955, 994), (1955, 1582), (654, 1582)], None, None, False), ('people', [(30, 1166), (141, 1166), (141, 1486), (30, 1486)], None, None, False)]
[('people', [(3644, 1135), (3764, 1135), (3764, 1372), (3644, 1372)], None, None, False), ('people', [(3374, 1152), (3451, 1152), (3451, 1328), (3374, 1328)], None, None, False), ('people', [(3190, 1143), (3272, 1143), (3272, 1254), (3190, 1254)], None, None, False), ('people', [(2799, 1167), (2854, 1167), (2854, 1233), (2799, 1233)], None, None, False), ('people', [(2768, 1123), (2863, 1123), (2863, 1236), (2768, 1236)], None, None, False), ('people', [(2665, 1154), (2742, 1154), (2742, 1273), (2665, 1273)], None, None, False), ('people', [(2642, 1136), (2736, 1136), (2736, 1210), (2642, 1210)], None, None, False), ('people', [(2597, 1131), (2660, 1131), (2660, 1191), (2597, 1191)], None, None, False), ('people', [(2581, 1163), (2640, 1163), (2640, 1306), (2581, 1306)], None, None, False), ('people', [(2538, 1154), (2575, 1154), (2575, 1198), (2538, 1198)], None, None, False), ('people', [(2506, 1175), (2573, 1175), (2573, 1400), (2506, 1400)], None, None, False), ('people', [(2356, 1320), (2475, 1320), (2475, 1444), (2356, 1444)], None, None, False), ('people', [(2227, 1176), (2296, 1176), (2296, 1433), (2227, 1433)], None, None, False), ('people', [(1955, 1178), (2008, 1178), (2008, 1348), (1955, 1348)], None, None, False), ('people', [(1528, 1181), (1593, 1181), (1593, 1407), (1528, 1407)], None, None, False), ('people', [(1471, 1193), (1538, 1193), (1538, 1412), (1471, 1412)], None, None, False), ('people', [(1415, 1182), (1486, 1182), (1486, 1412), (1415, 1412)], None, None, False), ('people', [(1332, 1184), (1386, 1184), (1386, 1354), (1332, 1354)], None, None, False), ('people', [(1286, 1183), (1335, 1183), (1335, 1363), (1286, 1363)], None, None, False), ('people', [(1285, 1165), (1313, 1165), (1313, 1279), (1285, 1279)], None, None, False), ('car', [(1, 1014), (1204, 1014), (1204, 1598), (1, 1598)], None, None, False)]
[('car', [(3758, 1193), (3840, 1193), (3840, 1472), (3758, 1472)], None, None, False), ('people', [(3645, 1135), (3763, 1135), (3763, 1462), (3645, 1462)], None, None, False), ('people', [(3398, 1148), (3518, 1148), (3518, 1426), (3398, 1426)], None, None, False), ('people', [(3237, 1151), (3349, 1151), (3349, 1403), (3237, 1403)], None, None, False), ('people', [(2782, 1174), (2869, 1174), (2869, 1266), (2782, 1266)], None, None, False), ('people', [(2770, 1131), (2861, 1131), (2861, 1192), (2770, 1192)], None, None, False), ('people', [(2739, 1126), (2796, 1126), (2796, 1194), (2739, 1194)], None, None, False), ('people', [(2698, 1144), (2764, 1144), (2764, 1316), (2698, 1316)], None, None, False), ('people', [(2639, 1158), (2704, 1158), (2704, 1302), (2639, 1302)], None, None, False), ('people', [(2584, 1166), (2639, 1166), (2639, 1255), (2584, 1255)], None, None, False), ('people', [(2603, 1128), (2645, 1128), (2645, 1169), (2603, 1169)], None, None, False), ('people', [(2541, 1152), (2583, 1152), (2583, 1240), (2541, 1240)], None, None, False), ('people', [(2508, 1174), (2567, 1174), (2567, 1235), (2508, 1235)], None, None, False), ('car', [(1933, 1223), (3079, 1223), (3079, 1564), (1933, 1564)], None, None, False), ('people', [(1952, 1171), (2010, 1171), (2010, 1317), (1952, 1317)], None, None, False), ('people', [(1529, 1182), (1602, 1182), (1602, 1405), (1529, 1405)], None, None, False), ('people', [(1483, 1194), (1548, 1194), (1548, 1416), (1483, 1416)], None, None, False), ('people', [(1414, 1188), (1488, 1188), (1488, 1414), (1414, 1414)], None, None, False), ('people', [(1365, 1181), (1418, 1181), (1418, 1363), (1365, 1363)], None, None, False), ('people', [(1316, 1181), (1374, 1181), (1374, 1368), (1316, 1368)], None, None, False), ('people', [(1243, 1166), (1307, 1166), (1307, 1369), (1243, 1369)], None, None, False), ('car', [(1, 1087), (316, 1087), (316, 1600), (1, 1600)], None, None, False), ('people', [(935, 1191), (988, 1191), (988, 1310), (935, 1310)], None, None, False)]
[('car', [(3114, 1171), (3840, 1171), (3840, 1541), (3114, 1541)], None, None, False), ('people', [(3687, 1143), (3737, 1143), (3737, 1189), (3687, 1189)], None, None, False), ('people', [(2848, 1130), (2901, 1130), (2901, 1262), (2848, 1262)], None, None, False), ('people', [(2777, 1127), (2853, 1127), (2853, 1195), (2777, 1195)], None, None, False), ('people', [(2776, 1171), (2864, 1171), (2864, 1402), (2776, 1402)], None, None, False), ('people', [(2721, 1151), (2789, 1151), (2789, 1393), (2721, 1393)], None, None, False), ('people', [(2663, 1159), (2726, 1159), (2726, 1401), (2663, 1401)], None, None, False), ('people', [(2598, 1128), (2665, 1128), (2665, 1198), (2598, 1198)], None, None, False), ('people', [(2583, 1162), (2655, 1162), (2655, 1411), (2583, 1411)], None, None, False), ('people', [(2539, 1154), (2587, 1154), (2587, 1361), (2539, 1361)], None, None, False), ('people', [(2504, 1175), (2566, 1175), (2566, 1401), (2504, 1401)], None, None, False), ('people', [(2353, 1317), (2478, 1317), (2478, 1440), (2353, 1440)], None, None, False), ('people', [(2235, 1174), (2316, 1174), (2316, 1365), (2235, 1365)], None, None, False), ('people', [(1228, 1223), (2343, 1223), (2343, 1568), (1228, 1568)], None, None, False), ('people', [(1362, 1186), (1408, 1186), (1408, 1285), (1362, 1285)], None, None, False), ('people', [(1525, 1185), (1586, 1185), (1586, 1229), (1525, 1229)], None, None, False), ('people', [(1472, 1187), (1523, 1187), (1523, 1249), (1472, 1249)], None, None, False), ('people', [(1416, 1179), (1469, 1179), (1469, 1268), (1416, 1268)], None, None, False), ('people', [(1241, 1168), (1319, 1168), (1319, 1323), (1241, 1323)], None, None, False), ('people', [(3, 1179), (112, 1179), (112, 1444), (3, 1444)], None, None, False), ('people', [(275, 1210), (319, 1210), (319, 1523), (275, 1523)], None, None, False), ('people', [(915, 1213), (981, 1213), (981, 1421), (915, 1421)], None, None, False), ('people', [(3316, 1146), (3365, 1146), (3365, 1186), (3316, 1186)], None, None, False)]
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3654, 1146), (3774, 1146), (3774, 1471), (3654, 1471)], None, None, False), ('people', [(3311, 1163), (3417, 1163), (3417, 1463), (3311, 1463)], None, None, False), ('people', [(3214, 1149), (3316, 1149), (3316, 1439), (3214, 1439)], None, None, False), ('people', [(2773, 1167), (2847, 1167), (2847, 1400), (2773, 1400)], None, None, False), ('people', [(2755, 1125), (2853, 1125), (2853, 1373), (2755, 1373)], None, None, False), ('people', [(2708, 1133), (2763, 1133), (2763, 1364), (2708, 1364)], None, None, False), ('people', [(2676, 1163), (2749, 1163), (2749, 1397), (2676, 1397)], None, None, False), ('people', [(2607, 1174), (2690, 1174), (2690, 1402), (2607, 1402)], None, None, False), ('people', [(2648, 1160), (2690, 1160), (2690, 1371), (2648, 1371)], None, None, False), ('people', [(2644, 1122), (2692, 1122), (2692, 1173), (2644, 1173)], None, None, False), ('people', [(2579, 1127), (2654, 1127), (2654, 1370), (2579, 1370)], None, None, False), ('people', [(2500, 1151), (2582, 1151), (2582, 1395), (2500, 1395)], None, None, False), ('people', [(2312, 1161), (2382, 1161), (2382, 1413), (2312, 1413)], None, None, False), ('people', [(2253, 1172), (2327, 1172), (2327, 1437), (2253, 1437)], None, None, False), ('people', [(1942, 1175), (2018, 1175), (2018, 1359), (1942, 1359)], None, None, False), ('car', [(878, 1196), (1877, 1196), (1877, 1555), (878, 1555)], None, None, False), ('people', [(1656, 1177), (1711, 1177), (1711, 1316), (1656, 1316)], None, None, False), ('people', [(1636, 1175), (1662, 1175), (1662, 1212), (1636, 1212)], None, None, False), ('people', [(1589, 1163), (1646, 1163), (1646, 1300), (1589, 1300)], None, None, False), ('people', [(1527, 1186), (1594, 1186), (1594, 1262), (1527, 1262)], None, None, False), ('people', [(1487, 1164), (1537, 1164), (1537, 1239), (1487, 1239)], None, None, False), ('people', [(1425, 1177), (1477, 1177), (1477, 1219), (1425, 1219)], None, None, False), ('people', [(244, 1189), (322, 1189), (322, 1522), (244, 1522)], None, None, False), ('people', [(1, 1185), (62, 1185), (62, 1448), (1, 1448)], None, None, False)]
[('people', [(3650, 1141), (3769, 1141), (3769, 1453), (3650, 1453)], None, None, False), ('people', [(3281, 1168), (3374, 1168), (3374, 1467), (3281, 1467)], None, None, False), ('people', [(3167, 1145), (3290, 1145), (3290, 1440), (3167, 1440)], None, None, False), ('people', [(2774, 1164), (2843, 1164), (2843, 1403), (2774, 1403)], None, None, False), ('people', [(2756, 1127), (2853, 1127), (2853, 1374), (2756, 1374)], None, None, False), ('people', [(2687, 1133), (2751, 1133), (2751, 1351), (2687, 1351)], None, None, False), ('people', [(2674, 1168), (2731, 1168), (2731, 1393), (2674, 1393)], None, None, False), ('people', [(2626, 1173), (2699, 1173), (2699, 1402), (2626, 1402)], None, None, False), ('people', [(2584, 1132), (2645, 1132), (2645, 1364), (2584, 1364)], None, None, False), ('people', [(2627, 1129), (2677, 1129), (2677, 1175), (2627, 1175)], None, None, False), ('people', [(2500, 1155), (2584, 1155), (2584, 1389), (2500, 1389)], None, None, False), ('people', [(2299, 1173), (2373, 1173), (2373, 1435), (2299, 1435)], None, None, False), ('people', [(2235, 1157), (2292, 1157), (2292, 1416), (2235, 1416)], None, None, False), ('people', [(1987, 1179), (2025, 1179), (2025, 1357), (1987, 1357)], None, None, False), ('people', [(1937, 1173), (2000, 1173), (2000, 1364), (1937, 1364)], None, None, False), ('people', [(1681, 1166), (1741, 1166), (1741, 1391), (1681, 1391)], None, None, False), ('people', [(1583, 1163), (1675, 1163), (1675, 1393), (1583, 1393)], None, None, False), ('people', [(1592, 1173), (1620, 1173), (1620, 1217), (1592, 1217)], None, None, False), ('people', [(1534, 1182), (1587, 1182), (1587, 1406), (1534, 1406)], None, None, False), ('people', [(1491, 1197), (1552, 1197), (1552, 1410), (1491, 1410)], None, None, False), ('people', [(1411, 1180), (1491, 1180), (1491, 1414), (1411, 1414)], None, None, False), ('people', [(937, 1204), (980, 1204), (980, 1317), (937, 1317)], None, None, False), ('people', [(929, 1186), (988, 1186), (988, 1293), (929, 1293)], None, None, False), ('car', [(1, 1217), (1052, 1217), (1052, 1581), (1, 1581)], None, None, False)]
[('car', [(3143, 468), (3840, 468), (3840, 1526), (3143, 1526)], None, None, False), ('people', [(2788, 1170), (2841, 1170), (2841, 1407), (2788, 1407)], None, None, False), ('people', [(2774, 1124), (2840, 1124), (2840, 1372), (2774, 1372)], None, None, False), ('people', [(2724, 1141), (2771, 1141), (2771, 1351), (2724, 1351)], None, None, False), ('people', [(2700, 1178), (2765, 1178), (2765, 1405), (2700, 1405)], None, None, False), ('people', [(2643, 1124), (2685, 1124), (2685, 1278), (2643, 1278)], None, None, False), ('people', [(2656, 1166), (2709, 1166), (2709, 1396), (2656, 1396)], None, None, False), ('people', [(2584, 1130), (2653, 1130), (2653, 1371), (2584, 1371)], None, None, False), ('people', [(2503, 1158), (2573, 1158), (2573, 1391), (2503, 1391)], None, None, False), ('people', [(2329, 1171), (2411, 1171), (2411, 1421), (2329, 1421)], None, None, False), ('people', [(1935, 1168), (2007, 1168), (2007, 1385), (1935, 1385)], None, None, False), ('people', [(1533, 1180), (1591, 1180), (1591, 1408), (1533, 1408)], None, None, False), ('people', [(1481, 1186), (1533, 1186), (1533, 1416), (1481, 1416)], None, None, False), ('people', [(1415, 1182), (1487, 1182), (1487, 1410), (1415, 1410)], None, None, False), ('people', [(258, 1170), (315, 1170), (315, 1523), (258, 1523)], None, None, False), ('car', [(2, 1340), (156, 1340), (156, 1569), (2, 1569)], None, None, False)]
[('car', [(2402, 488), (3840, 488), (3840, 1574), (2402, 1574)], None, None, False), ('people', [(2259, 1173), (2378, 1173), (2378, 1436), (2259, 1436)], None, None, False), ('people', [(1527, 1186), (1587, 1186), (1587, 1407), (1527, 1407)], None, None, False), ('people', [(1469, 1188), (1529, 1188), (1529, 1419), (1469, 1419)], None, None, False), ('people', [(1409, 1183), (1473, 1183), (1473, 1415), (1409, 1415)], None, None, False), ('people', [(231, 1172), (299, 1172), (299, 1516), (231, 1516)], None, None, False), ('people', [(1958, 1158), (2010, 1158), (2010, 1400), (1958, 1400)], None, None, False)]
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
[('car', [(2402, 488), (3840, 488), (3840, 1574), (2402, 1574)], None, None, False), ('people', [(2259, 1173), (2378, 1173), (2378, 1436), (2259, 1436)], None, None, False), ('people', [(1527, 1186), (1587, 1186), (1587, 1407), (1527, 1407)], None, None, False), ('people', [(1469, 1188), (1529, 1188), (1529, 1419), (1469, 1419)], None, None, False), ('people', [(1409, 1183), (1473, 1183), (1473, 1415), (1409, 1415)], None, None, False), ('people', [(231, 1172), (299, 1172), (299, 1516), (231, 1516)], None, None, False), ('people', [(1958, 1158), (2010, 1158), (2010, 1400), (1958, 1400)], None, None, False)]
[('car', [(3143, 468), (3840, 468), (3840, 1526), (3143, 1526)], None, None, False), ('people', [(2788, 1170), (2841, 1170), (2841, 1407), (2788, 1407)], None, None, False), ('people', [(2774, 1124), (2840, 1124), (2840, 1372), (2774, 1372)], None, None, False), ('people', [(2724, 1141), (2771, 1141), (2771, 1351), (2724, 1351)], None, None, False), ('people', [(2700, 1178), (2765, 1178), (2765, 1405), (2700, 1405)], None, None, False), ('people', [(2643, 1124), (2685, 1124), (2685, 1278), (2643, 1278)], None, None, False), ('people', [(2656, 1166), (2709, 1166), (2709, 1396), (2656, 1396)], None, None, False), ('people', [(2584, 1130), (2653, 1130), (2653, 1371), (2584, 1371)], None, None, False), ('people', [(2503, 1158), (2573, 1158), (2573, 1391), (2503, 1391)], None, None, False), ('people', [(2329, 1171), (2411, 1171), (2411, 1421), (2329, 1421)], None, None, False), ('people', [(1935, 1168), (2007, 1168), (2007, 1385), (1935, 1385)], None, None, False), ('people', [(1533, 1180), (1591, 1180), (1591, 1408), (1533, 1408)], None, None, False), ('people', [(1481, 1186), (1533, 1186), (1533, 1416), (1481, 1416)], None, None, False), ('people', [(1415, 1182), (1487, 1182), (1487, 1410), (1415, 1410)], None, None, False), ('people', [(258, 1170), (315, 1170), (315, 1523), (258, 1523)], None, None, False), ('car', [(2, 1340), (156, 1340), (156, 1569), (2, 1569)], None, None, False)]
[('people', [(3650, 1141), (3769, 1141), (3769, 1453), (3650, 1453)], None, None, False), ('people', [(3281, 1168), (3374, 1168), (3374, 1467), (3281, 1467)], None, None, False), ('people', [(3167, 1145), (3290, 1145), (3290, 1440), (3167, 1440)], None, None, False), ('people', [(2774, 1164), (2843, 1164), (2843, 1403), (2774, 1403)], None, None, False), ('people', [(2756, 1127), (2853, 1127), (2853, 1374), (2756, 1374)], None, None, False), ('people', [(2687, 1133), (2751, 1133), (2751, 1351), (2687, 1351)], None, None, False), ('people', [(2674, 1168), (2731, 1168), (2731, 1393), (2674, 1393)], None, None, False), ('people', [(2626, 1173), (2699, 1173), (2699, 1402), (2626, 1402)], None, None, False), ('people', [(2584, 1132), (2645, 1132), (2645, 1364), (2584, 1364)], None, None, False), ('people', [(2627, 1129), (2677, 1129), (2677, 1175), (2627, 1175)], None, None, False), ('people', [(2500, 1155), (2584, 1155), (2584, 1389), (2500, 1389)], None, None, False), ('people', [(2299, 1173), (2373, 1173), (2373, 1435), (2299, 1435)], None, None, False), ('people', [(2235, 1157), (2292, 1157), (2292, 1416), (2235, 1416)], None, None, False), ('people', [(1987, 1179), (2025, 1179), (2025, 1357), (1987, 1357)], None, None, False), ('people', [(1937, 1173), (2000, 1173), (2000, 1364), (1937, 1364)], None, None, False), ('people', [(1681, 1166), (1741, 1166), (1741, 1391), (1681, 1391)], None, None, False), ('people', [(1583, 1163), (1675, 1163), (1675, 1393), (1583, 1393)], None, None, False), ('people', [(1592, 1173), (1620, 1173), (1620, 1217), (1592, 1217)], None, None, False), ('people', [(1534, 1182), (1587, 1182), (1587, 1406), (1534, 1406)], None, None, False), ('people', [(1491, 1197), (1552, 1197), (1552, 1410), (1491, 1410)], None, None, False), ('people', [(1411, 1180), (1491, 1180), (1491, 1414), (1411, 1414)], None, None, False), ('people', [(937, 1204), (980, 1204), (980, 1317), (937, 1317)], None, None, False), ('people', [(929, 1186), (988, 1186), (988, 1293), (929, 1293)], None, None, False), ('car', [(1, 1217), (1052, 1217), (1052, 1581), (1, 1581)], None, None, False)]
[('people', [(3654, 1146), (3774, 1146), (3774, 1471), (3654, 1471)], None, None, False), ('people', [(3311, 1163), (3417, 1163), (3417, 1463), (3311, 1463)], None, None, False), ('people', [(3214, 1149), (3316, 1149), (3316, 1439), (3214, 1439)], None, None, False), ('people', [(2773, 1167), (2847, 1167), (2847, 1400), (2773, 1400)], None, None, False), ('people', [(2755, 1125), (2853, 1125), (2853, 1373), (2755, 1373)], None, None, False), ('people', [(2708, 1133), (2763, 1133), (2763, 1364), (2708, 1364)], None, None, False), ('people', [(2676, 1163), (2749, 1163), (2749, 1397), (2676, 1397)], None, None, False), ('people', [(2607, 1174), (2690, 1174), (2690, 1402), (2607, 1402)], None, None, False), ('people', [(2648, 1160), (2690, 1160), (2690, 1371), (2648, 1371)], None, None, False), ('people', [(2644, 1122), (2692, 1122), (2692, 1173), (2644, 1173)], None, None, False), ('people', [(2579, 1127), (2654, 1127), (2654, 1370), (2579, 1370)], None, None, False), ('people', [(2500, 1151), (2582, 1151), (2582, 1395), (2500, 1395)], None, None, False), ('people', [(2312, 1161), (2382, 1161), (2382, 1413), (2312, 1413)], None, None, False), ('people', [(2253, 1172), (2327, 1172), (2327, 1437), (2253, 1437)], None, None, False), ('people', [(1942, 1175), (2018, 1175), (2018, 1359), (1942, 1359)], None, None, False), ('car', [(878, 1196), (1877, 1196), (1877, 1555), (878, 1555)], None, None, False), ('people', [(1656, 1177), (1711, 1177), (1711, 1316), (1656, 1316)], None, None, False), ('people', [(1636, 1175), (1662, 1175), (1662, 1212), (1636, 1212)], None, None, False), ('people', [(1589, 1163), (1646, 1163), (1646, 1300), (1589, 1300)], None, None, False), ('people', [(1527, 1186), (1594, 1186), (1594, 1262), (1527, 1262)], None, None, False), ('people', [(1487, 1164), (1537, 1164), (1537, 1239), (1487, 1239)], None, None, False), ('people', [(1425, 1177), (1477, 1177), (1477, 1219), (1425, 1219)], None, None, False), ('people', [(244, 1189), (322, 1189), (322, 1522), (244, 1522)], None, None, False), ('people', [(1, 1185), (62, 1185), (62, 1448), (1, 1448)], None, None, False)]
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
[('car', [(3114, 1171), (3840, 1171), (3840, 1541), (3114, 1541)], None, None, False), ('people', [(3687, 1143), (3737, 1143), (3737, 1189), (3687, 1189)], None, None, False), ('people', [(2848, 1130), (2901, 1130), (2901, 1262), (2848, 1262)], None, None, False), ('people', [(2777, 1127), (2853, 1127), (2853, 1195), (2777, 1195)], None, None, False), ('people', [(2776, 1171), (2864, 1171), (2864, 1402), (2776, 1402)], None, None, False), ('people', [(2721, 1151), (2789, 1151), (2789, 1393), (2721, 1393)], None, None, False), ('people', [(2663, 1159), (2726, 1159), (2726, 1401), (2663, 1401)], None, None, False), ('people', [(2598, 1128), (2665, 1128), (2665, 1198), (2598, 1198)], None, None, False), ('people', [(2583, 1162), (2655, 1162), (2655, 1411), (2583, 1411)], None, None, False), ('people', [(2539, 1154), (2587, 1154), (2587, 1361), (2539, 1361)], None, None, False), ('people', [(2504, 1175), (2566, 1175), (2566, 1401), (2504, 1401)], None, None, False), ('people', [(2353, 1317), (2478, 1317), (2478, 1440), (2353, 1440)], None, None, False), ('people', [(2235, 1174), (2316, 1174), (2316, 1365), (2235, 1365)], None, None, False), ('people', [(1228, 1223), (2343, 1223), (2343, 1568), (1228, 1568)], None, None, False), ('people', [(1362, 1186), (1408, 1186), (1408, 1285), (1362, 1285)], None, None, False), ('people', [(1525, 1185), (1586, 1185), (1586, 1229), (1525, 1229)], None, None, False), ('people', [(1472, 1187), (1523, 1187), (1523, 1249), (1472, 1249)], None, None, False), ('people', [(1416, 1179), (1469, 1179), (1469, 1268), (1416, 1268)], None, None, False), ('people', [(1241, 1168), (1319, 1168), (1319, 1323), (1241, 1323)], None, None, False), ('people', [(3, 1179), (112, 1179), (112, 1444), (3, 1444)], None, None, False), ('people', [(275, 1210), (319, 1210), (319, 1523), (275, 1523)], None, None, False), ('people', [(915, 1213), (981, 1213), (981, 1421), (915, 1421)], None, None, False), ('people', [(3316, 1146), (3365, 1146), (3365, 1186), (3316, 1186)], None, None, False)]
[('car', [(3758, 1193), (3840, 1193), (3840, 1472), (3758, 1472)], None, None, False), ('people', [(3645, 1135), (3763, 1135), (3763, 1462), (3645, 1462)], None, None, False), ('people', [(3398, 1148), (3518, 1148), (3518, 1426), (3398, 1426)], None, None, False), ('people', [(3237, 1151), (3349, 1151), (3349, 1403), (3237, 1403)], None, None, False), ('people', [(2782, 1174), (2869, 1174), (2869, 1266), (2782, 1266)], None, None, False), ('people', [(2770, 1131), (2861, 1131), (2861, 1192), (2770, 1192)], None, None, False), ('people', [(2739, 1126), (2796, 1126), (2796, 1194), (2739, 1194)], None, None, False), ('people', [(2698, 1144), (2764, 1144), (2764, 1316), (2698, 1316)], None, None, False), ('people', [(2639, 1158), (2704, 1158), (2704, 1302), (2639, 1302)], None, None, False), ('people', [(2584, 1166), (2639, 1166), (2639, 1255), (2584, 1255)], None, None, False), ('people', [(2603, 1128), (2645, 1128), (2645, 1169), (2603, 1169)], None, None, False), ('people', [(2541, 1152), (2583, 1152), (2583, 1240), (2541, 1240)], None, None, False), ('people', [(2508, 1174), (2567, 1174), (2567, 1235), (2508, 1235)], None, None, False), ('car', [(1933, 1223), (3079, 1223), (3079, 1564), (1933, 1564)], None, None, False), ('people', [(1952, 1171), (2010, 1171), (2010, 1317), (1952, 1317)], None, None, False), ('people', [(1529, 1182), (1602, 1182), (1602, 1405), (1529, 1405)], None, None, False), ('people', [(1483, 1194), (1548, 1194), (1548, 1416), (1483, 1416)], None, None, False), ('people', [(1414, 1188), (1488, 1188), (1488, 1414), (1414, 1414)], None, None, False), ('people', [(1365, 1181), (1418, 1181), (1418, 1363), (1365, 1363)], None, None, False), ('people', [(1316, 1181), (1374, 1181), (1374, 1368), (1316, 1368)], None, None, False), ('people', [(1243, 1166), (1307, 1166), (1307, 1369), (1243, 1369)], None, None, False), ('car', [(1, 1087), (316, 1087), (316, 1600), (1, 1600)], None, None, False), ('people', [(935, 1191), (988, 1191), (988, 1310), (935, 1310)], None, None, False)]
[('people', [(3644, 1135), (3764, 1135), (3764, 1372), (3644, 1372)], None, None, False), ('people', [(3374, 1152), (3451, 1152), (3451, 1328), (3374, 1328)], None, None, False), ('people', [(3190, 1143), (3272, 1143), (3272, 1254), (3190, 1254)], None, None, False), ('people', [(2799, 1167), (2854, 1167), (2854, 1233), (2799, 1233)], None, None, False), ('people', [(2768, 1123), (2863, 1123), (2863, 1236), (2768, 1236)], None, None, False), ('people', [(2665, 1154), (2742, 1154), (2742, 1273), (2665, 1273)], None, None, False), ('people', [(2642, 1136), (2736, 1136), (2736, 1210), (2642, 1210)], None, None, False), ('people', [(2597, 1131), (2660, 1131), (2660, 1191), (2597, 1191)], None, None, False), ('people', [(2581, 1163), (2640, 1163), (2640, 1306), (2581, 1306)], None, None, False), ('people', [(2538, 1154), (2575, 1154), (2575, 1198), (2538, 1198)], None, None, False), ('people', [(2506, 1175), (2573, 1175), (2573, 1400), (2506, 1400)], None, None, False), ('people', [(2356, 1320), (2475, 1320), (2475, 1444), (2356, 1444)], None, None, False), ('people', [(2227, 1176), (2296, 1176), (2296, 1433), (2227, 1433)], None, None, False), ('people', [(1955, 1178), (2008, 1178), (2008, 1348), (1955, 1348)], None, None, False), ('people', [(1528, 1181), (1593, 1181), (1593, 1407), (1528, 1407)], None, None, False), ('people', [(1471, 1193), (1538, 1193), (1538, 1412), (1471, 1412)], None, None, False), ('people', [(1415, 1182), (1486, 1182), (1486, 1412), (1415, 1412)], None, None, False), ('people', [(1332, 1184), (1386, 1184), (1386, 1354), (1332, 1354)], None, None, False), ('people', [(1286, 1183), (1335, 1183), (1335, 1363), (1286, 1363)], None, None, False), ('people', [(1285, 1165), (1313, 1165), (1313, 1279), (1285, 1279)], None, None, False), ('car', [(1, 1014), (1204, 1014), (1204, 1598), (1, 1598)], None, None, False)]
[('people', [(3671, 1133), (3766, 1133), (3766, 1222), (3671, 1222)], None, None, False), ('people', [(3300, 1154), (3357, 1154), (3357, 1238), (3300, 1238)], None, None, False), ('people', [(3114, 1143), (3244, 1143), (3244, 1301), (3114, 1301)], None, None, False), ('car', [(3051, 1201), (3840, 1201), (3840, 1563), (3051, 1563)], None, None, False), ('people', [(2779, 1167), (2873, 1167), (2873, 1398), (2779, 1398)], None, None, False), ('people', [(2770, 1125), (2859, 1125), (2859, 1201), (2770, 1201)], None, None, False), ('people', [(2652, 1151), (2726, 1151), (2726, 1414), (2652, 1414)], None, None, False), ('people', [(2615, 1127), (2663, 1127), (2663, 1192), (2615, 1192)], None, None, False), ('people', [(2575, 1149), (2650, 1149), (2650, 1413), (2575, 1413)], None, None, False), ('people', [(2537, 1147), (2577, 1147), (2577, 1196), (2537, 1196)], None, None, False), ('people', [(2507, 1174), (2578, 1174), (2578, 1407), (2507, 1407)], None, None, False), ('people', [(2353, 1318), (2480, 1318), (2480, 1439), (2353, 1439)], None, None, False), ('people', [(2231, 1167), (2295, 1167), (2295, 1435), (2231, 1435)], None, None, False), ('people', [(1956, 1176), (2009, 1176), (2009, 1349), (1956, 1349)], None, None, False), ('car', [(654, 994), (1955, 994), (1955, 1582), (654, 1582)], None, None, False), ('people', [(30, 1166), (141, 1166), (141, 1486), (30, 1486)], None, None, False)]
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False), ('people', [(1330, 1183), (1366, 1183), (1366, 1343), (1330, 1343)], None, None, False), ('people', [(1596, 1172), (1649, 1172), (1649, 1218), (1596, 1218)], None, None, False), ('people', [(285, 1207), (322, 1207), (322, 1522), (285, 1522)], None, None, False), ('people', [(206, 1183), (294, 1183), (294, 1379), (206, 1379)], None, None, False), ('people', [(1, 1161), (139, 1161), (139, 1464), (1, 1464)], None, None, False)]
[('people', [(1291, 1072), (2029, 1072), (2029, 2160), (1291, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False), ('people', [(2454, 1120), (2827, 1120), (2827, 1863), (2454, 1863)], None, None, False), ('people', [(2802, 1169), (2875, 1169), (2875, 1383), (2802, 1383)], None, None, False), ('people', [(2780, 1114), (2864, 1114), (2864, 1210), (2780, 1210)], None, None, False), ('people', [(2681, 1150), (2714, 1150), (2714, 1187), (2681, 1187)], None, None, False), ('people', [(2678, 1154), (2760, 1154), (2760, 1216), (2678, 1216)], None, None, False), ('people', [(2628, 1152), (2665, 1152), (2665, 1201), (2628, 1201)], None, None, False), ('people', [(2598, 1122), (2643, 1122), (2643, 1191), (2598, 1191)], None, None, False), ('people', [(2357, 1309), (2459, 1309), (2459, 1446), (2357, 1446)], None, None, False), ('people', [(2257, 1156), (2326, 1156), (2326, 1386), (2257, 1386)], None, None, False), ('people', [(2224, 1173), (2294, 1173), (2294, 1429), (2224, 1429)], None, None, False), ('people', [(1964, 1179), (2016, 1179), (2016, 1350), (1964, 1350)], None, None, False), ('people', [(1936, 1170), (1963, 1170), (1963, 1307), (1936, 1307)], None, None, False), ('people', [(1527, 1192), (1590, 1192), (1590, 1409), (1527, 1409)], None, None, False), ('people', [(1475, 1189), (1544, 1189), (1544, 1414), (1475, 1414)], None, None, False), ('people', [(1421, 1176), (1485, 1176), (1485, 1414), (1421, 1414)], None, None, False), ('people', [(1352, 1169), (1382, 1169), (1382, 1264), (1352, 1264)], None, None, False), ('people', [(917, 1188), (986, 1188), (986, 1443), (917, 1443)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False), ('people', [(2454, 1120), (2827, 1120), (2827, 1863), (2454, 1863)], None, None, False), ('people', [(2802, 1169), (2875, 1169), (2875, 1383), (2802, 1383)], None, None, False), ('people', [(2780, 1114), (2864, 1114), (2864, 1210), (2780, 1210)], None, None, False), ('people', [(2681, 1150), (2714, 1150), (2714, 1187), (2681, 1187)], None, None, False), ('people', [(2678, 1154), (2760, 1154), (2760, 1216), (2678, 1216)], None, None, False), ('people', [(2628, 1152), (2665, 1152), (2665, 1201), (2628, 1201)], None, None, False), ('people', [(2598, 1122), (2643, 1122), (2643, 1191), (2598, 1191)], None, None, False), ('people', [(2357, 1309), (2459, 1309), (2459, 1446), (2357, 1446)], None, None, False), ('people', [(2257, 1156), (2326, 1156), (2326, 1386), (2257, 1386)], None, None, False), ('people', [(2224, 1173), (2294, 1173), (2294, 1429), (2224, 1429)], None, None, False), ('people', [(1964, 1179), (2016, 1179), (2016, 1350), (1964, 1350)], None, None, False), ('people', [(1936, 1170), (1963, 1170), (1963, 1307), (1936, 1307)], None, None, False), ('people', [(1527, 1192), (1590, 1192), (1590, 1409), (1527, 1409)], None, None, False), ('people', [(1475, 1189), (1544, 1189), (1544, 1414), (1475, 1414)], None, None, False), ('people', [(1421, 1176), (1485, 1176), (1485, 1414), (1421, 1414)], None, None, False), ('people', [(1352, 1169), (1382, 1169), (1382, 1264), (1352, 1264)], None, None, False), ('people', [(917, 1188), (986, 1188), (986, 1443), (917, 1443)], None, None, False)]
[('car', [(3102, 1128), (3840, 1128), (3840, 1541), (3102, 1541)], None, None, False), ('people', [(884, 949), (1242, 949), (1242, 2160), (884, 2160)], None, None, False), ('people', [(1, 1162), (45, 1162), (45, 1459), (1, 1459)], None, None, False), ('people', [(1238, 1160), (1279, 1160), (1279, 1243), (1238, 1243)], None, None, False), ('people', [(1271, 1161), (1398, 1161), (1398, 1432), (1271, 1432)], None, None, False), ('people', [(1413, 1176), (1472, 1176), (1472, 1405), (1413, 1405)], None, None, False), ('people', [(1471, 1196), (1533, 1196), (1533, 1406), (1471, 1406)], None, None, False), ('people', [(1520, 1174), (1589, 1174), (1589, 1420), (1520, 1420)], None, None, False), ('people', [(1622, 1158), (1669, 1158), (1669, 1215), (1622, 1215)], None, None, False), ('people', [(1991, 1177), (2024, 1177), (2024, 1358), (1991, 1358)], None, None, False), ('people', [(2194, 1173), (2279, 1173), (2279, 1423), (2194, 1423)], None, None, False), ('people', [(2279, 1172), (2321, 1172), (2321, 1422), (2279, 1422)], None, None, False), ('people', [(2319, 1152), (2366, 1152), (2366, 1412), (2319, 1412)], None, None, False), ('people', [(2359, 1172), (2411, 1172), (2411, 1381), (2359, 1381)], None, None, False), ('people', [(2428, 1176), (2497, 1176), (2497, 1342), (2428, 1342)], None, None, False), ('people', [(2534, 1138), (2606, 1138), (2606, 1421), (2534, 1421)], None, None, False), ('people', [(2598, 1156), (2650, 1156), (2650, 1403), (2598, 1403)], None, None, False), ('people', [(2621, 1158), (2709, 1158), (2709, 1414), (2621, 1414)], None, None, False), ('people', [(2678, 1143), (2747, 1143), (2747, 1409), (2678, 1409)], None, None, False), ('people', [(2602, 1131), (2637, 1131), (2637, 1158), (2602, 1158)], None, None, False), ('people', [(2739, 1157), (2786, 1157), (2786, 1395), (2739, 1395)], None, None, False), ('people', [(2779, 1125), (2838, 1125), (2838, 1167), (2779, 1167)], None, None, False), ('people', [(2791, 1163), (2875, 1163), (2875, 1417), (2791, 1417)], None, None, False)]
[('people', [(3645, 1139), (3762, 1139), (3762, 1458), (3645, 1458)], None, None, False), ('people', [(3237, 1158), (3314, 1158), (3314, 1408), (3237, 1408)], None, None, False), ('people', [(3142, 1143), (3247, 1143), (3247, 1406), (3142, 1406)], None, None, False), ('people', [(2229, 1172), (2273, 1172), (2273, 1434), (2229, 1434)], None, None, False), ('people', [(2280, 1173), (2342, 1173), (2342, 1271), (2280, 1271)], None, None, False), ('people', [(2229, 1013), (2607, 1013), (2607, 2160), (2229, 2160)], None, None, False), ('people', [(2530, 1132), (2593, 1132), (2593, 1428), (2530, 1428)], None, None, False), ('people', [(2603, 1166), (2640, 1166), (2640, 1408), (2603, 1408)], None, None, False), ('people', [(2596, 1125), (2647, 1125), (2647, 1191), (2596, 1191)], None, None, False), ('people', [(2627, 1143), (2704, 1143), (2704, 1420), (2627, 1420)], None, None, False), ('people', [(2697, 1149), (2742, 1149), (2742, 1415), (2697, 1415)], None, None, False), ('people', [(2732, 1159), (2778, 1159), (2778, 1405), (2732, 1405)], None, None, False), ('people', [(2754, 1159), (2864, 1159), (2864, 1396), (2754, 1396)], None, None, False), ('people', [(2793, 1128), (2880, 1128), (2880, 1389), (2793, 1389)], None, None, False), ('people', [(1527, 1178), (1590, 1178), (1590, 1400), (1527, 1400)], None, None, False), ('people', [(1470, 1176), (1578, 1176), (1578, 1454), (1470, 1454)], None, None, False), ('people', [(1414, 1178), (1487, 1178), (1487, 1415), (1414, 1415)], None, None, False), ('people', [(1374, 1174), (1403, 1174), (1403, 1229), (1374, 1229)], None, None, False), ('people', [(1322, 1171), (1360, 1171), (1360, 1273), (1322, 1273)], None, None, False), ('people', [(1257, 1169), (1299, 1169), (1299, 1258), (1257, 1258)], None, None, False), ('people', [(907, 1182), (979, 1182), (979, 1434), (907, 1434)], None, None, False), ('people', [(276, 1201), (318, 1201), (318, 1525), (276, 1525)], None, None, False)]
[('people', [(3664, 1139), (3759, 1139), (3759, 1295), (3664, 1295)], None, None, False), ('people', [(3329, 972), (3785, 972), (3785, 2160), (3329, 2160)], None, None, False), ('people', [(3233, 1155), (3309, 1155), (3309, 1418), (3233, 1418)], None, None, False), ('people', [(3147, 1138), (3242, 1138), (3242, 1413), (3147, 1413)], None, None, False), ('people', [(2762, 1158), (2863, 1158), (2863, 1419), (2762, 1419)], None, None, False), ('people', [(2790, 1127), (2885, 1127), (2885, 1374), (2790, 1374)], None, None, False), ('people', [(2696, 1173), (2754, 1173), (2754, 1405), (2696, 1405)], None, None, False), ('people', [(2663, 1142), (2720, 1142), (2720, 1406), (2663, 1406)], None, None, False), ('people', [(2631, 1155), (2702, 1155), (2702, 1426), (2631, 1426)], None, None, False), ('people', [(2592, 1165), (2649, 1165), (2649, 1407), (2592, 1407)], None, None, False), ('people', [(2589, 1132), (2631, 1132), (2631, 1168), (2589, 1168)], None, None, False), ('people', [(2537, 1138), (2622, 1138), (2622, 1425), (2537, 1425)], None, None, False), ('people', [(2509, 1176), (2552, 1176), (2552, 1396), (2509, 1396)], None, None, False), ('people', [(2429, 1146), (2502, 1146), (2502, 1365), (2429, 1365)], None, None, False), ('people', [(2367, 1146), (2420, 1146), (2420, 1335), (2367, 1335)], None, None, False), ('people', [(2343, 1183), (2406, 1183), (2406, 1382), (2343, 1382)], None, None, False), ('people', [(2242, 1168), (2346, 1168), (2346, 1427), (2242, 1427)], None, None, False), ('people', [(1544, 1183), (1604, 1183), (1604, 1409), (1544, 1409)], None, None, False), ('people', [(1680, 1156), (1722, 1156), (1722, 1213), (1680, 1213)], None, None, False), ('people', [(1484, 1188), (1552, 1188), (1552, 1416), (1484, 1416)], None, None, False), ('people', [(1364, 1169), (1453, 1169), (1453, 1409), (1364, 1409)], None, None, False), ('people', [(1434, 1175), (1486, 1175), (1486, 1406), (1434, 1406)], None, None, False), ('people', [(903, 1180), (986, 1180), (986, 1447), (903, 1447)], None, None, False), ('people', [(266, 1195), (319, 1195), (319, 1516), (266, 1516)], None, None, False), ('people', [(1309, 1177), (1342, 1177), (1342, 1287), (1309, 1287)], None, None, False), ('people', [(1277, 1162), (1311, 1162), (1311, 1267), (1277, 1267)], None, None, False), ('people', [(1242, 1170), (1273, 1170), (1273, 1269), (1242, 1269)], None, None, False)]
[('people', [(3645, 1139), (3762, 1139), (3762, 1458), (3645, 1458)], None, None, False), ('people', [(3237, 1158), (3314, 1158), (3314, 1408), (3237, 1408)], None, None, False), ('people', [(3142, 1143), (3247, 1143), (3247, 1406), (3142, 1406)], None, None, False), ('people', [(2229, 1172), (2273, 1172), (2273, 1434), (2229, 1434)], None, None, False), ('people', [(2280, 1173), (2342, 1173), (2342, 1271), (2280, 1271)], None, None, False), ('people', [(2229, 1013), (2607, 1013), (2607, 2160), (2229, 2160)], None, None, False), ('people', [(2530, 1132), (2593, 1132), (2593, 1428), (2530, 1428)], None, None, False), ('people', [(2603, 1166), (2640, 1166), (2640, 1408), (2603, 1408)], None, None, False), ('people', [(2596, 1125), (2647, 1125), (2647, 1191), (2596, 1191)], None, None, False), ('people', [(2627, 1143), (2704, 1143), (2704, 1420), (2627, 1420)], None, None, False), ('people', [(2697, 1149), (2742, 1149), (2742, 1415), (2697, 1415)], None, None, False), ('people', [(2732, 1159), (2778, 1159), (2778, 1405), (2732, 1405)], None, None, False), ('people', [(2754, 1159), (2864, 1159), (2864, 1396), (2754, 1396)], None, None, False), ('people', [(2793, 1128), (2880, 1128), (2880, 1389), (2793, 1389)], None, None, False), ('people', [(1527, 1178), (1590, 1178), (1590, 1400), (1527, 1400)], None, None, False), ('people', [(1470, 1176), (1578, 1176), (1578, 1454), (1470, 1454)], None, None, False), ('people', [(1414, 1178), (1487, 1178), (1487, 1415), (1414, 1415)], None, None, False), ('people', [(1374, 1174), (1403, 1174), (1403, 1229), (1374, 1229)], None, None, False), ('people', [(1322, 1171), (1360, 1171), (1360, 1273), (1322, 1273)], None, None, False), ('people', [(1257, 1169), (1299, 1169), (1299, 1258), (1257, 1258)], None, None, False), ('people', [(907, 1182), (979, 1182), (979, 1434), (907, 1434)], None, None, False), ('people', [(276, 1201), (318, 1201), (318, 1525), (276, 1525)], None, None, False)]
[('car', [(3102, 1128), (3840, 1128), (3840, 1541), (3102, 1541)], None, None, False), ('people', [(851, 943), (1209, 943), (1209, 2154), (851, 2154)], None, None, False), ('people', [(1, 1162), (45, 1162), (45, 1459), (1, 1459)], None, None, False), ('people', [(1238, 1160), (1279, 1160), (1279, 1243), (1238, 1243)], None, None, False), ('people', [(1271, 1161), (1398, 1161), (1398, 1432), (1271, 1432)], None, None, False), ('people', [(1413, 1176), (1472, 1176), (1472, 1405), (1413, 1405)], None, None, False), ('people', [(1471, 1196), (1533, 1196), (1533, 1406), (1471, 1406)], None, None, False), ('people', [(1520, 1174), (1589, 1174), (1589, 1420), (1520, 1420)], None, None, False), ('people', [(1622, 1158), (1669, 1158), (1669, 1215), (1622, 1215)], None, None, False), ('people', [(1991, 1177), (2024, 1177), (2024, 1361), (1991, 1361)], None, None, False), ('people', [(2194, 1173), (2279, 1173), (2279, 1423), (2194, 1423)], None, None, False), ('people', [(2279, 1172), (2321, 1172), (2321, 1422), (2279, 1422)], None, None, False), ('people', [(2319, 1152), (2366, 1152), (2366, 1412), (2319, 1412)], None, None, False), ('people', [(2359, 1172), (2411, 1172), (2411, 1381), (2359, 1381)], None, None, False), ('people', [(2428, 1176), (2497, 1176), (2497, 1342), (2428, 1342)], None, None, False), ('people', [(2534, 1138), (2606, 1138), (2606, 1421), (2534, 1421)], None, None, False), ('people', [(2598, 1156), (2650, 1156), (2650, 1403), (2598, 1403)], None, None, False), ('people', [(2621, 1158), (2709, 1158), (2709, 1414), (2621, 1414)], None, None, False), ('people', [(2678, 1143), (2747, 1143), (2747, 1409), (2678, 1409)], None, None, False), ('people', [(2602, 1131), (2637, 1131), (2637, 1158), (2602, 1158)], None, None, False), ('people', [(2739, 1157), (2786, 1157), (2786, 1395), (2739, 1395)], None, None, False), ('people', [(2779, 1125), (2838, 1125), (2838, 1167), (2779, 1167)], None, None, False), ('people', [(2791, 1163), (2875, 1163), (2875, 1417), (2791, 1417)], None, None, False)]
[('people', [(3637, 1136), (3763, 1136), (3763, 1456), (3637, 1456)], None, None, False), ('people', [(1, 1141), (57, 1141), (57, 1465), (1, 1465)], None, None, False), ('people', [(51, 1169), (104, 1169), (104, 1439), (51, 1439)], None, None, False), ('people', [(273, 1201), (325, 1201), (325, 1515), (273, 1515)], None, None, False), ('people', [(1114, 1165), (1273, 1165), (1273, 1541), (1114, 1541)], None, None, False), ('people', [(1352, 1165), (1396, 1165), (1396, 1282), (1352, 1282)], None, None, False), ('people', [(1412, 1183), (1489, 1183), (1489, 1410), (1412, 1410)], None, None, False), ('people', [(1490, 1190), (1545, 1190), (1545, 1414), (1490, 1414)], None, None, False), ('people', [(1530, 1185), (1579, 1185), (1579, 1395), (1530, 1395)], None, None, False), ('people', [(1583, 1162), (1621, 1162), (1621, 1219), (1583, 1219)], None, None, False), ('people', [(1657, 1152), (1695, 1152), (1695, 1216), (1657, 1216)], None, None, False), ('people', [(1943, 1174), (1970, 1174), (1970, 1341), (1943, 1341)], None, None, False), ('people', [(1963, 1174), (2016, 1174), (2016, 1349), (1963, 1349)], None, None, False), ('car', [(2017, 1155), (3083, 1155), (3083, 1550), (2017, 1550)], None, None, False), ('people', [(2604, 1127), (2640, 1127), (2640, 1162), (2604, 1162)], None, None, False), ('people', [(2670, 1154), (2704, 1154), (2704, 1198), (2670, 1198)], None, None, False), ('people', [(2749, 1146), (2791, 1146), (2791, 1205), (2749, 1205)], None, None, False), ('people', [(2799, 1126), (2853, 1126), (2853, 1168), (2799, 1168)], None, None, False), ('people', [(2799, 1162), (2858, 1162), (2858, 1265), (2799, 1265)], None, None, False), ('people', [(3144, 1143), (3247, 1143), (3247, 1418), (3144, 1418)], None, None, False), ('people', [(3241, 1156), (3313, 1156), (3313, 1412), (3241, 1412)], None, None, False)]
[('people', [(3641, 1135), (3760, 1135), (3760, 1453), (3641, 1453)], None, None, False), ('people', [(3241, 1156), (3312, 1156), (3312, 1405), (3241, 1405)], None, None, False), ('people', [(3141, 1143), (3245, 1143), (3245, 1405), (3141, 1405)], None, None, False), ('people', [(2775, 1179), (2844, 1179), (2844, 1444), (2775, 1444)], None, None, False), ('people', [(2768, 1152), (2856, 1152), (2856, 1210), (2768, 1210)], None, None, False), ('people', [(2797, 1122), (2833, 1122), (2833, 1157), (2797, 1157)], None, None, False), ('people', [(2675, 1152), (2755, 1152), (2755, 1409), (2675, 1409)], None, None, False), ('people', [(2601, 1159), (2674, 1159), (2674, 1401), (2601, 1401)], None, None, False), ('people', [(2588, 1125), (2678, 1125), (2678, 1191), (2588, 1191)], None, None, False), ('people', [(2529, 1141), (2610, 1141), (2610, 1419), (2529, 1419)], None, None, False), ('people', [(2500, 1171), (2552, 1171), (2552, 1400), (2500, 1400)], None, None, False), ('people', [(2358, 1316), (2489, 1316), (2489, 1441), (2358, 1441)], None, None, False), ('people', [(2291, 1156), (2372, 1156), (2372, 1390), (2291, 1390)], None, None, False), ('people', [(2256, 1151), (2296, 1151), (2296, 1393), (2256, 1393)], None, None, False), ('people', [(2199, 1178), (2285, 1178), (2285, 1430), (2199, 1430)], None, None, False), ('people', [(1312, 1153), (1540, 1153), (1540, 1656), (1312, 1656)], None, None, False), ('people', [(1, 1147), (113, 1147), (113, 1459), (1, 1459)], None, None, False), ('people', [(277, 1201), (315, 1201), (315, 1518), (277, 1518)], None, None, False), ('car', [(985, 1163), (1964, 1163), (1964, 1555), (985, 1555)], None, None, False)]
[('people', [(3638, 1137), (3759, 1137), (3759, 1455), (3638, 1455)], None, None, False), ('people', [(3240, 1152), (3312, 1152), (3312, 1406), (3240, 1406)], None, None, False), ('people', [(3144, 1146), (3247, 1146), (3247, 1409), (3144, 1409)], None, None, False), ('people', [(3003, 1163), (3124, 1163), (3124, 1434), (3003, 1434)], None, None, False), ('people', [(2786, 1169), (2877, 1169), (2877, 1405), (2786, 1405)], None, None, False), ('people', [(2786, 1118), (2869, 1118), (2869, 1174), (2786, 1174)], None, None, False), ('people', [(2670, 1150), (2742, 1150), (2742, 1395), (2670, 1395)], None, None, False), ('people', [(2735, 1151), (2789, 1151), (2789, 1400), (2735, 1400)], None, None, False), ('people', [(2612, 1160), (2695, 1160), (2695, 1403), (2612, 1403)], None, None, False), ('people', [(2599, 1131), (2668, 1131), (2668, 1195), (2599, 1195)], None, None, False), ('people', [(2544, 1137), (2616, 1137), (2616, 1418), (2544, 1418)], None, None, False), ('people', [(2506, 1171), (2552, 1171), (2552, 1399), (2506, 1399)], None, None, False), ('people', [(2355, 1317), (2475, 1317), (2475, 1450), (2355, 1450)], None, None, False), ('people', [(2276, 1162), (2345, 1162), (2345, 1395), (2276, 1395)], None, None, False), ('people', [(2231, 1173), (2295, 1173), (2295, 1434), (2231, 1434)], None, None, False), ('people', [(1749, 1137), (2042, 1137), (2042, 1704), (1749, 1704)], None, None, False), ('people', [(7, 1151), (161, 1151), (161, 1474), (7, 1474)], None, None, False), ('car', [(157, 1169), (1254, 1169), (1254, 1574), (157, 1574)], None, None, False), ('people', [(1249, 1177), (1290, 1177), (1290, 1322), (1249, 1322)], None, None, False), ('people', [(1357, 1166), (1384, 1166), (1384, 1264), (1357, 1264)], None, None, False), ('people', [(1420, 1178), (1481, 1178), (1481, 1415), (1420, 1415)], None, None, False), ('people', [(1479, 1189), (1537, 1189), (1537, 1414), (1479, 1414)], None, None, False), ('people', [(1530, 1187), (1593, 1187), (1593, 1411), (1530, 1411)], None, None, False), ('people', [(1517, 1156), (1543, 1156), (1543, 1206), (1517, 1206)], None, None, False), ('people', [(1572, 1153), (1608, 1153), (1608, 1222), (1572, 1222)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False), ('people', [(2454, 1120), (2827, 1120), (2827, 1863), (2454, 1863)], None, None, False), ('people', [(2802, 1169), (2875, 1169), (2875, 1383), (2802, 1383)], None, None, False), ('people', [(2780, 1114), (2864, 1114), (2864, 1210), (2780, 1210)], None, None, False), ('people', [(2681, 1150), (2714, 1150), (2714, 1187), (2681, 1187)], None, None, False), ('people', [(2678, 1154), (2760, 1154), (2760, 1216), (2678, 1216)], None, None, False), ('people', [(2628, 1152), (2665, 1152), (2665, 1201), (2628, 1201)], None, None, False), ('people', [(2598, 1122), (2643, 1122), (2643, 1191), (2598, 1191)], None, None, False), ('people', [(2357, 1309), (2459, 1309), (2459, 1446), (2357, 1446)], None, None, False), ('people', [(2257, 1156), (2326, 1156), (2326, 1386), (2257, 1386)], None, None, False), ('people', [(2224, 1173), (2294, 1173), (2294, 1429), (2224, 1429)], None, None, False), ('people', [(1964, 1179), (2016, 1179), (2016, 1350), (1964, 1350)], None, None, False), ('people', [(1936, 1170), (1963, 1170), (1963, 1307), (1936, 1307)], None, None, False), ('people', [(1527, 1192), (1590, 1192), (1590, 1409), (1527, 1409)], None, None, False), ('people', [(1475, 1189), (1544, 1189), (1544, 1414), (1475, 1414)], None, None, False), ('people', [(1421, 1176), (1485, 1176), (1485, 1414), (1421, 1414)], None, None, False), ('people', [(1352, 1169), (1382, 1169), (1382, 1264), (1352, 1264)], None, None, False), ('people', [(917, 1188), (986, 1188), (986, 1443), (917, 1443)], None, None, False)]
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
[('people', [(1291, 1072), (2029, 1072), (2029, 2160), (1291, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False), ('people', [(2454, 1120), (2827, 1120), (2827, 1863), (2454, 1863)], None, None, False), ('people', [(2802, 1169), (2875, 1169), (2875, 1383), (2802, 1383)], None, None, False), ('people', [(2780, 1114), (2864, 1114), (2864, 1210), (2780, 1210)], None, None, False), ('people', [(2681, 1150), (2714, 1150), (2714, 1187), (2681, 1187)], None, None, False), ('people', [(2678, 1154), (2760, 1154), (2760, 1216), (2678, 1216)], None, None, False), ('people', [(2628, 1152), (2665, 1152), (2665, 1201), (2628, 1201)], None, None, False), ('people', [(2598, 1122), (2643, 1122), (2643, 1191), (2598, 1191)], None, None, False), ('people', [(2357, 1309), (2459, 1309), (2459, 1446), (2357, 1446)], None, None, False), ('people', [(2257, 1156), (2326, 1156), (2326, 1386), (2257, 1386)], None, None, False), ('people', [(2224, 1173), (2294, 1173), (2294, 1429), (2224, 1429)], None, None, False), ('people', [(1964, 1179), (2016, 1179), (2016, 1350), (1964, 1350)], None, None, False), ('people', [(1936, 1170), (1963, 1170), (1963, 1307), (1936, 1307)], None, None, False), ('people', [(1527, 1192), (1590, 1192), (1590, 1409), (1527, 1409)], None, None, False), ('people', [(1475, 1189), (1544, 1189), (1544, 1414), (1475, 1414)], None, None, False), ('people', [(1421, 1176), (1485, 1176), (1485, 1414), (1421, 1414)], None, None, False), ('people', [(1352, 1169), (1382, 1169), (1382, 1264), (1352, 1264)], None, None, False), ('people', [(917, 1188), (986, 1188), (986, 1443), (917, 1443)], None, None, False)]
[('people', [(3638, 1137), (3759, 1137), (3759, 1455), (3638, 1455)], None, None, False), ('people', [(3240, 1152), (3312, 1152), (3312, 1406), (3240, 1406)], None, None, False), ('people', [(3144, 1146), (3247, 1146), (3247, 1409), (3144, 1409)], None, None, False), ('people', [(3003, 1163), (3124, 1163), (3124, 1434), (3003, 1434)], None, None, False), ('people', [(2786, 1169), (2877, 1169), (2877, 1405), (2786, 1405)], None, None, False), ('people', [(2786, 1118), (2869, 1118), (2869, 1174), (2786, 1174)], None, None, False), ('people', [(2670, 1150), (2742, 1150), (2742, 1395), (2670, 1395)], None, None, False), ('people', [(2735, 1151), (2789, 1151), (2789, 1400), (2735, 1400)], None, None, False), ('people', [(2612, 1160), (2695, 1160), (2695, 1403), (2612, 1403)], None, None, False), ('people', [(2599, 1131), (2668, 1131), (2668, 1195), (2599, 1195)], None, None, False), ('people', [(2544, 1137), (2616, 1137), (2616, 1418), (2544, 1418)], None, None, False), ('people', [(2506, 1171), (2552, 1171), (2552, 1399), (2506, 1399)], None, None, False), ('people', [(2355, 1317), (2475, 1317), (2475, 1450), (2355, 1450)], None, None, False), ('people', [(2276, 1162), (2345, 1162), (2345, 1395), (2276, 1395)], None, None, False), ('people', [(2231, 1173), (2295, 1173), (2295, 1434), (2231, 1434)], None, None, False), ('people', [(1749, 1137), (2042, 1137), (2042, 1704), (1749, 1704)], None, None, False), ('people', [(7, 1151), (161, 1151), (161, 1474), (7, 1474)], None, None, False), ('car', [(157, 1169), (1254, 1169), (1254, 1574), (157, 1574)], None, None, False), ('people', [(1249, 1177), (1290, 1177), (1290, 1322), (1249, 1322)], None, None, False), ('people', [(1357, 1166), (1384, 1166), (1384, 1264), (1357, 1264)], None, None, False), ('people', [(1420, 1178), (1481, 1178), (1481, 1415), (1420, 1415)], None, None, False), ('people', [(1479, 1189), (1537, 1189), (1537, 1414), (1479, 1414)], None, None, False), ('people', [(1530, 1187), (1593, 1187), (1593, 1411), (1530, 1411)], None, None, False), ('people', [(1517, 1156), (1543, 1156), (1543, 1206), (1517, 1206)], None, None, False), ('people', [(1572, 1153), (1608, 1153), (1608, 1222), (1572, 1222)], None, None, False)]
[('people', [(3641, 1135), (3760, 1135), (3760, 1453), (3641, 1453)], None, None, False), ('people', [(3241, 1156), (3312, 1156), (3312, 1405), (3241, 1405)], None, None, False), ('people', [(3141, 1143), (3245, 1143), (3245, 1405), (3141, 1405)], None, None, False), ('people', [(2775, 1179), (2844, 1179), (2844, 1444), (2775, 1444)], None, None, False), ('people', [(2768, 1152), (2856, 1152), (2856, 1210), (2768, 1210)], None, None, False), ('people', [(2797, 1122), (2833, 1122), (2833, 1157), (2797, 1157)], None, None, False), ('people', [(2675, 1152), (2755, 1152), (2755, 1409), (2675, 1409)], None, None, False), ('people', [(2601, 1159), (2674, 1159), (2674, 1401), (2601, 1401)], None, None, False), ('people', [(2588, 1125), (2678, 1125), (2678, 1191), (2588, 1191)], None, None, False), ('people', [(2529, 1141), (2610, 1141), (2610, 1419), (2529, 1419)], None, None, False), ('people', [(2500, 1171), (2552, 1171), (2552, 1400), (2500, 1400)], None, None, False), ('people', [(2358, 1316), (2489, 1316), (2489, 1441), (2358, 1441)], None, None, False), ('people', [(2291, 1156), (2372, 1156), (2372, 1390), (2291, 1390)], None, None, False), ('people', [(2256, 1151), (2296, 1151), (2296, 1393), (2256, 1393)], None, None, False), ('people', [(2199, 1178), (2285, 1178), (2285, 1430), (2199, 1430)], None, None, False), ('people', [(1312, 1153), (1540, 1153), (1540, 1656), (1312, 1656)], None, None, False), ('people', [(1, 1147), (113, 1147), (113, 1459), (1, 1459)], None, None, False), ('people', [(277, 1201), (315, 1201), (315, 1518), (277, 1518)], None, None, False), ('car', [(985, 1163), (1964, 1163), (1964, 1555), (985, 1555)], None, None, False)]
[('people', [(3637, 1136), (3763, 1136), (3763, 1456), (3637, 1456)], None, None, False), ('people', [(1, 1141), (57, 1141), (57, 1465), (1, 1465)], None, None, False), ('people', [(51, 1169), (104, 1169), (104, 1439), (51, 1439)], None, None, False), ('people', [(273, 1201), (325, 1201), (325, 1515), (273, 1515)], None, None, False), ('people', [(1114, 1165), (1273, 1165), (1273, 1541), (1114, 1541)], None, None, False), ('people', [(1352, 1165), (1396, 1165), (1396, 1282), (1352, 1282)], None, None, False), ('people', [(1412, 1183), (1489, 1183), (1489, 1410), (1412, 1410)], None, None, False), ('people', [(1490, 1190), (1545, 1190), (1545, 1414), (1490, 1414)], None, None, False), ('people', [(1530, 1185), (1579, 1185), (1579, 1395), (1530, 1395)], None, None, False), ('people', [(1583, 1162), (1621, 1162), (1621, 1219), (1583, 1219)], None, None, False), ('people', [(1657, 1152), (1695, 1152), (1695, 1216), (1657, 1216)], None, None, False), ('people', [(1943, 1174), (1970, 1174), (1970, 1341), (1943, 1341)], None, None, False), ('people', [(1963, 1174), (2016, 1174), (2016, 1349), (1963, 1349)], None, None, False), ('car', [(2017, 1155), (3083, 1155), (3083, 1550), (2017, 1550)], None, None, False), ('people', [(2604, 1127), (2640, 1127), (2640, 1162), (2604, 1162)], None, None, False), ('people', [(2670, 1154), (2704, 1154), (2704, 1198), (2670, 1198)], None, None, False), ('people', [(2749, 1146), (2791, 1146), (2791, 1205), (2749, 1205)], None, None, False), ('people', [(2799, 1126), (2853, 1126), (2853, 1168), (2799, 1168)], None, None, False), ('people', [(2799, 1162), (2858, 1162), (2858, 1265), (2799, 1265)], None, None, False), ('people', [(3144, 1143), (3247, 1143), (3247, 1418), (3144, 1418)], None, None, False), ('people', [(3241, 1156), (3313, 1156), (3313, 1412), (3241, 1412)], None, None, False)]
[('car', [(3102, 1128), (3840, 1128), (3840, 1541), (3102, 1541)], None, None, False), ('people', [(851, 943), (1209, 943), (1209, 2154), (851, 2154)], None, None, False), ('people', [(1, 1162), (45, 1162), (45, 1459), (1, 1459)], None, None, False), ('people', [(1238, 1160), (1279, 1160), (1279, 1243), (1238, 1243)], None, None, False), ('people', [(1271, 1161), (1398, 1161), (1398, 1432), (1271, 1432)], None, None, False), ('people', [(1413, 1176), (1472, 1176), (1472, 1405), (1413, 1405)], None, None, False), ('people', [(1471, 1196), (1533, 1196), (1533, 1406), (1471, 1406)], None, None, False), ('people', [(1520, 1174), (1589, 1174), (1589, 1420), (1520, 1420)], None, None, False), ('people', [(1622, 1158), (1669, 1158), (1669, 1215), (1622, 1215)], None, None, False), ('people', [(1991, 1177), (2024, 1177), (2024, 1361), (1991, 1361)], None, None, False), ('people', [(2194, 1173), (2279, 1173), (2279, 1423), (2194, 1423)], None, None, False), ('people', [(2279, 1172), (2321, 1172), (2321, 1422), (2279, 1422)], None, None, False), ('people', [(2319, 1152), (2366, 1152), (2366, 1412), (2319, 1412)], None, None, False), ('people', [(2359, 1172), (2411, 1172), (2411, 1381), (2359, 1381)], None, None, False), ('people', [(2428, 1176), (2497, 1176), (2497, 1342), (2428, 1342)], None, None, False), ('people', [(2534, 1138), (2606, 1138), (2606, 1421), (2534, 1421)], None, None, False), ('people', [(2598, 1156), (2650, 1156), (2650, 1403), (2598, 1403)], None, None, False), ('people', [(2621, 1158), (2709, 1158), (2709, 1414), (2621, 1414)], None, None, False), ('people', [(2678, 1143), (2747, 1143), (2747, 1409), (2678, 1409)], None, None, False), ('people', [(2602, 1131), (2637, 1131), (2637, 1158), (2602, 1158)], None, None, False), ('people', [(2739, 1157), (2786, 1157), (2786, 1395), (2739, 1395)], None, None, False), ('people', [(2779, 1125), (2838, 1125), (2838, 1167), (2779, 1167)], None, None, False), ('people', [(2791, 1163), (2875, 1163), (2875, 1417), (2791, 1417)], None, None, False)]
[('people', [(3645, 1139), (3762, 1139), (3762, 1458), (3645, 1458)], None, None, False), ('people', [(3237, 1158), (3314, 1158), (3314, 1408), (3237, 1408)], None, None, False), ('people', [(3142, 1143), (3247, 1143), (3247, 1406), (3142, 1406)], None, None, False), ('people', [(2229, 1172), (2273, 1172), (2273, 1434), (2229, 1434)], None, None, False), ('people', [(2280, 1173), (2342, 1173), (2342, 1271), (2280, 1271)], None, None, False), ('people', [(2229, 1013), (2607, 1013), (2607, 2160), (2229, 2160)], None, None, False), ('people', [(2530, 1132), (2593, 1132), (2593, 1428), (2530, 1428)], None, None, False), ('people', [(2603, 1166), (2640, 1166), (2640, 1408), (2603, 1408)], None, None, False), ('people', [(2596, 1125), (2647, 1125), (2647, 1191), (2596, 1191)], None, None, False), ('people', [(2627, 1143), (2704, 1143), (2704, 1420), (2627, 1420)], None, None, False), ('people', [(2697, 1149), (2742, 1149), (2742, 1415), (2697, 1415)], None, None, False), ('people', [(2732, 1159), (2778, 1159), (2778, 1405), (2732, 1405)], None, None, False), ('people', [(2754, 1159), (2864, 1159), (2864, 1396), (2754, 1396)], None, None, False), ('people', [(2793, 1128), (2880, 1128), (2880, 1389), (2793, 1389)], None, None, False), ('people', [(1527, 1178), (1590, 1178), (1590, 1400), (1527, 1400)], None, None, False), ('people', [(1470, 1176), (1578, 1176), (1578, 1454), (1470, 1454)], None, None, False), ('people', [(1414, 1178), (1487, 1178), (1487, 1415), (1414, 1415)], None, None, False), ('people', [(1374, 1174), (1403, 1174), (1403, 1229), (1374, 1229)], None, None, False), ('people', [(1322, 1171), (1360, 1171), (1360, 1273), (1322, 1273)], None, None, False), ('people', [(1257, 1169), (1299, 1169), (1299, 1258), (1257, 1258)], None, None, False), ('people', [(907, 1182), (979, 1182), (979, 1434), (907, 1434)], None, None, False), ('people', [(276, 1201), (318, 1201), (318, 1525), (276, 1525)], None, None, False)]
[('people', [(3664, 1139), (3759, 1139), (3759, 1295), (3664, 1295)], None, None, False), ('people', [(3329, 972), (3785, 972), (3785, 2160), (3329, 2160)], None, None, False), ('people', [(3233, 1155), (3309, 1155), (3309, 1418), (3233, 1418)], None, None, False), ('people', [(3147, 1138), (3242, 1138), (3242, 1413), (3147, 1413)], None, None, False), ('people', [(2762, 1158), (2863, 1158), (2863, 1419), (2762, 1419)], None, None, False), ('people', [(2790, 1127), (2885, 1127), (2885, 1374), (2790, 1374)], None, None, False), ('people', [(2696, 1173), (2754, 1173), (2754, 1405), (2696, 1405)], None, None, False), ('people', [(2663, 1142), (2720, 1142), (2720, 1406), (2663, 1406)], None, None, False), ('people', [(2631, 1155), (2702, 1155), (2702, 1426), (2631, 1426)], None, None, False), ('people', [(2592, 1165), (2649, 1165), (2649, 1407), (2592, 1407)], None, None, False), ('people', [(2589, 1132), (2631, 1132), (2631, 1168), (2589, 1168)], None, None, False), ('people', [(2537, 1138), (2622, 1138), (2622, 1425), (2537, 1425)], None, None, False), ('people', [(2509, 1176), (2552, 1176), (2552, 1396), (2509, 1396)], None, None, False), ('people', [(2429, 1146), (2502, 1146), (2502, 1365), (2429, 1365)], None, None, False), ('people', [(2367, 1146), (2420, 1146), (2420, 1335), (2367, 1335)], None, None, False), ('people', [(2343, 1183), (2406, 1183), (2406, 1382), (2343, 1382)], None, None, False), ('people', [(2242, 1168), (2346, 1168), (2346, 1427), (2242, 1427)], None, None, False), ('people', [(1544, 1183), (1604, 1183), (1604, 1409), (1544, 1409)], None, None, False), ('people', [(1680, 1156), (1722, 1156), (1722, 1213), (1680, 1213)], None, None, False), ('people', [(1484, 1188), (1552, 1188), (1552, 1416), (1484, 1416)], None, None, False), ('people', [(1364, 1169), (1453, 1169), (1453, 1409), (1364, 1409)], None, None, False), ('people', [(1434, 1175), (1486, 1175), (1486, 1406), (1434, 1406)], None, None, False), ('people', [(903, 1180), (986, 1180), (986, 1447), (903, 1447)], None, None, False), ('people', [(266, 1195), (319, 1195), (319, 1516), (266, 1516)], None, None, False), ('people', [(1309, 1177), (1342, 1177), (1342, 1287), (1309, 1287)], None, None, False), ('people', [(1277, 1162), (1311, 1162), (1311, 1267), (1277, 1267)], None, None, False), ('people', [(1242, 1170), (1273, 1170), (1273, 1269), (1242, 1269)], None, None, False)]
[('people', [(3664, 1139), (3759, 1139), (3759, 1295), (3664, 1295)], None, None, False), ('people', [(3329, 972), (3785, 972), (3785, 2160), (3329, 2160)], None, None, False), ('people', [(3233, 1155), (3309, 1155), (3309, 1418), (3233, 1418)], None, None, False), ('people', [(3147, 1138), (3242, 1138), (3242, 1413), (3147, 1413)], None, None, False), ('people', [(2762, 1158), (2863, 1158), (2863, 1419), (2762, 1419)], None, None, False), ('people', [(2790, 1127), (2885, 1127), (2885, 1374), (2790, 1374)], None, None, False), ('people', [(2696, 1173), (2754, 1173), (2754, 1405), (2696, 1405)], None, None, False), ('people', [(2663, 1142), (2720, 1142), (2720, 1406), (2663, 1406)], None, None, False), ('people', [(2631, 1155), (2702, 1155), (2702, 1426), (2631, 1426)], None, None, False), ('people', [(2592, 1165), (2649, 1165), (2649, 1407), (2592, 1407)], None, None, False), ('people', [(2589, 1132), (2631, 1132), (2631, 1168), (2589, 1168)], None, None, False), ('people', [(2537, 1138), (2622, 1138), (2622, 1425), (2537, 1425)], None, None, False), ('people', [(2509, 1176), (2552, 1176), (2552, 1396), (2509, 1396)], None, None, False), ('people', [(2429, 1146), (2502, 1146), (2502, 1365), (2429, 1365)], None, None, False), ('people', [(2367, 1146), (2420, 1146), (2420, 1335), (2367, 1335)], None, None, False), ('people', [(2343, 1183), (2406, 1183), (2406, 1382), (2343, 1382)], None, None, False), ('people', [(2242, 1168), (2346, 1168), (2346, 1427), (2242, 1427)], None, None, False), ('people', [(1544, 1183), (1604, 1183), (1604, 1409), (1544, 1409)], None, None, False), ('people', [(1680, 1156), (1722, 1156), (1722, 1213), (1680, 1213)], None, None, False), ('people', [(1484, 1188), (1552, 1188), (1552, 1416), (1484, 1416)], None, None, False), ('people', [(1364, 1169), (1453, 1169), (1453, 1409), (1364, 1409)], None, None, False), ('people', [(1434, 1175), (1486, 1175), (1486, 1406), (1434, 1406)], None, None, False), ('people', [(903, 1180), (986, 1180), (986, 1447), (903, 1447)], None, None, False), ('people', [(266, 1195), (319, 1195), (319, 1516), (266, 1516)], None, None, False), ('people', [(1309, 1177), (1342, 1177), (1342, 1287), (1309, 1287)], None, None, False), ('people', [(1277, 1162), (1311, 1162), (1311, 1267), (1277, 1267)], None, None, False), ('people', [(1242, 1170), (1273, 1170), (1273, 1269), (1242, 1269)], None, None, False)]
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00023.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00023.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00024.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00024.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00025.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00025.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00026.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00026.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00027.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00027.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00028.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00028.txt
Image:D:\lkr_yolo\yolov11_vehicle_counter\dataset\images\frame_00029.jpg -> Annotation:D:/lkr_yolo/yolov11_vehicle_counter/dataset/images/frame_00029.txt
[('people', [(3670, 1140), (3782, 1140), (3782, 1447), (3670, 1447)], None, None, False), ('people', [(3256, 1154), (3337, 1154), (3337, 1453), (3256, 1453)], None, None, False), ('people', [(3145, 1143), (3247, 1143), (3247, 1452), (3145, 1452)], None, None, False), ('people', [(3012, 1154), (3096, 1154), (3096, 1447), (3012, 1447)], None, None, False), ('people', [(122, 1171), (211, 1171), (211, 1519), (122, 1519)], None, None, False), ('people', [(5, 1205), (106, 1205), (106, 1482), (5, 1482)], None, None, False), ('car', [(470, 561), (3051, 561), (3051, 1556), (470, 1556)], None, None, False)]
[('people', [(203, 1172), (282, 1172), (282, 1376), (203, 1376)], None, None, False), ('car', [(1445, 533), (3840, 533), (3840, 1562), (1445, 1562)], None, None, False)]
[('car', [(2402, 488), (3840, 488), (3840, 1574), (2402, 1574)], None, None, False), ('people', [(2259, 1173), (2378, 1173), (2378, 1436), (2259, 1436)], None, None, False), ('people', [(1527, 1186), (1587, 1186), (1587, 1407), (1527, 1407)], None, None, False), ('people', [(1469, 1188), (1529, 1188), (1529, 1419), (1469, 1419)], None, None, False), ('people', [(1409, 1183), (1473, 1183), (1473, 1415), (1409, 1415)], None, None, False), ('people', [(231, 1172), (299, 1172), (299, 1516), (231, 1516)], None, None, False), ('people', [(1958, 1158), (2010, 1158), (2010, 1400), (1958, 1400)], None, None, False)]
[('car', [(3143, 468), (3840, 468), (3840, 1526), (3143, 1526)], None, None, False), ('people', [(2788, 1170), (2841, 1170), (2841, 1407), (2788, 1407)], None, None, False), ('people', [(2774, 1124), (2840, 1124), (2840, 1372), (2774, 1372)], None, None, False), ('people', [(2724, 1141), (2771, 1141), (2771, 1351), (2724, 1351)], None, None, False), ('people', [(2700, 1178), (2765, 1178), (2765, 1405), (2700, 1405)], None, None, False), ('people', [(2643, 1124), (2685, 1124), (2685, 1278), (2643, 1278)], None, None, False), ('people', [(2656, 1166), (2709, 1166), (2709, 1396), (2656, 1396)], None, None, False), ('people', [(2584, 1130), (2653, 1130), (2653, 1371), (2584, 1371)], None, None, False), ('people', [(2503, 1158), (2573, 1158), (2573, 1391), (2503, 1391)], None, None, False), ('people', [(2329, 1171), (2411, 1171), (2411, 1421), (2329, 1421)], None, None, False), ('people', [(1935, 1168), (2007, 1168), (2007, 1385), (1935, 1385)], None, None, False), ('people', [(1533, 1180), (1591, 1180), (1591, 1408), (1533, 1408)], None, None, False), ('people', [(1481, 1186), (1533, 1186), (1533, 1416), (1481, 1416)], None, None, False), ('people', [(1415, 1182), (1487, 1182), (1487, 1410), (1415, 1410)], None, None, False), ('people', [(258, 1170), (315, 1170), (315, 1523), (258, 1523)], None, None, False), ('car', [(2, 1340), (156, 1340), (156, 1569), (2, 1569)], None, None, False)]
[('people', [(3650, 1141), (3769, 1141), (3769, 1453), (3650, 1453)], None, None, False), ('people', [(3281, 1168), (3374, 1168), (3374, 1467), (3281, 1467)], None, None, False), ('people', [(3167, 1145), (3290, 1145), (3290, 1440), (3167, 1440)], None, None, False), ('people', [(2774, 1164), (2843, 1164), (2843, 1403), (2774, 1403)], None, None, False), ('people', [(2756, 1127), (2853, 1127), (2853, 1374), (2756, 1374)], None, None, False), ('people', [(2687, 1133), (2751, 1133), (2751, 1351), (2687, 1351)], None, None, False), ('people', [(2674, 1168), (2731, 1168), (2731, 1393), (2674, 1393)], None, None, False), ('people', [(2626, 1173), (2699, 1173), (2699, 1402), (2626, 1402)], None, None, False), ('people', [(2584, 1132), (2645, 1132), (2645, 1364), (2584, 1364)], None, None, False), ('people', [(2627, 1129), (2677, 1129), (2677, 1175), (2627, 1175)], None, None, False), ('people', [(2500, 1155), (2584, 1155), (2584, 1389), (2500, 1389)], None, None, False), ('people', [(2299, 1173), (2373, 1173), (2373, 1435), (2299, 1435)], None, None, False), ('people', [(2235, 1157), (2292, 1157), (2292, 1416), (2235, 1416)], None, None, False), ('people', [(1987, 1179), (2025, 1179), (2025, 1357), (1987, 1357)], None, None, False), ('people', [(1937, 1173), (2000, 1173), (2000, 1364), (1937, 1364)], None, None, False), ('people', [(1681, 1166), (1741, 1166), (1741, 1391), (1681, 1391)], None, None, False), ('people', [(1583, 1163), (1675, 1163), (1675, 1393), (1583, 1393)], None, None, False), ('people', [(1592, 1173), (1620, 1173), (1620, 1217), (1592, 1217)], None, None, False), ('people', [(1534, 1182), (1587, 1182), (1587, 1406), (1534, 1406)], None, None, False), ('people', [(1491, 1197), (1552, 1197), (1552, 1410), (1491, 1410)], None, None, False), ('people', [(1411, 1180), (1491, 1180), (1491, 1414), (1411, 1414)], None, None, False), ('people', [(937, 1204), (980, 1204), (980, 1317), (937, 1317)], None, None, False), ('people', [(929, 1186), (988, 1186), (988, 1293), (929, 1293)], None, None, False), ('car', [(1, 1217), (1052, 1217), (1052, 1581), (1, 1581)], None, None, False)]
[('people', [(3654, 1146), (3774, 1146), (3774, 1471), (3654, 1471)], None, None, False), ('people', [(3311, 1163), (3417, 1163), (3417, 1463), (3311, 1463)], None, None, False), ('people', [(3214, 1149), (3316, 1149), (3316, 1439), (3214, 1439)], None, None, False), ('people', [(2773, 1167), (2847, 1167), (2847, 1400), (2773, 1400)], None, None, False), ('people', [(2755, 1125), (2853, 1125), (2853, 1373), (2755, 1373)], None, None, False), ('people', [(2708, 1133), (2763, 1133), (2763, 1364), (2708, 1364)], None, None, False), ('people', [(2676, 1163), (2749, 1163), (2749, 1397), (2676, 1397)], None, None, False), ('people', [(2607, 1174), (2690, 1174), (2690, 1402), (2607, 1402)], None, None, False), ('people', [(2648, 1160), (2690, 1160), (2690, 1371), (2648, 1371)], None, None, False), ('people', [(2644, 1122), (2692, 1122), (2692, 1173), (2644, 1173)], None, None, False), ('people', [(2579, 1127), (2654, 1127), (2654, 1370), (2579, 1370)], None, None, False), ('people', [(2500, 1151), (2582, 1151), (2582, 1395), (2500, 1395)], None, None, False), ('people', [(2312, 1161), (2382, 1161), (2382, 1413), (2312, 1413)], None, None, False), ('people', [(2253, 1172), (2327, 1172), (2327, 1437), (2253, 1437)], None, None, False), ('people', [(1942, 1175), (2018, 1175), (2018, 1359), (1942, 1359)], None, None, False), ('car', [(878, 1196), (1877, 1196), (1877, 1555), (878, 1555)], None, None, False), ('people', [(1656, 1177), (1711, 1177), (1711, 1316), (1656, 1316)], None, None, False), ('people', [(1636, 1175), (1662, 1175), (1662, 1212), (1636, 1212)], None, None, False), ('people', [(1589, 1163), (1646, 1163), (1646, 1300), (1589, 1300)], None, None, False), ('people', [(1527, 1186), (1594, 1186), (1594, 1262), (1527, 1262)], None, None, False), ('people', [(1487, 1164), (1537, 1164), (1537, 1239), (1487, 1239)], None, None, False), ('people', [(1425, 1177), (1477, 1177), (1477, 1219), (1425, 1219)], None, None, False), ('people', [(244, 1189), (322, 1189), (322, 1522), (244, 1522)], None, None, False), ('people', [(1, 1185), (62, 1185), (62, 1448), (1, 1448)], None, None, False)]
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3654, 1146), (3774, 1146), (3774, 1471), (3654, 1471)], None, None, False), ('people', [(3311, 1163), (3417, 1163), (3417, 1463), (3311, 1463)], None, None, False), ('people', [(3214, 1149), (3316, 1149), (3316, 1439), (3214, 1439)], None, None, False), ('people', [(2773, 1167), (2847, 1167), (2847, 1400), (2773, 1400)], None, None, False), ('people', [(2755, 1125), (2853, 1125), (2853, 1373), (2755, 1373)], None, None, False), ('people', [(2708, 1133), (2763, 1133), (2763, 1364), (2708, 1364)], None, None, False), ('people', [(2676, 1163), (2749, 1163), (2749, 1397), (2676, 1397)], None, None, False), ('people', [(2607, 1174), (2690, 1174), (2690, 1402), (2607, 1402)], None, None, False), ('people', [(2648, 1160), (2690, 1160), (2690, 1371), (2648, 1371)], None, None, False), ('people', [(2644, 1122), (2692, 1122), (2692, 1173), (2644, 1173)], None, None, False), ('people', [(2579, 1127), (2654, 1127), (2654, 1370), (2579, 1370)], None, None, False), ('people', [(2500, 1151), (2582, 1151), (2582, 1395), (2500, 1395)], None, None, False), ('people', [(2312, 1161), (2382, 1161), (2382, 1413), (2312, 1413)], None, None, False), ('people', [(2253, 1172), (2327, 1172), (2327, 1437), (2253, 1437)], None, None, False), ('people', [(1942, 1175), (2018, 1175), (2018, 1359), (1942, 1359)], None, None, False), ('car', [(878, 1196), (1877, 1196), (1877, 1555), (878, 1555)], None, None, False), ('people', [(1656, 1177), (1711, 1177), (1711, 1316), (1656, 1316)], None, None, False), ('people', [(1636, 1175), (1662, 1175), (1662, 1212), (1636, 1212)], None, None, False), ('people', [(1589, 1163), (1646, 1163), (1646, 1300), (1589, 1300)], None, None, False), ('people', [(1527, 1186), (1594, 1186), (1594, 1262), (1527, 1262)], None, None, False), ('people', [(1487, 1164), (1537, 1164), (1537, 1239), (1487, 1239)], None, None, False), ('people', [(1425, 1177), (1477, 1177), (1477, 1219), (1425, 1219)], None, None, False), ('people', [(244, 1189), (322, 1189), (322, 1522), (244, 1522)], None, None, False), ('people', [(1, 1185), (62, 1185), (62, 1448), (1, 1448)], None, None, False)]
[('people', [(3644, 1150), (3777, 1150), (3777, 1460), (3644, 1460)], None, None, False), ('people', [(3373, 1155), (3497, 1155), (3497, 1457), (3373, 1457)], None, None, False), ('people', [(3258, 1141), (3370, 1141), (3370, 1427), (3258, 1427)], None, None, False), ('people', [(2769, 1166), (2850, 1166), (2850, 1402), (2769, 1402)], None, None, False), ('people', [(2772, 1122), (2860, 1122), (2860, 1367), (2772, 1367)], None, None, False), ('people', [(2678, 1121), (2752, 1121), (2752, 1362), (2678, 1362)], None, None, False), ('people', [(2658, 1162), (2748, 1162), (2748, 1407), (2658, 1407)], None, None, False), ('people', [(2577, 1131), (2671, 1131), (2671, 1365), (2577, 1365)], None, None, False), ('people', [(2571, 1180), (2640, 1180), (2640, 1341), (2571, 1341)], None, None, False), ('people', [(2506, 1153), (2572, 1153), (2572, 1317), (2506, 1317)], None, None, False), ('people', [(1691, 1190), (2705, 1190), (2705, 1547), (1691, 1547)], None, None, False), ('people', [(1552, 1170), (1621, 1170), (1621, 1394), (1552, 1394)], None, None, False), ('people', [(1486, 1186), (1543, 1186), (1543, 1416), (1486, 1416)], None, None, False), ('people', [(1530, 1187), (1559, 1187), (1559, 1239), (1530, 1239)], None, None, False), ('people', [(1417, 1183), (1483, 1183), (1483, 1410), (1417, 1410)], None, None, False), ('people', [(1385, 1170), (1442, 1170), (1442, 1338), (1385, 1338)], None, None, False), ('people', [(1269, 1163), (1296, 1163), (1296, 1263), (1269, 1263)], None, None, False), ('people', [(911, 1194), (983, 1194), (983, 1413), (911, 1413)], None, None, False), ('people', [(1, 1262), (606, 1262), (606, 1592), (1, 1592)], None, None, False), ('people', [(47, 1178), (109, 1178), (109, 1260), (47, 1260)], None, None, False), ('people', [(2, 1182), (52, 1182), (52, 1263), (2, 1263)], None, None, False)]
[('people', [(3644, 1143), (3774, 1143), (3774, 1466), (3644, 1466)], None, None, False), ('people', [(3398, 1155), (3504, 1155), (3504, 1386), (3398, 1386)], None, None, False), ('people', [(3282, 1142), (3410, 1142), (3410, 1326), (3282, 1326)], None, None, False), ('people', [(2773, 1130), (2871, 1130), (2871, 1183), (2773, 1183)], None, None, False), ('people', [(2702, 1130), (2757, 1130), (2757, 1183), (2702, 1183)], None, None, False), ('people', [(2582, 1135), (2669, 1135), (2669, 1189), (2582, 1189)], None, None, False), ('people', [(2477, 1150), (2542, 1150), (2542, 1203), (2477, 1203)], None, None, False), ('people', [(2542, 1157), (2572, 1157), (2572, 1187), (2542, 1187)], None, None, False), ('people', [(2204, 1179), (2307, 1179), (2307, 1449), (2204, 1449)], None, None, False), ('people', [(1953, 1174), (2008, 1174), (2008, 1347), (1953, 1347)], None, None, False), ('car', [(2443, 1177), (3489, 1177), (3489, 1543), (2443, 1543)], None, None, False), ('people', [(1527, 1187), (1602, 1187), (1602, 1399), (1527, 1399)], None, None, False), ('people', [(1483, 1192), (1544, 1192), (1544, 1410), (1483, 1410)], None, None, False), ('people', [(1413, 1183), (1477, 1183), (1477, 1364), (1413, 1364)], None, None, False), ('people', [(1354, 1182), (1417, 1182), (1417, 1356), (1354, 1356)], None, None, False), ('people', [(1310, 1175), (1364, 1175), (1364, 1341), (1310, 1341)], None, None, False), ('people', [(1277, 1164), (1313, 1164), (1313, 1273), (1277, 1273)], None, None, False), ('people', [(1233, 1163), (1267, 1163), (1267, 1269), (1233, 1269)], None, None, False), ('car', [(505, 1242), (1535, 1242), (1535, 1591), (505, 1591)], None, None, False), ('car', [(1, 1181), (59, 1181), (59, 1452), (1, 1452)], None, None, False), ('people', [(279, 1204), (318, 1204), (318, 1522), (279, 1522)], None, None, False)]
[('car', [(3114, 1171), (3840, 1171), (3840, 1541), (3114, 1541)], None, None, False), ('people', [(3687, 1143), (3737, 1143), (3737, 1189), (3687, 1189)], None, None, False), ('people', [(2848, 1130), (2901, 1130), (2901, 1262), (2848, 1262)], None, None, False), ('people', [(2777, 1127), (2853, 1127), (2853, 1195), (2777, 1195)], None, None, False), ('people', [(2776, 1171), (2864, 1171), (2864, 1402), (2776, 1402)], None, None, False), ('people', [(2721, 1151), (2789, 1151), (2789, 1393), (2721, 1393)], None, None, False), ('people', [(2663, 1159), (2726, 1159), (2726, 1401), (2663, 1401)], None, None, False), ('people', [(2598, 1128), (2665, 1128), (2665, 1198), (2598, 1198)], None, None, False), ('people', [(2583, 1162), (2655, 1162), (2655, 1411), (2583, 1411)], None, None, False), ('people', [(2539, 1154), (2587, 1154), (2587, 1361), (2539, 1361)], None, None, False), ('people', [(2504, 1175), (2566, 1175), (2566, 1401), (2504, 1401)], None, None, False), ('people', [(2353, 1317), (2478, 1317), (2478, 1440), (2353, 1440)], None, None, False), ('people', [(2235, 1174), (2316, 1174), (2316, 1365), (2235, 1365)], None, None, False), ('people', [(1228, 1223), (2343, 1223), (2343, 1568), (1228, 1568)], None, None, False), ('people', [(1362, 1186), (1408, 1186), (1408, 1285), (1362, 1285)], None, None, False), ('people', [(1525, 1185), (1586, 1185), (1586, 1229), (1525, 1229)], None, None, False), ('people', [(1472, 1187), (1523, 1187), (1523, 1249), (1472, 1249)], None, None, False), ('people', [(1416, 1179), (1469, 1179), (1469, 1268), (1416, 1268)], None, None, False), ('people', [(1241, 1168), (1319, 1168), (1319, 1323), (1241, 1323)], None, None, False), ('people', [(3, 1179), (112, 1179), (112, 1444), (3, 1444)], None, None, False), ('people', [(275, 1210), (319, 1210), (319, 1523), (275, 1523)], None, None, False), ('people', [(915, 1213), (981, 1213), (981, 1421), (915, 1421)], None, None, False), ('people', [(3316, 1146), (3365, 1146), (3365, 1186), (3316, 1186)], None, None, False)]
[('car', [(3758, 1193), (3840, 1193), (3840, 1472), (3758, 1472)], None, None, False), ('people', [(3645, 1135), (3763, 1135), (3763, 1462), (3645, 1462)], None, None, False), ('people', [(3398, 1148), (3518, 1148), (3518, 1426), (3398, 1426)], None, None, False), ('people', [(3237, 1151), (3349, 1151), (3349, 1403), (3237, 1403)], None, None, False), ('people', [(2782, 1174), (2869, 1174), (2869, 1266), (2782, 1266)], None, None, False), ('people', [(2770, 1131), (2861, 1131), (2861, 1192), (2770, 1192)], None, None, False), ('people', [(2739, 1126), (2796, 1126), (2796, 1194), (2739, 1194)], None, None, False), ('people', [(2698, 1144), (2764, 1144), (2764, 1316), (2698, 1316)], None, None, False), ('people', [(2639, 1158), (2704, 1158), (2704, 1302), (2639, 1302)], None, None, False), ('people', [(2584, 1166), (2639, 1166), (2639, 1255), (2584, 1255)], None, None, False), ('people', [(2603, 1128), (2645, 1128), (2645, 1169), (2603, 1169)], None, None, False), ('people', [(2541, 1152), (2583, 1152), (2583, 1240), (2541, 1240)], None, None, False), ('people', [(2508, 1174), (2567, 1174), (2567, 1235), (2508, 1235)], None, None, False), ('car', [(1933, 1223), (3079, 1223), (3079, 1564), (1933, 1564)], None, None, False), ('people', [(1952, 1171), (2010, 1171), (2010, 1317), (1952, 1317)], None, None, False), ('people', [(1529, 1182), (1602, 1182), (1602, 1405), (1529, 1405)], None, None, False), ('people', [(1483, 1194), (1548, 1194), (1548, 1416), (1483, 1416)], None, None, False), ('people', [(1414, 1188), (1488, 1188), (1488, 1414), (1414, 1414)], None, None, False), ('people', [(1365, 1181), (1418, 1181), (1418, 1363), (1365, 1363)], None, None, False), ('people', [(1316, 1181), (1374, 1181), (1374, 1368), (1316, 1368)], None, None, False), ('people', [(1243, 1166), (1307, 1166), (1307, 1369), (1243, 1369)], None, None, False), ('car', [(1, 1087), (316, 1087), (316, 1600), (1, 1600)], None, None, False), ('people', [(935, 1191), (988, 1191), (988, 1310), (935, 1310)], None, None, False)]
[('people', [(3644, 1135), (3764, 1135), (3764, 1372), (3644, 1372)], None, None, False), ('people', [(3374, 1152), (3451, 1152), (3451, 1328), (3374, 1328)], None, None, False), ('people', [(3190, 1143), (3272, 1143), (3272, 1254), (3190, 1254)], None, None, False), ('people', [(2799, 1167), (2854, 1167), (2854, 1233), (2799, 1233)], None, None, False), ('people', [(2768, 1123), (2863, 1123), (2863, 1236), (2768, 1236)], None, None, False), ('people', [(2665, 1154), (2742, 1154), (2742, 1273), (2665, 1273)], None, None, False), ('people', [(2642, 1136), (2736, 1136), (2736, 1210), (2642, 1210)], None, None, False), ('people', [(2597, 1131), (2660, 1131), (2660, 1191), (2597, 1191)], None, None, False), ('people', [(2581, 1163), (2640, 1163), (2640, 1306), (2581, 1306)], None, None, False), ('people', [(2538, 1154), (2575, 1154), (2575, 1198), (2538, 1198)], None, None, False), ('people', [(2506, 1175), (2573, 1175), (2573, 1400), (2506, 1400)], None, None, False), ('people', [(2356, 1320), (2475, 1320), (2475, 1444), (2356, 1444)], None, None, False), ('people', [(2227, 1176), (2296, 1176), (2296, 1433), (2227, 1433)], None, None, False), ('people', [(1955, 1178), (2008, 1178), (2008, 1348), (1955, 1348)], None, None, False), ('people', [(1528, 1181), (1593, 1181), (1593, 1407), (1528, 1407)], None, None, False), ('people', [(1471, 1193), (1538, 1193), (1538, 1412), (1471, 1412)], None, None, False), ('people', [(1415, 1182), (1486, 1182), (1486, 1412), (1415, 1412)], None, None, False), ('people', [(1332, 1184), (1386, 1184), (1386, 1354), (1332, 1354)], None, None, False), ('people', [(1286, 1183), (1335, 1183), (1335, 1363), (1286, 1363)], None, None, False), ('people', [(1285, 1165), (1313, 1165), (1313, 1279), (1285, 1279)], None, None, False), ('car', [(1, 1014), (1204, 1014), (1204, 1598), (1, 1598)], None, None, False)]
[('people', [(3671, 1133), (3766, 1133), (3766, 1222), (3671, 1222)], None, None, False), ('people', [(3300, 1154), (3357, 1154), (3357, 1238), (3300, 1238)], None, None, False), ('people', [(3114, 1143), (3244, 1143), (3244, 1301), (3114, 1301)], None, None, False), ('car', [(3051, 1201), (3840, 1201), (3840, 1563), (3051, 1563)], None, None, False), ('people', [(2779, 1167), (2873, 1167), (2873, 1398), (2779, 1398)], None, None, False), ('people', [(2770, 1125), (2859, 1125), (2859, 1201), (2770, 1201)], None, None, False), ('people', [(2652, 1151), (2726, 1151), (2726, 1414), (2652, 1414)], None, None, False), ('people', [(2615, 1127), (2663, 1127), (2663, 1192), (2615, 1192)], None, None, False), ('people', [(2575, 1149), (2650, 1149), (2650, 1413), (2575, 1413)], None, None, False), ('people', [(2537, 1147), (2577, 1147), (2577, 1196), (2537, 1196)], None, None, False), ('people', [(2507, 1174), (2578, 1174), (2578, 1407), (2507, 1407)], None, None, False), ('people', [(2353, 1318), (2480, 1318), (2480, 1439), (2353, 1439)], None, None, False), ('people', [(2231, 1167), (2295, 1167), (2295, 1435), (2231, 1435)], None, None, False), ('people', [(1956, 1176), (2009, 1176), (2009, 1349), (1956, 1349)], None, None, False), ('car', [(654, 994), (1955, 994), (1955, 1582), (654, 1582)], None, None, False), ('people', [(30, 1166), (141, 1166), (141, 1486), (30, 1486)], None, None, False)]
[('people', [(3611, 1142), (3758, 1142), (3758, 1295), (3611, 1295)], None, None, False), ('car', [(3509, 1188), (3840, 1188), (3840, 1543), (3509, 1543)], None, None, False), ('people', [(3247, 1161), (3330, 1161), (3330, 1414), (3247, 1414)], None, None, False), ('people', [(3149, 1146), (3244, 1146), (3244, 1406), (3149, 1406)], None, None, False), ('people', [(2786, 1169), (2866, 1169), (2866, 1403), (2786, 1403)], None, None, False), ('people', [(2774, 1123), (2858, 1123), (2858, 1370), (2774, 1370)], None, None, False), ('people', [(2645, 1159), (2725, 1159), (2725, 1414), (2645, 1414)], None, None, False), ('people', [(2619, 1134), (2649, 1134), (2649, 1192), (2619, 1192)], None, None, False), ('people', [(2591, 1163), (2638, 1163), (2638, 1269), (2591, 1269)], None, None, False), ('people', [(2514, 1143), (2590, 1143), (2590, 1267), (2514, 1267)], None, None, False), ('car', [(1388, 985), (2678, 985), (2678, 1569), (1388, 1569)], None, None, False), ('people', [(212, 1192), (289, 1192), (289, 1391), (212, 1391)], None, None, False), ('people', [(279, 1212), (320, 1212), (320, 1525), (279, 1525)], None, None, False), ('people', [(1, 1164), (124, 1164), (124, 1460), (1, 1460)], None, None, False), ('people', [(1252, 1183), (1304, 1183), (1304, 1356), (1252, 1356)], None, None, False), ('people', [(1344, 1195), (1398, 1195), (1398, 1428), (1344, 1428)], None, None, False)]
[('people', [(3476, 1183), (3840, 1183), (3840, 2160), (3476, 2160)], None, None, False), ('people', [(3258, 1158), (3331, 1158), (3331, 1413), (3258, 1413)], None, None, False), ('people', [(3157, 1143), (3247, 1143), (3247, 1416), (3157, 1416)], None, None, False), ('car', [(1896, 980), (3199, 980), (3199, 1569), (1896, 1569)], None, None, False), ('people', [(1518, 1189), (1582, 1189), (1582, 1414), (1518, 1414)], None, None, False), ('people', [(1462, 1196), (1556, 1196), (1556, 1425), (1462, 1425)], None, None, False), ('people', [(1413, 1176), (1469, 1176), (1469, 1415), (1413, 1415)], None, None, False), ('people', [(1240, 1178), (1297, 1178), (1297, 1336), (1240, 1336)], None, None, False), ('people', [(1330, 1183), (1366, 1183), (1366, 1343), (1330, 1343)], None, None, False), ('people', [(1596, 1172), (1649, 1172), (1649, 1218), (1596, 1218)], None, None, False), ('people', [(285, 1207), (322, 1207), (322, 1522), (285, 1522)], None, None, False), ('people', [(206, 1183), (294, 1183), (294, 1379), (206, 1379)], None, None, False), ('people', [(1, 1161), (139, 1161), (139, 1464), (1, 1464)], None, None, False)]
[('people', [(1291, 1072), (2029, 1072), (2029, 2160), (1291, 2160)], None, None, False), ('people', [(3634, 1126), (3744, 1126), (3744, 1453), (3634, 1453)], None, None, False), ('people', [(3711, 1159), (3792, 1159), (3792, 1439), (3711, 1439)], None, None, False), ('car', [(2276, 968), (3622, 968), (3622, 1588), (2276, 1588)], None, None, False), ('people', [(2, 1143), (124, 1143), (124, 1468), (2, 1468)], None, None, False), ('people', [(282, 1208), (320, 1208), (320, 1525), (282, 1525)], None, None, False), ('people', [(1600, 1189), (1671, 1189), (1671, 1426), (1600, 1426)], None, None, False), ('people', [(1280, 1183), (1307, 1183), (1307, 1326), (1280, 1326)], None, None, False), ('people', [(1959, 1179), (2004, 1179), (2004, 1348), (1959, 1348)], None, None, False)]
[('people', [(3683, 1088), (3840, 1088), (3840, 1560), (3683, 1560)], None, None, False), ('car', [(2594, 966), (3840, 966), (3840, 1571), (2594, 1571)], None, None, False), ('people', [(2532, 1135), (2603, 1135), (2603, 1427), (2532, 1427)], None, None, False), ('people', [(2359, 1316), (2482, 1316), (2482, 1462), (2359, 1462)], None, None, False), ('people', [(2229, 1175), (2296, 1175), (2296, 1439), (2229, 1439)], None, None, False), ('people', [(1956, 1180), (2006, 1180), (2006, 1346), (1956, 1346)], None, None, False), ('people', [(1525, 1186), (1589, 1186), (1589, 1413), (1525, 1413)], None, None, False), ('people', [(1483, 1187), (1534, 1187), (1534, 1419), (1483, 1419)], None, None, False), ('people', [(1407, 1178), (1477, 1178), (1477, 1413), (1407, 1413)], None, None, False), ('people', [(1242, 1178), (1286, 1178), (1286, 1328), (1242, 1328)], None, None, False), ('people', [(2, 1160), (117, 1160), (117, 1474), (2, 1474)], None, None, False), ('people', [(263, 1214), (306, 1214), (306, 1514), (263, 1514)], None, None, False)]
[('car', [(3007, 959), (3840, 959), (3840, 1583), (3007, 1583)], None, None, False), ('car', [(1, 1191), (333, 1191), (333, 1578), (1, 1578)], None, None, False), ('people', [(1242, 1179), (1291, 1179), (1291, 1329), (1242, 1329)], None, None, False), ('people', [(2454, 1120), (2827, 1120), (2827, 1863), (2454, 1863)], None, None, False), ('people', [(2802, 1169), (2875, 1169), (2875, 1383), (2802, 1383)], None, None, False), ('people', [(2780, 1114), (2864, 1114), (2864, 1210), (2780, 1210)], None, None, False), ('people', [(2681, 1150), (2714, 1150), (2714, 1187), (2681, 1187)], None, None, False), ('people', [(2678, 1154), (2760, 1154), (2760, 1216), (2678, 1216)], None, None, False), ('people', [(2628, 1152), (2665, 1152), (2665, 1201), (2628, 1201)], None, None, False), ('people', [(2598, 1122), (2643, 1122), (2643, 1191), (2598, 1191)], None, None, False), ('people', [(2357, 1309), (2459, 1309), (2459, 1446), (2357, 1446)], None, None, False), ('people', [(2257, 1156), (2326, 1156), (2326, 1386), (2257, 1386)], None, None, False), ('people', [(2224, 1173), (2294, 1173), (2294, 1429), (2224, 1429)], None, None, False), ('people', [(1964, 1179), (2016, 1179), (2016, 1350), (1964, 1350)], None, None, False), ('people', [(1936, 1170), (1963, 1170), (1963, 1307), (1936, 1307)], None, None, False), ('people', [(1527, 1192), (1590, 1192), (1590, 1409), (1527, 1409)], None, None, False), ('people', [(1475, 1189), (1544, 1189), (1544, 1414), (1475, 1414)], None, None, False), ('people', [(1421, 1176), (1485, 1176), (1485, 1414), (1421, 1414)], None, None, False), ('people', [(1352, 1169), (1382, 1169), (1382, 1264), (1352, 1264)], None, None, False), ('people', [(917, 1188), (986, 1188), (986, 1443), (917, 1443)], None, None, False)]
[('people', [(3638, 1137), (3759, 1137), (3759, 1455), (3638, 1455)], None, None, False), ('people', [(3240, 1152), (3312, 1152), (3312, 1406), (3240, 1406)], None, None, False), ('people', [(3144, 1146), (3247, 1146), (3247, 1409), (3144, 1409)], None, None, False), ('people', [(3003, 1163), (3124, 1163), (3124, 1434), (3003, 1434)], None, None, False), ('people', [(2786, 1169), (2877, 1169), (2877, 1405), (2786, 1405)], None, None, False), ('people', [(2786, 1118), (2869, 1118), (2869, 1174), (2786, 1174)], None, None, False), ('people', [(2670, 1150), (2742, 1150), (2742, 1395), (2670, 1395)], None, None, False), ('people', [(2735, 1151), (2789, 1151), (2789, 1400), (2735, 1400)], None, None, False), ('people', [(2612, 1160), (2695, 1160), (2695, 1403), (2612, 1403)], None, None, False), ('people', [(2599, 1131), (2668, 1131), (2668, 1195), (2599, 1195)], None, None, False), ('people', [(2544, 1137), (2616, 1137), (2616, 1418), (2544, 1418)], None, None, False), ('people', [(2506, 1171), (2552, 1171), (2552, 1399), (2506, 1399)], None, None, False), ('people', [(2355, 1317), (2475, 1317), (2475, 1450), (2355, 1450)], None, None, False), ('people', [(2276, 1162), (2345, 1162), (2345, 1395), (2276, 1395)], None, None, False), ('people', [(2231, 1173), (2295, 1173), (2295, 1434), (2231, 1434)], None, None, False), ('people', [(1749, 1137), (2042, 1137), (2042, 1704), (1749, 1704)], None, None, False), ('people', [(7, 1151), (161, 1151), (161, 1474), (7, 1474)], None, None, False), ('car', [(157, 1169), (1254, 1169), (1254, 1574), (157, 1574)], None, None, False), ('people', [(1249, 1177), (1290, 1177), (1290, 1322), (1249, 1322)], None, None, False), ('people', [(1357, 1166), (1384, 1166), (1384, 1264), (1357, 1264)], None, None, False), ('people', [(1420, 1178), (1481, 1178), (1481, 1415), (1420, 1415)], None, None, False), ('people', [(1479, 1189), (1537, 1189), (1537, 1414), (1479, 1414)], None, None, False), ('people', [(1530, 1187), (1593, 1187), (1593, 1411), (1530, 1411)], None, None, False), ('people', [(1517, 1156), (1543, 1156), (1543, 1206), (1517, 1206)], None, None, False), ('people', [(1572, 1153), (1608, 1153), (1608, 1222), (1572, 1222)], None, None, False)]
[('people', [(3641, 1135), (3760, 1135), (3760, 1453), (3641, 1453)], None, None, False), ('people', [(3241, 1156), (3312, 1156), (3312, 1405), (3241, 1405)], None, None, False), ('people', [(3141, 1143), (3245, 1143), (3245, 1405), (3141, 1405)], None, None, False), ('people', [(2775, 1179), (2844, 1179), (2844, 1444), (2775, 1444)], None, None, False), ('people', [(2768, 1152), (2856, 1152), (2856, 1210), (2768, 1210)], None, None, False), ('people', [(2797, 1122), (2833, 1122), (2833, 1157), (2797, 1157)], None, None, False), ('people', [(2675, 1152), (2755, 1152), (2755, 1409), (2675, 1409)], None, None, False), ('people', [(2601, 1159), (2674, 1159), (2674, 1401), (2601, 1401)], None, None, False), ('people', [(2588, 1125), (2678, 1125), (2678, 1191), (2588, 1191)], None, None, False), ('people', [(2529, 1141), (2610, 1141), (2610, 1419), (2529, 1419)], None, None, False), ('people', [(2500, 1171), (2552, 1171), (2552, 1400), (2500, 1400)], None, None, False), ('people', [(2358, 1316), (2489, 1316), (2489, 1441), (2358, 1441)], None, None, False), ('people', [(2291, 1156), (2372, 1156), (2372, 1390), (2291, 1390)], None, None, False), ('people', [(2256, 1151), (2296, 1151), (2296, 1393), (2256, 1393)], None, None, False), ('people', [(2199, 1178), (2285, 1178), (2285, 1430), (2199, 1430)], None, None, False), ('people', [(1312, 1153), (1540, 1153), (1540, 1656), (1312, 1656)], None, None, False), ('people', [(1, 1147), (113, 1147), (113, 1459), (1, 1459)], None, None, False), ('people', [(277, 1201), (315, 1201), (315, 1518), (277, 1518)], None, None, False), ('car', [(985, 1163), (1964, 1163), (1964, 1555), (985, 1555)], None, None, False)]
[('people', [(3637, 1136), (3763, 1136), (3763, 1456), (3637, 1456)], None, None, False), ('people', [(1, 1141), (57, 1141), (57, 1465), (1, 1465)], None, None, False), ('people', [(51, 1169), (104, 1169), (104, 1439), (51, 1439)], None, None, False), ('people', [(273, 1201), (325, 1201), (325, 1515), (273, 1515)], None, None, False), ('people', [(1114, 1165), (1273, 1165), (1273, 1541), (1114, 1541)], None, None, False), ('people', [(1352, 1165), (1396, 1165), (1396, 1282), (1352, 1282)], None, None, False), ('people', [(1412, 1183), (1489, 1183), (1489, 1410), (1412, 1410)], None, None, False), ('people', [(1490, 1190), (1545, 1190), (1545, 1414), (1490, 1414)], None, None, False), ('people', [(1530, 1185), (1579, 1185), (1579, 1395), (1530, 1395)], None, None, False), ('people', [(1583, 1162), (1621, 1162), (1621, 1219), (1583, 1219)], None, None, False), ('people', [(1657, 1152), (1695, 1152), (1695, 1216), (1657, 1216)], None, None, False), ('people', [(1943, 1174), (1970, 1174), (1970, 1341), (1943, 1341)], None, None, False), ('people', [(1963, 1174), (2016, 1174), (2016, 1349), (1963, 1349)], None, None, False), ('car', [(2017, 1155), (3083, 1155), (3083, 1550), (2017, 1550)], None, None, False), ('people', [(2604, 1127), (2640, 1127), (2640, 1162), (2604, 1162)], None, None, False), ('people', [(2670, 1154), (2704, 1154), (2704, 1198), (2670, 1198)], None, None, False), ('people', [(2749, 1146), (2791, 1146), (2791, 1205), (2749, 1205)], None, None, False), ('people', [(2799, 1126), (2853, 1126), (2853, 1168), (2799, 1168)], None, None, False), ('people', [(2799, 1162), (2858, 1162), (2858, 1265), (2799, 1265)], None, None, False), ('people', [(3144, 1143), (3247, 1143), (3247, 1418), (3144, 1418)], None, None, False), ('people', [(3241, 1156), (3313, 1156), (3313, 1412), (3241, 1412)], None, None, False)]
[('car', [(3102, 1128), (3840, 1128), (3840, 1541), (3102, 1541)], None, None, False), ('people', [(851, 943), (1209, 943), (1209, 2154), (851, 2154)], None, None, False), ('people', [(1, 1162), (45, 1162), (45, 1459), (1, 1459)], None, None, False), ('people', [(1238, 1160), (1279, 1160), (1279, 1243), (1238, 1243)], None, None, False), ('people', [(1271, 1161), (1398, 1161), (1398, 1432), (1271, 1432)], None, None, False), ('people', [(1413, 1176), (1472, 1176), (1472, 1405), (1413, 1405)], None, None, False), ('people', [(1471, 1196), (1533, 1196), (1533, 1406), (1471, 1406)], None, None, False), ('people', [(1520, 1174), (1589, 1174), (1589, 1420), (1520, 1420)], None, None, False), ('people', [(1622, 1158), (1669, 1158), (1669, 1215), (1622, 1215)], None, None, False), ('people', [(1991, 1177), (2024, 1177), (2024, 1361), (1991, 1361)], None, None, False), ('people', [(2194, 1173), (2279, 1173), (2279, 1423), (2194, 1423)], None, None, False), ('people', [(2279, 1172), (2321, 1172), (2321, 1422), (2279, 1422)], None, None, False), ('people', [(2319, 1152), (2366, 1152), (2366, 1412), (2319, 1412)], None, None, False), ('people', [(2359, 1172), (2411, 1172), (2411, 1381), (2359, 1381)], None, None, False), ('people', [(2428, 1176), (2497, 1176), (2497, 1342), (2428, 1342)], None, None, False), ('people', [(2534, 1138), (2606, 1138), (2606, 1421), (2534, 1421)], None, None, False), ('people', [(2598, 1156), (2650, 1156), (2650, 1403), (2598, 1403)], None, None, False), ('people', [(2621, 1158), (2709, 1158), (2709, 1414), (2621, 1414)], None, None, False), ('people', [(2678, 1143), (2747, 1143), (2747, 1409), (2678, 1409)], None, None, False), ('people', [(2602, 1131), (2637, 1131), (2637, 1158), (2602, 1158)], None, None, False), ('people', [(2739, 1157), (2786, 1157), (2786, 1395), (2739, 1395)], None, None, False), ('people', [(2779, 1125), (2838, 1125), (2838, 1167), (2779, 1167)], None, None, False), ('people', [(2791, 1163), (2875, 1163), (2875, 1417), (2791, 1417)], None, None, False)]
[('people', [(3645, 1139), (3762, 1139), (3762, 1458), (3645, 1458)], None, None, False), ('people', [(3237, 1158), (3314, 1158), (3314, 1408), (3237, 1408)], None, None, False), ('people', [(3142, 1143), (3247, 1143), (3247, 1406), (3142, 1406)], None, None, False), ('people', [(2229, 1172), (2273, 1172), (2273, 1434), (2229, 1434)], None, None, False), ('people', [(2280, 1173), (2342, 1173), (2342, 1271), (2280, 1271)], None, None, False), ('people', [(2229, 1013), (2607, 1013), (2607, 2160), (2229, 2160)], None, None, False), ('people', [(2530, 1132), (2593, 1132), (2593, 1428), (2530, 1428)], None, None, False), ('people', [(2603, 1166), (2640, 1166), (2640, 1408), (2603, 1408)], None, None, False), ('people', [(2596, 1125), (2647, 1125), (2647, 1191), (2596, 1191)], None, None, False), ('people', [(2627, 1143), (2704, 1143), (2704, 1420), (2627, 1420)], None, None, False), ('people', [(2697, 1149), (2742, 1149), (2742, 1415), (2697, 1415)], None, None, False), ('people', [(2732, 1159), (2778, 1159), (2778, 1405), (2732, 1405)], None, None, False), ('people', [(2754, 1159), (2864, 1159), (2864, 1396), (2754, 1396)], None, None, False), ('people', [(2793, 1128), (2880, 1128), (2880, 1389), (2793, 1389)], None, None, False), ('people', [(1527, 1178), (1590, 1178), (1590, 1400), (1527, 1400)], None, None, False), ('people', [(1470, 1176), (1578, 1176), (1578, 1454), (1470, 1454)], None, None, False), ('people', [(1414, 1178), (1487, 1178), (1487, 1415), (1414, 1415)], None, None, False), ('people', [(1374, 1174), (1403, 1174), (1403, 1229), (1374, 1229)], None, None, False), ('people', [(1322, 1171), (1360, 1171), (1360, 1273), (1322, 1273)], None, None, False), ('people', [(1257, 1169), (1299, 1169), (1299, 1258), (1257, 1258)], None, None, False), ('people', [(907, 1182), (979, 1182), (979, 1434), (907, 1434)], None, None, False), ('people', [(276, 1201), (318, 1201), (318, 1525), (276, 1525)], None, None, False)]
[('people', [(3664, 1139), (3759, 1139), (3759, 1295), (3664, 1295)], None, None, False), ('people', [(3329, 972), (3785, 972), (3785, 2160), (3329, 2160)], None, None, False), ('people', [(3233, 1155), (3309, 1155), (3309, 1418), (3233, 1418)], None, None, False), ('people', [(3147, 1138), (3242, 1138), (3242, 1413), (3147, 1413)], None, None, False), ('people', [(2762, 1158), (2863, 1158), (2863, 1419), (2762, 1419)], None, None, False), ('people', [(2790, 1127), (2885, 1127), (2885, 1374), (2790, 1374)], None, None, False), ('people', [(2696, 1173), (2754, 1173), (2754, 1405), (2696, 1405)], None, None, False), ('people', [(2663, 1142), (2720, 1142), (2720, 1406), (2663, 1406)], None, None, False), ('people', [(2631, 1155), (2702, 1155), (2702, 1426), (2631, 1426)], None, None, False), ('people', [(2592, 1165), (2649, 1165), (2649, 1407), (2592, 1407)], None, None, False), ('people', [(2589, 1132), (2631, 1132), (2631, 1168), (2589, 1168)], None, None, False), ('people', [(2537, 1138), (2622, 1138), (2622, 1425), (2537, 1425)], None, None, False), ('people', [(2509, 1176), (2552, 1176), (2552, 1396), (2509, 1396)], None, None, False), ('people', [(2429, 1146), (2502, 1146), (2502, 1365), (2429, 1365)], None, None, False), ('people', [(2367, 1146), (2420, 1146), (2420, 1335), (2367, 1335)], None, None, False), ('people', [(2343, 1183), (2406, 1183), (2406, 1382), (2343, 1382)], None, None, False), ('people', [(2242, 1168), (2346, 1168), (2346, 1427), (2242, 1427)], None, None, False), ('people', [(1544, 1183), (1604, 1183), (1604, 1409), (1544, 1409)], None, None, False), ('people', [(1680, 1156), (1722, 1156), (1722, 1213), (1680, 1213)], None, None, False), ('people', [(1484, 1188), (1552, 1188), (1552, 1416), (1484, 1416)], None, None, False), ('people', [(1364, 1169), (1453, 1169), (1453, 1409), (1364, 1409)], None, None, False), ('people', [(1434, 1175), (1486, 1175), (1486, 1406), (1434, 1406)], None, None, False), ('people', [(903, 1180), (986, 1180), (986, 1447), (903, 1447)], None, None, False), ('people', [(266, 1195), (319, 1195), (319, 1516), (266, 1516)], None, None, False), ('people', [(1309, 1177), (1342, 1177), (1342, 1287), (1309, 1287)], None, None, False), ('people', [(1277, 1162), (1311, 1162), (1311, 1267), (1277, 1267)], None, None, False), ('people', [(1242, 1170), (1273, 1170), (1273, 1269), (1242, 1269)], None, None, False)]
[('people', [(3643, 1140), (3766, 1140), (3766, 1455), (3643, 1455)], None, None, False), ('people', [(3232, 1152), (3311, 1152), (3311, 1408), (3232, 1408)], None, None, False), ('people', [(3140, 1145), (3237, 1145), (3237, 1401), (3140, 1401)], None, None, False), ('people', [(2139, 1189), (2179, 1189), (2179, 1250), (2139, 1250)], None, None, False), ('people', [(2317, 1170), (2373, 1170), (2373, 1439), (2317, 1439)], None, None, False), ('people', [(2371, 1181), (2434, 1181), (2434, 1338), (2371, 1338)], None, None, False), ('people', [(2364, 1313), (2487, 1313), (2487, 1468), (2364, 1468)], None, None, False), ('people', [(2440, 1153), (2514, 1153), (2514, 1219), (2440, 1219)], None, None, False), ('people', [(2447, 1175), (2516, 1175), (2516, 1375), (2447, 1375)], None, None, False), ('people', [(2512, 1161), (2541, 1161), (2541, 1400), (2512, 1400)], None, None, False), ('people', [(2536, 1140), (2600, 1140), (2600, 1428), (2536, 1428)], None, None, False), ('people', [(2594, 1132), (2629, 1132), (2629, 1183), (2594, 1183)], None, None, False), ('people', [(2609, 1145), (2660, 1145), (2660, 1404), (2609, 1404)], None, None, False), ('people', [(2642, 1159), (2730, 1159), (2730, 1436), (2642, 1436)], None, None, False), ('people', [(2760, 1156), (2866, 1156), (2866, 1412), (2760, 1412)], None, None, False), ('people', [(2787, 1127), (2856, 1127), (2856, 1174), (2787, 1174)], None, None, False), ('people', [(78, 1185), (137, 1185), (137, 1463), (78, 1463)], None, None, False), ('people', [(267, 1198), (320, 1198), (320, 1513), (267, 1513)], None, None, False), ('people', [(1271, 1180), (1333, 1180), (1333, 1397), (1271, 1397)], None, None, False), ('people', [(1305, 1165), (1330, 1165), (1330, 1195), (1305, 1195)], None, None, False), ('people', [(1339, 1175), (1378, 1175), (1378, 1285), (1339, 1285)], None, None, False), ('people', [(1420, 1179), (1487, 1179), (1487, 1409), (1420, 1409)], None, None, False), ('people', [(1492, 1184), (1554, 1184), (1554, 1413), (1492, 1413)], None, None, False), ('people', [(1535, 1182), (1596, 1182), (1596, 1408), (1535, 1408)], None, None, False), ('people', [(1941, 1219), (1988, 1219), (1988, 1419), (1941, 1419)], None, None, False)]
[('people', [(3643, 1138), (3760, 1138), (3760, 1455), (3643, 1455)], None, None, False), ('people', [(3234, 1154), (3313, 1154), (3313, 1408), (3234, 1408)], None, None, False), ('people', [(3127, 1147), (3234, 1147), (3234, 1402), (3127, 1402)], None, None, False), ('people', [(2750, 1158), (2836, 1158), (2836, 1413), (2750, 1413)], None, None, False), ('people', [(2797, 1131), (2831, 1131), (2831, 1164), (2797, 1164)], None, None, False), ('people', [(2802, 1162), (2879, 1162), (2879, 1379), (2802, 1379)], None, None, False), ('people', [(2678, 1148), (2764, 1148), (2764, 1434), (2678, 1434)], None, None, False), ('people', [(2564, 1174), (2677, 1174), (2677, 1448), (2564, 1448)], None, None, False), ('people', [(2595, 1130), (2672, 1130), (2672, 1252), (2595, 1252)], None, None, False), ('people', [(2536, 1135), (2605, 1135), (2605, 1426), (2536, 1426)], None, None, False), ('people', [(2516, 1138), (2558, 1138), (2558, 1199), (2516, 1199)], None, None, False), ('people', [(2465, 1152), (2541, 1152), (2541, 1373), (2465, 1373)], None, None, False), ('people', [(2390, 1159), (2471, 1159), (2471, 1354), (2390, 1354)], None, None, False), ('people', [(2354, 1314), (2479, 1314), (2479, 1478), (2354, 1478)], None, None, False), ('people', [(2299, 1169), (2372, 1169), (2372, 1439), (2299, 1439)], None, None, False), ('people', [(2233, 1184), (2286, 1184), (2286, 1391), (2233, 1391)], None, None, False), ('people', [(1937, 1174), (1981, 1174), (1981, 1364), (1937, 1364)], None, None, False), ('people', [(1539, 1183), (1595, 1183), (1595, 1411), (1539, 1411)], None, None, False), ('people', [(1488, 1185), (1545, 1185), (1545, 1419), (1488, 1419)], None, None, False), ('people', [(1417, 1181), (1489, 1181), (1489, 1418), (1417, 1418)], None, None, False), ('people', [(1325, 1172), (1364, 1172), (1364, 1272), (1325, 1272)], None, None, False), ('people', [(1374, 1170), (1405, 1170), (1405, 1268), (1374, 1268)], None, None, False), ('people', [(1251, 1176), (1307, 1176), (1307, 1328), (1251, 1328)], None, None, False), ('people', [(1294, 1172), (1321, 1172), (1321, 1224), (1294, 1224)], None, None, False), ('people', [(903, 1187), (981, 1187), (981, 1439), (903, 1439)], None, None, False), ('people', [(240, 1178), (324, 1178), (324, 1520), (240, 1520)], None, None, False)]
[('people', [(258, 1172), (320, 1172), (320, 1519), (258, 1519)], None, None, False), ('people', [(910, 1189), (982, 1189), (982, 1453), (910, 1453)], None, None, False), ('people', [(1243, 1173), (1284, 1173), (1284, 1352), (1243, 1352)], None, None, False), ('people', [(1293, 1174), (1323, 1174), (1323, 1288), (1293, 1288)], None, None, False), ('people', [(1347, 1166), (1385, 1166), (1385, 1276), (1347, 1276)], None, None, False), ('people', [(1413, 1173), (1476, 1173), (1476, 1409), (1413, 1409)], None, None, False), ('people', [(1491, 1192), (1549, 1192), (1549, 1423), (1491, 1423)], None, None, False), ('people', [(1553, 1193), (1614, 1193), (1614, 1427), (1553, 1427)], None, None, False), ('people', [(1674, 1183), (1737, 1183), (1737, 1420), (1674, 1420)], None, None, False), ('people', [(1938, 1172), (1984, 1172), (1984, 1366), (1938, 1366)], None, None, False), ('people', [(1964, 1146), (2011, 1146), (2011, 1333), (1964, 1333)], None, None, False), ('people', [(2294, 1168), (2353, 1168), (2353, 1435), (2294, 1435)], None, None, False), ('people', [(2356, 1293), (2456, 1293), (2456, 1467), (2356, 1467)], None, None, False), ('people', [(2420, 1148), (2474, 1148), (2474, 1232), (2420, 1232)], None, None, False), ('people', [(2462, 1147), (2524, 1147), (2524, 1386), (2462, 1386)], None, None, False), ('people', [(2511, 1158), (2556, 1158), (2556, 1396), (2511, 1396)], None, None, False), ('people', [(2543, 1149), (2600, 1149), (2600, 1455), (2543, 1455)], None, None, False), ('people', [(2584, 1134), (2609, 1134), (2609, 1401), (2584, 1401)], None, None, False), ('people', [(2606, 1176), (2660, 1176), (2660, 1420), (2606, 1420)], None, None, False), ('people', [(2600, 1125), (2669, 1125), (2669, 1238), (2600, 1238)], None, None, False), ('people', [(2670, 1148), (2750, 1148), (2750, 1436), (2670, 1436)], None, None, False), ('people', [(2745, 1188), (2856, 1188), (2856, 1456), (2745, 1456)], None, None, False), ('people', [(2765, 1142), (2811, 1142), (2811, 1193), (2765, 1193)], None, None, False), ('people', [(2772, 1123), (2862, 1123), (2862, 1190), (2772, 1190)], None, None, False), ('people', [(2805, 1168), (2878, 1168), (2878, 1378), (2805, 1378)], None, None, False), ('people', [(3140, 1147), (3233, 1147), (3233, 1415), (3140, 1415)], None, None, False), ('people', [(3233, 1149), (3311, 1149), (3311, 1412), (3233, 1412)], None, None, False), ('people', [(3640, 1138), (3758, 1138), (3758, 1459), (3640, 1459)], None, None, False)]
[('car', [(1, 1254), (669, 1254), (669, 1568), (1, 1568)], None, None, False), ('people', [(234, 1175), (311, 1175), (311, 1252), (234, 1252)], None, None, False), ('people', [(905, 1179), (987, 1179), (987, 1435), (905, 1435)], None, None, False), ('people', [(1260, 1169), (1304, 1169), (1304, 1274), (1260, 1274)], None, None, False), ('people', [(1324, 1168), (1356, 1168), (1356, 1268), (1324, 1268)], None, None, False), ('people', [(1374, 1161), (1410, 1161), (1410, 1222), (1374, 1222)], None, None, False), ('people', [(1365, 1193), (1456, 1193), (1456, 1431), (1365, 1431)], None, None, False), ('people', [(1427, 1187), (1480, 1187), (1480, 1415), (1427, 1415)], None, None, False), ('people', [(1469, 1186), (1530, 1186), (1530, 1423), (1469, 1423)], None, None, False), ('people', [(1530, 1181), (1603, 1181), (1603, 1415), (1530, 1415)], None, None, False), ('people', [(1942, 1174), (1999, 1174), (1999, 1367), (1942, 1367)], None, None, False), ('people', [(2249, 1174), (2332, 1174), (2332, 1435), (2249, 1435)], None, None, False), ('people', [(2312, 1151), (2376, 1151), (2376, 1402), (2312, 1402)], None, None, False), ('people', [(2366, 1175), (2424, 1175), (2424, 1343), (2366, 1343)], None, None, False), ('people', [(2352, 1319), (2469, 1319), (2469, 1435), (2352, 1435)], None, None, False), ('people', [(2450, 1172), (2533, 1172), (2533, 1385), (2450, 1385)], None, None, False), ('people', [(2510, 1155), (2591, 1155), (2591, 1458), (2510, 1458)], None, None, False), ('people', [(2596, 1163), (2676, 1163), (2676, 1463), (2596, 1463)], None, None, False), ('people', [(2592, 1129), (2678, 1129), (2678, 1208), (2592, 1208)], None, None, False), ('people', [(2664, 1143), (2769, 1143), (2769, 1443), (2664, 1443)], None, None, False), ('people', [(2796, 1168), (2874, 1168), (2874, 1415), (2796, 1415)], None, None, False), ('people', [(2776, 1131), (2869, 1131), (2869, 1199), (2776, 1199)], None, None, False), ('people', [(2740, 1136), (2782, 1136), (2782, 1200), (2740, 1200)], None, None, False), ('people', [(2891, 1172), (2994, 1172), (2994, 1446), (2891, 1446)], None, None, False), ('people', [(3111, 1146), (3220, 1146), (3220, 1406), (3111, 1406)], None, None, False), ('people', [(3223, 1154), (3327, 1154), (3327, 1412), (3223, 1412)], None, None, False), ('people', [(3640, 1140), (3765, 1140), (3765, 1452), (3640, 1452)], None, None, False)]
[('people', [(223, 1167), (316, 1167), (316, 1513), (223, 1513)], None, None, False), ('car', [(822, 1229), (1776, 1229), (1776, 1549), (822, 1549)], None, None, False), ('people', [(1255, 1175), (1284, 1175), (1284, 1207), (1255, 1207)], None, None, False), ('people', [(1250, 1203), (1294, 1203), (1294, 1230), (1250, 1230)], None, None, False), ('people', [(1325, 1199), (1381, 1199), (1381, 1240), (1325, 1240)], None, None, False), ('people', [(1391, 1159), (1430, 1159), (1430, 1219), (1391, 1219)], None, None, False), ('people', [(1419, 1184), (1481, 1184), (1481, 1274), (1419, 1274)], None, None, False), ('people', [(1370, 1170), (1388, 1170), (1388, 1197), (1370, 1197)], None, None, False), ('people', [(1479, 1188), (1547, 1188), (1547, 1290), (1479, 1290)], None, None, False), ('people', [(1529, 1184), (1601, 1184), (1601, 1331), (1529, 1331)], None, None, False), ('people', [(1706, 1208), (1804, 1208), (1804, 1290), (1706, 1290)], None, None, False), ('people', [(1943, 1179), (2007, 1179), (2007, 1372), (1943, 1372)], None, None, False), ('people', [(1944, 1139), (2001, 1139), (2001, 1208), (1944, 1208)], None, None, False), ('people', [(2228, 1174), (2317, 1174), (2317, 1439), (2228, 1439)], None, None, False), ('people', [(2320, 1171), (2415, 1171), (2415, 1419), (2320, 1419)], None, None, False), ('people', [(2280, 1174), (2330, 1174), (2330, 1369), (2280, 1369)], None, None, False), ('people', [(2355, 1316), (2470, 1316), (2470, 1450), (2355, 1450)], None, None, False), ('people', [(2425, 1180), (2500, 1180), (2500, 1361), (2425, 1361)], None, None, False), ('people', [(2492, 1164), (2547, 1164), (2547, 1269), (2492, 1269)], None, None, False), ('people', [(2527, 1159), (2645, 1159), (2645, 1480), (2527, 1480)], None, None, False), ('people', [(2537, 1135), (2610, 1135), (2610, 1202), (2537, 1202)], None, None, False), ('people', [(2597, 1129), (2659, 1129), (2659, 1184), (2597, 1184)], None, None, False), ('people', [(2626, 1163), (2735, 1163), (2735, 1481), (2626, 1481)], None, None, False), ('people', [(2689, 1152), (2796, 1152), (2796, 1448), (2689, 1448)], None, None, False), ('people', [(2791, 1168), (2877, 1168), (2877, 1405), (2791, 1405)], None, None, False), ('people', [(2748, 1140), (2797, 1140), (2797, 1261), (2748, 1261)], None, None, False), ('people', [(2782, 1122), (2870, 1122), (2870, 1203), (2782, 1203)], None, None, False), ('people', [(3045, 1168), (3157, 1168), (3157, 1445), (3045, 1445)], None, None, False), ('people', [(3143, 1146), (3240, 1146), (3240, 1408), (3143, 1408)], None, None, False), ('people', [(3232, 1156), (3307, 1156), (3307, 1411), (3232, 1411)], None, None, False), ('people', [(3637, 1135), (3757, 1135), (3757, 1460), (3637, 1460)], None, None, False)]
[('people', [(231, 1159), (318, 1159), (318, 1520), (231, 1520)], None, None, False), ('people', [(903, 1175), (982, 1175), (982, 1452), (903, 1452)], None, None, False), ('people', [(1235, 1170), (1269, 1170), (1269, 1251), (1235, 1251)], None, None, False), ('people', [(1358, 1165), (1402, 1165), (1402, 1261), (1358, 1261)], None, None, False), ('people', [(1405, 1161), (1442, 1161), (1442, 1225), (1405, 1225)], None, None, False), ('people', [(1417, 1179), (1481, 1179), (1481, 1410), (1417, 1410)], None, None, False), ('people', [(1474, 1190), (1545, 1190), (1545, 1419), (1474, 1419)], None, None, False), ('people', [(1537, 1182), (1596, 1182), (1596, 1410), (1537, 1410)], None, None, False), ('people', [(1883, 1217), (2807, 1217), (2807, 1537), (1883, 1537)], None, None, False), ('people', [(2262, 1166), (2309, 1166), (2309, 1221), (2262, 1221)], None, None, False), ('people', [(2230, 1177), (2261, 1177), (2261, 1219), (2230, 1219)], None, None, False), ('people', [(2468, 1175), (2525, 1175), (2525, 1265), (2468, 1265)], None, None, False), ('people', [(2514, 1161), (2561, 1161), (2561, 1258), (2514, 1258)], None, None, False), ('people', [(2550, 1138), (2605, 1138), (2605, 1247), (2550, 1247)], None, None, False), ('people', [(2598, 1117), (2682, 1117), (2682, 1207), (2598, 1207)], None, None, False), ('people', [(2562, 1155), (2676, 1155), (2676, 1339), (2562, 1339)], None, None, False), ('people', [(2659, 1161), (2779, 1161), (2779, 1366), (2659, 1366)], None, None, False), ('people', [(2727, 1142), (2815, 1142), (2815, 1417), (2727, 1417)], None, None, False), ('people', [(2790, 1167), (2884, 1167), (2884, 1401), (2790, 1401)], None, None, False), ('people', [(2769, 1127), (2868, 1127), (2868, 1216), (2769, 1216)], None, None, False), ('people', [(3140, 1142), (3234, 1142), (3234, 1409), (3140, 1409)], None, None, False), ('people', [(3173, 1166), (3306, 1166), (3306, 1445), (3173, 1445)], None, None, False), ('people', [(3237, 1151), (3303, 1151), (3303, 1407), (3237, 1407)], None, None, False), ('people', [(3646, 1137), (3763, 1137), (3763, 1468), (3646, 1468)], None, None, False)]
[('people', [(244, 1168), (320, 1168), (320, 1515), (244, 1515)], None, None, False), ('people', [(903, 1181), (987, 1181), (987, 1439), (903, 1439)], None, None, False), ('people', [(1363, 1162), (1418, 1162), (1418, 1221), (1363, 1221)], None, None, False), ('people', [(1412, 1181), (1483, 1181), (1483, 1414), (1412, 1414)], None, None, False), ('people', [(1465, 1192), (1540, 1192), (1540, 1418), (1465, 1418)], None, None, False), ('people', [(1533, 1182), (1607, 1182), (1607, 1411), (1533, 1411)], None, None, False), ('people', [(1966, 1180), (2018, 1180), (2018, 1381), (1966, 1381)], None, None, False), ('people', [(2225, 1179), (2304, 1179), (2304, 1431), (2225, 1431)], None, None, False), ('people', [(2493, 1178), (2554, 1178), (2554, 1409), (2493, 1409)], None, None, False), ('people', [(2562, 1162), (2651, 1162), (2651, 1495), (2562, 1495)], None, None, False), ('people', [(2556, 1140), (2643, 1140), (2643, 1218), (2556, 1218)], None, None, False), ('people', [(2592, 1122), (2669, 1122), (2669, 1196), (2592, 1196)], None, None, False), ('people', [(2650, 1160), (2769, 1160), (2769, 1486), (2650, 1486)], None, None, False), ('people', [(2736, 1152), (2830, 1152), (2830, 1265), (2736, 1265)], None, None, False), ('people', [(2772, 1125), (2867, 1125), (2867, 1171), (2772, 1171)], None, None, False), ('people', [(2796, 1165), (2877, 1165), (2877, 1251), (2796, 1251)], None, None, False), ('car', [(2724, 1214), (3770, 1214), (3770, 1525), (2724, 1525)], None, None, False), ('people', [(3145, 1152), (3230, 1152), (3230, 1212), (3145, 1212)], None, None, False), ('people', [(3227, 1161), (3287, 1161), (3287, 1216), (3227, 1216)], None, None, False), ('people', [(3358, 1164), (3451, 1164), (3451, 1238), (3358, 1238)], None, None, False), ('people', [(3640, 1129), (3747, 1129), (3747, 1351), (3640, 1351)], None, None, False), ('people', [(3782, 1151), (3840, 1151), (3840, 1413), (3782, 1413)], None, None, False)]
[('people', [(231, 1159), (318, 1159), (318, 1520), (231, 1520)], None, None, False), ('people', [(903, 1175), (982, 1175), (982, 1452), (903, 1452)], None, None, False), ('people', [(1235, 1170), (1269, 1170), (1269, 1251), (1235, 1251)], None, None, False), ('people', [(1358, 1165), (1402, 1165), (1402, 1261), (1358, 1261)], None, None, False), ('people', [(1405, 1161), (1442, 1161), (1442, 1225), (1405, 1225)], None, None, False), ('people', [(1417, 1179), (1481, 1179), (1481, 1410), (1417, 1410)], None, None, False), ('people', [(1474, 1190), (1545, 1190), (1545, 1419), (1474, 1419)], None, None, False), ('people', [(1537, 1182), (1596, 1182), (1596, 1410), (1537, 1410)], None, None, False), ('people', [(1883, 1217), (2807, 1217), (2807, 1537), (1883, 1537)], None, None, False), ('people', [(2262, 1166), (2309, 1166), (2309, 1221), (2262, 1221)], None, None, False), ('people', [(2230, 1177), (2261, 1177), (2261, 1219), (2230, 1219)], None, None, False), ('people', [(2468, 1175), (2525, 1175), (2525, 1265), (2468, 1265)], None, None, False), ('people', [(2514, 1161), (2561, 1161), (2561, 1258), (2514, 1258)], None, None, False), ('people', [(2550, 1138), (2605, 1138), (2605, 1247), (2550, 1247)], None, None, False), ('people', [(2598, 1117), (2682, 1117), (2682, 1207), (2598, 1207)], None, None, False), ('people', [(2562, 1155), (2676, 1155), (2676, 1339), (2562, 1339)], None, None, False), ('people', [(2659, 1161), (2779, 1161), (2779, 1366), (2659, 1366)], None, None, False), ('people', [(2727, 1142), (2815, 1142), (2815, 1417), (2727, 1417)], None, None, False), ('people', [(2790, 1167), (2884, 1167), (2884, 1401), (2790, 1401)], None, None, False), ('people', [(2769, 1127), (2868, 1127), (2868, 1216), (2769, 1216)], None, None, False), ('people', [(3140, 1142), (3234, 1142), (3234, 1409), (3140, 1409)], None, None, False), ('people', [(3173, 1166), (3306, 1166), (3306, 1445), (3173, 1445)], None, None, False), ('people', [(3237, 1151), (3303, 1151), (3303, 1407), (3237, 1407)], None, None, False), ('people', [(3646, 1137), (3763, 1137), (3763, 1468), (3646, 1468)], None, None, False)]
[('people', [(244, 1168), (320, 1168), (320, 1515), (244, 1515)], None, None, False), ('people', [(903, 1181), (987, 1181), (987, 1439), (903, 1439)], None, None, False), ('people', [(1363, 1162), (1418, 1162), (1418, 1221), (1363, 1221)], None, None, False), ('people', [(1412, 1181), (1483, 1181), (1483, 1414), (1412, 1414)], None, None, False), ('people', [(1465, 1192), (1540, 1192), (1540, 1418), (1465, 1418)], None, None, False), ('people', [(1533, 1182), (1607, 1182), (1607, 1411), (1533, 1411)], None, None, False), ('people', [(1966, 1180), (2018, 1180), (2018, 1381), (1966, 1381)], None, None, False), ('people', [(2225, 1179), (2304, 1179), (2304, 1431), (2225, 1431)], None, None, False), ('people', [(2493, 1178), (2554, 1178), (2554, 1409), (2493, 1409)], None, None, False), ('people', [(2562, 1162), (2651, 1162), (2651, 1495), (2562, 1495)], None, None, False), ('people', [(2556, 1140), (2643, 1140), (2643, 1218), (2556, 1218)], None, None, False), ('people', [(2592, 1122), (2669, 1122), (2669, 1196), (2592, 1196)], None, None, False), ('people', [(2650, 1160), (2769, 1160), (2769, 1486), (2650, 1486)], None, None, False), ('people', [(2736, 1152), (2830, 1152), (2830, 1265), (2736, 1265)], None, None, False), ('people', [(2772, 1125), (2867, 1125), (2867, 1171), (2772, 1171)], None, None, False), ('people', [(2796, 1165), (2877, 1165), (2877, 1251), (2796, 1251)], None, None, False), ('car', [(2724, 1214), (3770, 1214), (3770, 1525), (2724, 1525)], None, None, False), ('people', [(3145, 1152), (3230, 1152), (3230, 1212), (3145, 1212)], None, None, False), ('people', [(3227, 1161), (3287, 1161), (3287, 1216), (3227, 1216)], None, None, False), ('people', [(3358, 1164), (3451, 1164), (3451, 1238), (3358, 1238)], None, None, False), ('people', [(3640, 1129), (3747, 1129), (3747, 1351), (3640, 1351)], None, None, False), ('people', [(3782, 1151), (3840, 1151), (3840, 1413), (3782, 1413)], None, None, False)]
[('people', [(231, 1159), (318, 1159), (318, 1520), (231, 1520)], None, None, False), ('people', [(903, 1175), (982, 1175), (982, 1452), (903, 1452)], None, None, False), ('people', [(1235, 1170), (1269, 1170), (1269, 1251), (1235, 1251)], None, None, False), ('people', [(1358, 1165), (1402, 1165), (1402, 1261), (1358, 1261)], None, None, False), ('people', [(1405, 1161), (1442, 1161), (1442, 1225), (1405, 1225)], None, None, False), ('people', [(1417, 1179), (1481, 1179), (1481, 1410), (1417, 1410)], None, None, False), ('people', [(1474, 1190), (1545, 1190), (1545, 1419), (1474, 1419)], None, None, False), ('people', [(1537, 1182), (1596, 1182), (1596, 1410), (1537, 1410)], None, None, False), ('people', [(1883, 1217), (2807, 1217), (2807, 1537), (1883, 1537)], None, None, False), ('people', [(2262, 1166), (2309, 1166), (2309, 1221), (2262, 1221)], None, None, False), ('people', [(2230, 1177), (2261, 1177), (2261, 1219), (2230, 1219)], None, None, False), ('people', [(2468, 1175), (2525, 1175), (2525, 1265), (2468, 1265)], None, None, False), ('people', [(2514, 1161), (2561, 1161), (2561, 1258), (2514, 1258)], None, None, False), ('people', [(2550, 1138), (2605, 1138), (2605, 1247), (2550, 1247)], None, None, False), ('people', [(2598, 1117), (2682, 1117), (2682, 1207), (2598, 1207)], None, None, False), ('people', [(2562, 1155), (2676, 1155), (2676, 1339), (2562, 1339)], None, None, False), ('people', [(2659, 1161), (2779, 1161), (2779, 1366), (2659, 1366)], None, None, False), ('people', [(2727, 1142), (2815, 1142), (2815, 1417), (2727, 1417)], None, None, False), ('people', [(2790, 1167), (2884, 1167), (2884, 1401), (2790, 1401)], None, None, False), ('people', [(2769, 1127), (2868, 1127), (2868, 1216), (2769, 1216)], None, None, False), ('people', [(3140, 1142), (3234, 1142), (3234, 1409), (3140, 1409)], None, None, False), ('people', [(3173, 1166), (3306, 1166), (3306, 1445), (3173, 1445)], None, None, False), ('people', [(3237, 1151), (3303, 1151), (3303, 1407), (3237, 1407)], None, None, False), ('people', [(3646, 1137), (3763, 1137), (3763, 1468), (3646, 1468)], None, None, False)]
[('people', [(244, 1168), (320, 1168), (320, 1515), (244, 1515)], None, None, False), ('people', [(903, 1181), (987, 1181), (987, 1439), (903, 1439)], None, None, False), ('people', [(1363, 1162), (1418, 1162), (1418, 1221), (1363, 1221)], None, None, False), ('people', [(1412, 1181), (1483, 1181), (1483, 1414), (1412, 1414)], None, None, False), ('people', [(1465, 1192), (1540, 1192), (1540, 1418), (1465, 1418)], None, None, False), ('people', [(1533, 1182), (1607, 1182), (1607, 1411), (1533, 1411)], None, None, False), ('people', [(1966, 1180), (2018, 1180), (2018, 1381), (1966, 1381)], None, None, False), ('people', [(2225, 1179), (2304, 1179), (2304, 1431), (2225, 1431)], None, None, False), ('people', [(2493, 1178), (2554, 1178), (2554, 1409), (2493, 1409)], None, None, False), ('people', [(2562, 1162), (2651, 1162), (2651, 1495), (2562, 1495)], None, None, False), ('people', [(2556, 1140), (2643, 1140), (2643, 1218), (2556, 1218)], None, None, False), ('people', [(2592, 1122), (2669, 1122), (2669, 1196), (2592, 1196)], None, None, False), ('people', [(2650, 1160), (2769, 1160), (2769, 1486), (2650, 1486)], None, None, False), ('people', [(2736, 1152), (2830, 1152), (2830, 1265), (2736, 1265)], Non210.3       210.3                   210.3       210.3       210.3