#!/usr/bin/env python3
"""
测试华丽版Web应用（真正工作版）
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_webapp():
    """测试Web应用"""
    print("🧪 测试华丽版Web应用（真正工作版）...")
    
    # 检查服务器是否运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
            print("🌐 访问地址: http://localhost:5000")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: run_webapp_gorgeous.bat")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_files():
    """检查必要文件"""
    print("📁 检查必要文件...")
    
    webapp_dir = Path("webapp")
    required_files = [
        webapp_dir / "app_gorgeous_working.py",
        webapp_dir / "index_gorgeous.html",
        webapp_dir / "static/js/main_gorgeous.js",
        webapp_dir / "templates/index.html"
    ]
    
    all_exist = True
    for file_path in required_files:
        if file_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def check_model_availability():
    """检查模型可用性"""
    print("🤖 检查AI模型可用性...")
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics已安装")
        
        # 检查自定义模型
        custom_model_paths = [
            "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
            "../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
            "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
        ]
        
        custom_model_found = False
        for model_path in custom_model_paths:
            if os.path.exists(model_path):
                print(f"✅ 找到自定义训练模型: {model_path}")
                custom_model_found = True
                break
        
        if not custom_model_found:
            print("⚠️  未找到自定义训练模型，将使用预训练模型")
        
        return True
        
    except ImportError:
        print("❌ ultralytics未安装")
        print("💡 安装命令: pip install ultralytics")
        return False

def main():
    """主函数"""
    print("🌟 YOLOv11车辆计数器 - 华丽版测试（真正工作版）")
    print("=" * 70)
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败")
        return
    
    print("\n✅ 文件检查通过")
    
    # 检查模型
    model_available = check_model_availability()
    
    # 测试Web应用
    if test_webapp():
        print("\n🎉 华丽版Web应用测试通过！")
        print("\n🌟 华丽版特色:")
        print("   🎨 现代化玻璃拟态设计")
        print("   🌈 动态渐变背景和粒子效果")
        print("   💫 流畅的动画和过渡效果")
        print("   🎯 专业级AI分析界面")
        print("   📱 响应式布局适配")
        
        print("\n🤖 AI功能:")
        print("   🔍 YOLOv11深度学习检测")
        print("   🚗 车辆穿越计数（左右分别统计）")
        print("   👥 人员总数统计（按ID去重）")
        print("   📏 智能轨迹跟踪")
        print("   🎨 高级可视化注释")
        print("   📹 高质量视频输出")
        
        if model_available:
            print("\n✅ AI模型就绪，可以进行真实检测")
        else:
            print("\n⚠️  AI模型不可用，将运行在模拟模式")
            
        print("\n🎛️ 界面布局:")
        print("   📤 左侧: 智能控制中心")
        print("   🎬 中央: 视频预览和对比分析")
        print("   📊 右侧: 实时统计和数据下载")
        
    else:
        print("\n❌ Web应用测试失败")
        print("\n💡 解决方案:")
        print("   1. 运行 run_webapp_gorgeous.bat 启动服务器")
        print("   2. 检查端口5000是否被占用")
        print("   3. 确保安装了所需依赖:")
        print("      pip install flask ultralytics opencv-python")
        print("   4. 检查华丽版文件是否存在")

if __name__ == "__main__":
    main()