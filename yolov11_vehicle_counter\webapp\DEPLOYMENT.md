# YOLOv11车辆计数器 - 云服务器部署指南

## 🚀 快速部署

### 1. 环境要求
- Python 3.8+
- 至少4GB内存
- 推荐使用GPU（可选）

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动服务器
```bash
# 方法1: 使用启动脚本（推荐）
python start_server.py

# 方法2: 直接启动
python app.py
```

### 4. 访问应用
- 本地访问: http://localhost:5000
- 外部访问: http://your-server-ip:5000

## 📁 项目结构

```
webapp/
├── app.py                 # 主应用文件
├── deploy_config.py       # 部署配置
├── start_server.py        # 启动脚本
├── requirements.txt       # 依赖包
├── templates/
│   └── index.html        # 前端页面
├── static/
│   ├── js/
│   │   └── main.js       # JavaScript逻辑
│   └── css/              # 样式文件
├── uploads/              # 上传文件目录（自动创建）
└── outputs/              # 输出文件目录（自动创建）
```

## 🔧 配置说明

### 路径配置
- 所有路径都使用相对路径，无硬编码
- 自动适配不同环境（本地/云服务器）
- 模型文件自动搜索多个位置

### 服务器配置
- 默认端口: 5000
- 允许外部访问: 0.0.0.0
- 多线程支持
- 生产环境优化

## 🎯 功能特性

### UI优化
- ✅ Car和People并列显示
- ✅ 实时统计更新
- ✅ 处理完成后状态保持
- ✅ 响应式设计
- ✅ 降低显示延时

### 技术特性
- 🤖 YOLOv11深度学习检测
- 🎯 实时目标跟踪
- 📊 精确计数统计
- 📹 视频处理和预览
- ☁️ 云服务器优化

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -an | grep :5000
   # 或使用启动脚本自动选择可用端口
   python start_server.py
   ```

2. **模型文件未找到**
   - 确保模型文件在正确位置
   - 检查 `deploy_config.py` 中的路径配置
   - 系统会自动使用预训练模型作为备选

3. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   ```

4. **GPU相关错误**
   ```bash
   # 如果没有GPU，系统会自动使用CPU
   # 设置环境变量强制使用CPU
   export CUDA_VISIBLE_DEVICES=""
   ```

### 性能优化

1. **内存优化**
   - 系统自动管理GPU内存
   - 定期清理缓存
   - 监控内存使用

2. **处理速度**
   - 更新频率: 每5帧（降低延时）
   - 多线程处理
   - 异步任务队列

## 🔒 安全注意事项

1. **文件上传限制**
   - 最大文件大小: 500MB
   - 支持格式: mp4, avi, mov, mkv
   - 文件名安全处理

2. **服务器安全**
   - 生产环境关闭调试模式
   - 建议使用反向代理（nginx）
   - 配置防火墙规则

## 📝 更新日志

### v2.0 - 云服务器优化版
- ✅ 移除所有硬编码路径
- ✅ 添加部署配置系统
- ✅ UI界面优化（Car/People并列）
- ✅ 降低显示延时
- ✅ 处理完成状态保持
- ✅ 响应式设计改进

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否完整安装
3. 端口是否可用
4. 模型文件是否存在

运行 `python start_server.py` 会自动检查环境并提供详细的错误信息。
