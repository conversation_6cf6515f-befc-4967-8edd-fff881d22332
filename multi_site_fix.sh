#!/bin/bash

# 多站点修复脚本 - 恢复hearttalk.me并正确配置vehicle.smart-traffic.top

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "========================================"
echo "🔧 多站点配置修复"
echo "========================================"
echo "恢复 hearttalk.me 并正确配置 vehicle.smart-traffic.top"
echo "========================================"
echo

# 停止车辆计数应用
log_info "停止车辆计数应用..."
sudo supervisorctl stop vehicle-counter || true

# 禁用我们的站点配置
log_info "禁用错误的站点配置..."
sudo a2dissite vehicle-counter.conf || true

# 备份当前配置
log_info "备份当前Apache配置..."
sudo cp -r /etc/apache2/sites-available /etc/apache2/sites-available.backup.$(date +%Y%m%d_%H%M%S)

# 检查原有网站目录
log_info "检查原有网站..."
if [ -d "/var/www/html" ]; then
    log_info "发现原有网站目录: /var/www/html"
    ls -la /var/www/html/
fi

# 移动车辆计数项目到独立目录
log_info "移动车辆计数项目..."
if [ -d "/var/www/vehicle-counter" ]; then
    log_success "车辆计数项目目录已存在: /var/www/vehicle-counter"
else
    log_error "车辆计数项目目录不存在，需要重新部署"
fi

# 创建hearttalk.me的虚拟主机配置
log_info "创建hearttalk.me虚拟主机配置..."
sudo tee /etc/apache2/sites-available/hearttalk.me.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName hearttalk.me
    ServerAlias www.hearttalk.me
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/hearttalk_error.log
    CustomLog ${APACHE_LOG_DIR}/hearttalk_access.log combined
</VirtualHost>
EOF

# 创建vehicle.smart-traffic.top的虚拟主机配置
log_info "创建vehicle.smart-traffic.top虚拟主机配置..."
sudo tee /etc/apache2/sites-available/vehicle.smart-traffic.top.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    ServerAlias www.vehicle.smart-traffic.top
    
    DocumentRoot /var/www/vehicle-counter
    
    # 静态文件服务
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    # 代理到Flask应用
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # 文件上传限制 (100MB)
    LimitRequestBody 104857600
    
    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${APACHE_LOG_DIR}/vehicle-counter_error.log
    CustomLog ${APACHE_LOG_DIR}/vehicle-counter_access.log combined
</VirtualHost>
EOF

# 禁用默认站点
log_info "禁用默认站点..."
sudo a2dissite 000-default.conf || true

# 启用两个站点
log_info "启用站点配置..."
sudo a2ensite hearttalk.me.conf
sudo a2ensite vehicle.smart-traffic.top.conf

# 测试Apache配置
log_info "测试Apache配置..."
sudo apache2ctl configtest

# 重新加载Apache
log_info "重新加载Apache..."
sudo systemctl reload apache2

# 重启车辆计数应用
log_info "重启车辆计数应用..."
sudo supervisorctl start vehicle-counter

# 检查服务状态
log_info "检查服务状态..."
sudo supervisorctl status vehicle-counter
sudo systemctl status apache2 --no-pager -l

echo
echo "========================================"
echo "🎉 多站点配置完成！"
echo "========================================"
echo "📱 hearttalk.me: http://hearttalk.me"
echo "🚗 vehicle.smart-traffic.top: http://vehicle.smart-traffic.top"
echo "🖥️  IP访问测试: http://************"
echo "========================================"
echo
echo "测试命令:"
echo "curl -H 'Host: hearttalk.me' http://************"
echo "curl -H 'Host: vehicle.smart-traffic.top' http://************"
echo "========================================"
