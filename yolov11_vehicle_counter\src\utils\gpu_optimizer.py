"""
GPU优化器模块
检测和配置GPU使用，提升处理性能
"""

import logging
import torch
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class GPUOptimizer:
    """GPU优化器 - 检测和配置GPU使用"""
    
    def __init__(self):
        self.device = None
        self.gpu_available = False
        self.gpu_info = {}
        self._detect_gpu()
    
    def _detect_gpu(self):
        """检测GPU可用性"""
        try:
            if torch.cuda.is_available():
                self.gpu_available = True
                self.device = torch.device('cuda:0')
                
                # 获取GPU信息
                self.gpu_info = {
                    'name': torch.cuda.get_device_name(0),
                    'memory_total': torch.cuda.get_device_properties(0).total_memory / 1024**3,  # GB
                    'memory_allocated': torch.cuda.memory_allocated(0) / 1024**3,  # GB
                    'memory_reserved': torch.cuda.memory_reserved(0) / 1024**3,  # GB
                    'cuda_version': torch.version.cuda,
                    'device_count': torch.cuda.device_count()
                }
                
                logger.info(f"检测到GPU: {self.gpu_info['name']}")
                logger.info(f"GPU内存: {self.gpu_info['memory_total']:.1f}GB 总计")
                
            else:
                self.gpu_available = False
                self.device = torch.device('cpu')
                logger.info("未检测到可用GPU，使用CPU")
                
        except Exception as e:
            logger.warning(f"GPU检测失败: {e}")
            self.gpu_available = False
            self.device = torch.device('cpu')
    
    def get_optimal_device(self, force_cpu: bool = False) -> str:
        """
        获取最优设备
        
        Args:
            force_cpu: 是否强制使用CPU
            
        Returns:
            str: 设备名称 ('cuda' 或 'cpu')
        """
        if force_cpu:
            return 'cpu'
        
        if self.gpu_available:
            # 检查GPU内存使用情况
            if self.gpu_info['memory_allocated'] / self.gpu_info['memory_total'] < 0.8:
                return 'cuda'
            else:
                logger.warning("GPU内存使用率过高，切换到CPU")
                return 'cpu'
        
        return 'cpu'
    
    def get_optimized_tracker_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取GPU优化的跟踪器配置
        
        Args:
            base_config: 基础配置
            
        Returns:
            Dict: 优化后的配置
        """
        optimized_config = base_config.copy()
        
        if self.gpu_available:
            # GPU优化配置
            optimized_config.update({
                'device': 'cuda',
                'max_dist': 0.2,        # GPU可以处理更复杂的计算
                'max_iou_distance': 0.7,
                'max_age': 30,          # 更长的跟踪生命周期
                'n_init': 3,            # 更多的初始化帧
                'nn_budget': 100        # 更大的预算
            })
            logger.info("使用GPU优化配置")
        else:
            # CPU优化配置（保守）
            optimized_config.update({
                'device': 'cpu',
                'max_dist': 0.1,
                'max_iou_distance': 0.5,
                'max_age': 15,
                'n_init': 2,
                'nn_budget': 50
            })
            logger.info("使用CPU优化配置")
        
        return optimized_config
    
    def get_yolo_optimization(self) -> Dict[str, Any]:
        """获取YOLO GPU优化参数"""
        if self.gpu_available:
            return {
                'device': 'cuda',
                'half': True,           # 使用FP16精度
                'batch_size': 4,        # 批处理
                'imgsz': 640,          # 标准尺寸
                'conf': 0.4,           # 稍微降低置信度阈值
                'iou': 0.6,            # IOU阈值
                'max_det': 500         # 更多检测
            }
        else:
            return {
                'device': 'cpu',
                'half': False,
                'batch_size': 1,
                'imgsz': 640,
                'conf': 0.5,
                'iou': 0.7,
                'max_det': 300
            }
    
    def optimize_memory(self):
        """优化GPU内存使用"""
        if self.gpu_available:
            try:
                # 清理GPU缓存
                torch.cuda.empty_cache()
                
                # 设置内存分配策略
                torch.cuda.set_per_process_memory_fraction(0.8)  # 使用80%的GPU内存
                
                logger.info("GPU内存优化完成")
                
                # 更新内存信息
                self.gpu_info['memory_allocated'] = torch.cuda.memory_allocated(0) / 1024**3
                self.gpu_info['memory_reserved'] = torch.cuda.memory_reserved(0) / 1024**3
                
            except Exception as e:
                logger.warning(f"GPU内存优化失败: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取GPU性能报告"""
        report = {
            'gpu_available': self.gpu_available,
            'device': str(self.device),
            'gpu_info': self.gpu_info.copy() if self.gpu_available else None
        }
        
        if self.gpu_available:
            try:
                # 更新实时内存信息
                report['gpu_info']['memory_allocated_current'] = torch.cuda.memory_allocated(0) / 1024**3
                report['gpu_info']['memory_reserved_current'] = torch.cuda.memory_reserved(0) / 1024**3
                report['gpu_info']['memory_usage_percent'] = (
                    report['gpu_info']['memory_allocated_current'] / 
                    report['gpu_info']['memory_total'] * 100
                )
            except Exception as e:
                logger.warning(f"获取GPU状态失败: {e}")
        
        return report
    
    def should_use_gpu(self, memory_threshold: float = 0.7) -> bool:
        """
        判断是否应该使用GPU
        
        Args:
            memory_threshold: 内存使用阈值
            
        Returns:
            bool: 是否使用GPU
        """
        if not self.gpu_available:
            return False
        
        try:
            current_usage = torch.cuda.memory_allocated(0) / torch.cuda.get_device_properties(0).total_memory
            return current_usage < memory_threshold
        except Exception:
            return False

# 全局GPU优化器实例
_gpu_optimizer = None

def get_gpu_optimizer() -> GPUOptimizer:
    """获取GPU优化器的全局实例"""
    global _gpu_optimizer
    if _gpu_optimizer is None:
        _gpu_optimizer = GPUOptimizer()
    return _gpu_optimizer