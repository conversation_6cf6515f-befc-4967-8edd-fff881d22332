import argparse
from ultralytics import Y<PERSON><PERSON>

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='YOLOv11 最新训练脚本 (UltraThink)')
    parser.add_argument('--data', type=str, default='dataset/split_dataset/data.yaml', help='数据集配置文件')
    parser.add_argument('--weights', type=str, default='models/weights/yolo11n.pt', help='预训练权重文件')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮数')
    parser.add_argument('--patience', type=int, default=200, help='提前停止patience')
    parser.add_argument('--batch', type=int, default=4, help='批次大小')
    parser.add_argument('--imgsz', type=int, default=1024, help='输入图片尺寸')
    parser.add_argument('--project', type=str, default='runs/train', help='训练结果输出目录')
    parser.add_argument('--name', type=str, default='yolov11_vehicle_detection', help='实验名称')
    parser.add_argument('--device', type=str, default='0', help='训练设备（如0,1或cpu）')
    parser.add_argument('--workers', type=int, default=8, help='dataloader线程数')
    parser.add_argument('--cache', action='store_true', help='是否启用数据缓存')
    args = parser.parse_args()

    print("=== UltraThink YOLOv11 训练参数 ===")
    for k, v in vars(args).items():
        print(f"{k}: {v}")
    print("==============================")

    model = YOLO(args.weights)
    results = model.train(
        data=args.data,
        epochs=args.epochs,
        patience=args.patience,
        batch=args.batch,
        imgsz=args.imgsz,
        project=args.project,
        name=args.name,
        device=args.device,
        workers=args.workers,
        cache=args.cache,
    )