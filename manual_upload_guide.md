# 手动上传指南

## 方法1: 使用WinSCP (推荐)

### 1. 下载并安装WinSCP
- 下载地址: https://winscp.net/eng/download.php
- 安装后打开WinSCP

### 2. 连接服务器
```
协议: SFTP
主机名: 47.236.30.93
端口: 22
用户名: admin
密码: [你的服务器密码]
```

### 3. 上传文件
- 连接成功后，在右侧窗口导航到: `/var/www/vehicle-counter/`
- 如果目录不存在，右键创建目录
- 在左侧窗口导航到: `D:\lkr_yolo\yolov11_vehicle_counter\webapp\`
- 选择以下文件和文件夹，拖拽到右侧：
  - `app.py`
  - `index.html`
  - `requirements.txt`
  - `static` 文件夹 (整个文件夹)

## 方法2: 使用命令行 (需要输入密码)

### 1. 创建远程目录
```bash
ssh admin@47.236.30.93 "sudo mkdir -p /var/www/vehicle-counter && sudo chown admin:admin /var/www/vehicle-counter"
```

### 2. 上传文件
```bash
# 上传主要文件
scp yolov11_vehicle_counter\webapp\app.py admin@47.236.30.93:/var/www/vehicle-counter/
scp yolov11_vehicle_counter\webapp\index.html admin@47.236.30.93:/var/www/vehicle-counter/
scp yolov11_vehicle_counter\webapp\requirements.txt admin@47.236.30.93:/var/www/vehicle-counter/

# 上传static目录
scp -r yolov11_vehicle_counter\webapp\static admin@47.236.30.93:/var/www/vehicle-counter/
```

## 方法3: 使用VSCode插件

### 1. 安装SFTP插件
- 在VSCode中安装 "SFTP" 插件

### 2. 配置连接
- 在项目根目录创建 `.vscode/sftp.json`
- 配置服务器连接信息

### 3. 上传文件
- 右键文件夹选择 "Upload Folder"

## 验证上传成功

上传完成后，登录服务器验证：
```bash
ssh admin@47.236.30.93
ls -la /var/www/vehicle-counter/
```

应该看到以下文件：
- app.py
- index.html
- requirements.txt
- static/ (目录)

## 下一步

文件上传成功后，运行部署脚本：
```bash
# 上传部署脚本
scp deploy_smart_traffic.sh admin@47.236.30.93:~/

# 登录服务器
ssh admin@47.236.30.93

# 运行部署
chmod +x deploy_smart_traffic.sh
./deploy_smart_traffic.sh
```
