# yolov11_vehicle_counter 项目任务信息记录

## 关键参考代码与功能梳理（src目录）

### 1. 训练脚本
- `train_yolov11m.py`：主力训练脚本，mAP高达0.916，推荐继续使用。
- `train_yolov11n.py`、`train_yolov11x.py`、`train_yolov12x.py`、`train_yolov11.py`、`train_yolov11_new.py`：不同模型/参数的训练脚本，可用于横向对比。

### 2. 推理与批量处理
- `main.py`：集成YOLOv11推理、DeepSORT多目标跟踪、撞线计数等功能，支持图片/视频批量处理。
- `YOLOv11-DeepSort/main_yolov11.py`：YOLOv11+DeepSORT多目标跟踪与ID分配，适合视频流/图片序列的目标跟踪与轨迹分析。
- `YOLOv11-DeepSort/tracker.py`、`YOLOv11-DeepSort/deep_sort/`：DeepSORT跟踪算法实现，支持ID分配、轨迹管理。

### 3. 计数与可视化
- `webui.py`：集成Gradio等前端可视化，支持图片/视频上传、检测与计数结果展示。
- `main.py`、`YOLOv11-DeepSort/main_yolov11.py`：均包含撞线计数、目标ID可视化等功能。

### 4. 数据处理与分割
- `yolov5/split_dataset.py`、`split_and_generate_yaml.py`：数据集交错分割、自动生成data.yaml配置。

### 5. 其他
- `tracker/`：DeepSORT、SORT等多目标跟踪算法实现，便于集成计数、轨迹分析。
- `YOLOv11-DeepSort/sutils.py`、`image2video.py`：辅助脚本，支持图片视频转换、可视化等。

---

## 后续任务推进建议
1. **推理与批量检测**：用`main.py`或`YOLOv11-DeepSort/main_yolov11.py`，对图片/视频/序列进行目标检测与跟踪。
2. **车辆/行人计数**：集成撞线计数、区域计数等功能，结合DeepSORT实现ID唯一性。
3. **前端可视化**：用`webui.py`开发Gradio界面，支持交互式检测与计数结果展示。
4. **实验归档与对比**：整理所有训练、推理、计数结果，便于横向对比和复现。
5. **项目文档与备份**：持续完善本记录，确保所有关键路径、功能、实验结果可追溯。

---

## DeepSort/OSNet特征提取器权重与模型结构不匹配问题记录

### 1. 报错现象
- RuntimeError: Error(s) in loading state_dict for OSNet: Missing key(s) in state_dict: ...
- 说明DeepSort的特征提取器（OSNet）加载权重时，模型结构与权重文件不匹配，部分参数找不到。

### 2. 原因分析
- 权重文件与模型结构不兼容（如官方与第三方实现、不同版本、损坏等）。
- 代码或权重路径混用，或权重下载/解压不完整。

### 3. 官方权重下载与配置建议
- 推荐使用官方DeepSort/OSNet权重，确保与模型结构完全一致。
- 官方权重下载地址：
  - [KaiyangZhou/deep-person-reid](https://github.com/KaiyangZhou/deep-person-reid)（如osnet_x0_25_msmt17.pt/pth）
  - [ZQPei/deep_sort_pytorch](https://github.com/ZQPei/deep_sort_pytorch)（官方DeepSort权重）
- 下载后，将权重路径配置到`tracker/configs/deep_sort.yaml`的`REID_CKPT`字段，或在代码中指定。

### 4. 兼容性加载调试（仅限测试）
- 可在`load_state_dict`时加`strict=False`跳过部分参数不匹配，但不推荐用于生产环境。

### 5. 后续操作建议
1. 下载官方权重，替换本地权重文件。
2. 检查`feature_extractor.py`中OSNet模型结构与权重来源一致。
3. 检查`deep_sort.yaml`配置文件权重路径。
4. 如需自动检测权重与模型兼容性、权重下载脚本、或权重校验工具，可随时集成。

---

如需进一步集成自动化推理、批量评估、可视化、实验管理等功能，请随时吩咐！ 