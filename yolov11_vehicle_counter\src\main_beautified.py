
import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
from ultralytics import <PERSON>OL<PERSON>
from pathlib import Path
import numpy as np
from tracker.deep_sort import DeepSort
from utils.visualization import draw_fancy_bbox_label # Import our new visualization utility

# --- Configuration ---
track_history = {}
counted_ids = set()

def run_tracker(model_path, video_path, save_path, show_video=True):
    """Runs YOLOv11 object tracking, saves the output, and optionally displays it."""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    print(f"Loading model from: {model_path}")
    model = YOLO(model_path)

    # --- Initialize DeepSORT ---
    tracker = DeepSort(
        model='osnet_x0_25', # Changed from model_type to model
        max_dist=0.3,
        max_iou_distance=0.7,
        max_age=150, # Our key optimization
        n_init=3,
        nn_budget=100,
        device='cpu' # or 'cuda' if you have a GPU-enabled deepsort model
    )
    # ---

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)

    # --- Dashboard Configuration ---
    dashboard_height = 100
    dashboard_color = (0, 0, 0) # Black
    new_h = h + dashboard_height
    # ---

    # Setup video writer with the new dimensions
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, new_h))
    print(f"Output video will be saved to: {save_path}")

    frame_idx = 0
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("Video processing completed.")
            break
        frame_idx += 1

        # --- Create the main canvas with the dashboard ---
        main_canvas = np.zeros((new_h, w, 3), dtype=np.uint8)
        main_canvas[:dashboard_height, :] = dashboard_color
        main_canvas[dashboard_height:, :] = frame
        # ---

        # --- Manual Tracking Loop ---
        # 1. Get detections from YOLO
        results = model.predict(frame, verbose=False)
        detections = results[0].boxes.data.cpu().numpy()

        # 2. Format detections for DeepSORT
        xywhs = detections[:, :4]
        confs = detections[:, 4]
        clss = detections[:, 5]

        # 3. Update the tracker
        tracks = tracker.update(xywhs, confs, clss, frame)
        # ---

        # Draw the counting line
        cv2.line(main_canvas, (line_start[0], line_start[1] + dashboard_height), (line_end[0], line_end[1] + dashboard_height), (0, 255, 0), 2)

        if len(tracks) > 0:
            for track in tracks:
                if not track.is_confirmed() or track.time_since_update > 1:
                    continue
                
                bbox = track.to_tlbr() # Get bounding box in (top, left, bottom, right)
                track_id = track.track_id
                cls = track.get_class()
                confidence = track.confidence # Get confidence from track object

                # --- New: Draw fancy bounding box and label ---
                main_canvas = draw_fancy_bbox_label(main_canvas, bbox, track_id, model.names[int(cls)], confidence, dashboard_height)
                # ---

                # Counting logic
                center_x, center_y = int((bbox[0] + bbox[2]) / 2), int((bbox[1] + bbox[3]) / 2)
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                track_history[track_id] = (center_x, center_y)

        # --- Draw dashboard text ---
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1
        font_thickness = 2
        text_color = (255, 255, 255) # White

        cv2.putText(main_canvas, f"Left Count: {left_count}", (50, 60), font, font_scale, text_color, font_thickness)
        cv2.putText(main_canvas, f"Right Count: {right_count}", (w - 350, 60), font, font_scale, text_color, font_thickness)
        # ---

        if show_video:
            try:
                cv2.imshow("YOLOv11 Tracking and Counting", main_canvas)
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    break
            except cv2.error:
                print("(GUI not available) Cannot display video. Continuing to process and save the video.")
                show_video = False

        out.write(main_canvas)

    cap.release()
    out.release()
    cv2.destroyAllWindows()
    print(f"Final Counts -> Left: {left_count}, Right: {right_count}")
    print(f"Output video saved successfully to {save_path}")

if __name__ == '__main__':
    # --- Hard-coded Path Configuration with Smart Environment Detection ---
    local_model_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    local_save_path = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/output_beautified_local.mp4')

    server_model_path = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt')
    server_video_path = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    server_save_path = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/output_beautified_server.mp4')

    if local_model_path.exists():
        print("Local environment detected.")
        model_path = local_model_path
        video_path = local_video_path
        save_path = local_save_path
        show_video = False
    elif server_model_path.exists():
        print("Server environment detected.")
        model_path = server_model_path
        video_path = server_video_path
        save_path = server_save_path
        show_video = False
    else:
        print("Error: Could not find model files for either local or server environment.")
        exit()
    
    run_tracker(model_path, video_path, save_path, show_video=show_video)
