#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 清洁ID版本
- 重新映射ID，从1开始按顺序分配
- 清理界面，减少混乱
- 优化跟踪显示
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import argparse
from datetime import datetime

# --- Configuration ---
track_history = {}
counted_ids = set()
# ID映射管理
original_to_clean_id = {}  # 原始ID到清洁ID的映射
next_clean_id = 1          # 下一个可用的清洁ID

def get_clean_id(original_id):
    """获取清洁的ID（从1开始按顺序分配）"""
    global next_clean_id, original_to_clean_id
    
    if original_id not in original_to_clean_id:
        original_to_clean_id[original_id] = next_clean_id
        next_clean_id += 1
    
    return original_to_clean_id[original_id]

def create_clean_ui_overlay(frame, left_count, right_count, line_x):
    """创建清洁的UI覆盖层"""
    h, w = frame.shape[:2]
    
    # 创建半透明覆盖层用于显示计数信息
    overlay = frame.copy()
    
    # 左侧计数区域 - 深蓝色背景
    cv2.rectangle(overlay, (10, 10), (300, 120), (139, 69, 19), -1)  # 深蓝色
    # 右侧计数区域 - 深红色背景  
    cv2.rectangle(overlay, (w-310, 10), (w-10, 120), (19, 69, 139), -1)  # 深红色
    
    # 混合原图和覆盖层
    alpha = 0.85
    frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
    
    # 绘制超大字体计数信息
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 1.8
    thickness = 3
    
    # 左侧计数 - 白色大字
    cv2.putText(frame, "LEFT", (25, 45), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"{left_count:03d}", (25, 95), font, font_scale, (255, 255, 255), thickness)
    
    # 右侧计数 - 白色大字
    cv2.putText(frame, "RIGHT", (w-150, 45), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"{right_count:03d}", (w-150, 95), font, font_scale, (255, 255, 255), thickness)
    
    # 绘制清洁的计数线
    cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 4)
    
    # 计数线标签
    cv2.rectangle(frame, (line_x-50, h//2-15), (line_x+50, h//2+15), (0, 255, 0), -1)
    cv2.putText(frame, "COUNT", (line_x-45, h//2+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    return frame

def draw_clean_detections(frame, results, line_x):
    """绘制清洁的检测框，使用重新映射的ID"""
    if results[0].boxes is None or results[0].boxes.id is None:
        return frame
    
    boxes = results[0].boxes.xyxy.cpu().numpy()
    confidences = results[0].boxes.conf.cpu().numpy()
    class_ids = results[0].boxes.cls.cpu().numpy()
    original_track_ids = results[0].boxes.id.int().cpu().tolist()
    
    for box, conf, cls_id, original_id in zip(boxes, confidences, class_ids, original_track_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # 获取清洁的ID
        clean_id = get_clean_id(original_id)
        
        # 根据类别选择颜色和标签
        class_names = {0: 'person', 2: 'car', 3: 'motorcycle', 5: 'bus', 7: 'truck'}
        class_name = class_names.get(int(cls_id), f'class_{int(cls_id)}')
        
        # 为不同类别选择颜色
        if class_name == 'car':
            color = (0, 255, 255)  # 黄色
        elif class_name == 'truck':
            color = (255, 0, 255)  # 紫色
        elif class_name == 'bus':
            color = (255, 165, 0)  # 橙色
        elif class_name == 'motorcycle':
            color = (0, 255, 0)    # 绿色
        elif class_name == 'person':
            color = (255, 255, 255)  # 白色
        else:
            color = (128, 128, 128)  # 灰色
        
        # 绘制检测框
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        
        # 计算中心点
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # 绘制中心点
        cv2.circle(frame, (center_x, center_y), 4, color, -1)
        
        # 绘制轨迹（使用原始ID作为键）
        if original_id in track_history:
            prev_x, prev_y = track_history[original_id]
            cv2.line(frame, (prev_x, prev_y), (center_x, center_y), color, 2)
        
        # 绘制清洁的标签
        label = f"ID:{clean_id} {class_name}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        
        # 标签背景
        cv2.rectangle(frame, (x1, y1-25), (x1 + label_size[0] + 10, y1), color, -1)
        
        # 标签文字 - 使用黑色文字确保可见性
        cv2.putText(frame, label, (x1+5, y1-8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 如果靠近计数线，高亮显示
        if abs(center_x - line_x) < 30:
            cv2.rectangle(frame, (x1-3, y1-3), (x2+3, y2+3), (0, 0, 255), 2)
    
    return frame

def run_tracker(model_path, video_path, save_path, show_video=True):
    """运行清洁ID的车辆跟踪器"""
    global track_history, counted_ids, original_to_clean_id, next_clean_id
    
    # 重置全局变量
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()
    original_to_clean_id.clear()
    next_clean_id = 1

    print(f"🚗 加载模型: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ 无法打开视频文件: {video_path}")
        return

    # 获取视频属性
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
    
    line_x = w // 2
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"💾 输出视频: {save_path}")

    frame_count = 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("✅ 视频处理完成")
            break

        frame_count += 1
        
        # YOLO跟踪
        results = model.track(frame, persist=True, verbose=False)
        
        # 绘制清洁的检测结果
        annotated_frame = draw_clean_detections(frame.copy(), results, line_x)
        
        # 处理跟踪结果（使用原始ID进行计数逻辑）
        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            original_track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, original_id in zip(boxes, original_track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                
                # 计数逻辑（使用原始ID）
                if original_id in track_history:
                    prev_x, _ = track_history[original_id]
                    if original_id not in counted_ids:
                        clean_id = get_clean_id(original_id)
                        # 从左到右穿越
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(original_id)
                            print(f"🚙 车辆 ID:{clean_id} 从左到右穿越，右侧计数: {right_count}")
                        # 从右到左穿越
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(original_id)
                            print(f"🚗 车辆 ID:{clean_id} 从右到左穿越，左侧计数: {left_count}")
                
                # 更新历史位置（使用原始ID）
                track_history[original_id] = (center_x, center_y)

        # 创建清洁UI覆盖层
        annotated_frame = create_clean_ui_overlay(annotated_frame, left_count, right_count, line_x)
        
        # 添加简洁的进度信息
        progress = frame_count / total_frames if total_frames > 0 else 0
        progress_text = f"Frame: {frame_count}/{total_frames} ({progress*100:.1f}%)"
        cv2.putText(annotated_frame, progress_text, (10, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 显示当前活跃ID数量
        active_ids = len(original_to_clean_id)
        id_text = f"Active IDs: {active_ids}"
        cv2.putText(annotated_frame, id_text, (10, h-45), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 显示视频
        if show_video:
            try:
                cv2.imshow("YOLOv11 Clean ID Vehicle Counter", annotated_frame)
                key = cv2.waitKey(1) & 0xFF
                if key == ord("q"):
                    print("🛑 用户请求退出")
                    break
                elif key == ord("r"):
                    # 重置ID映射
                    original_to_clean_id.clear()
                    next_clean_id = 1
                    print("🔄 ID映射已重置")
            except cv2.error:
                print("⚠️ GUI不可用，继续处理但不显示窗口")
                show_video = False

        # 保存帧
        out.write(annotated_frame)
        
        # 进度显示
        if frame_count % 100 == 0:
            print(f"📊 进度: {frame_count}/{total_frames} ({progress*100:.1f}%) | 左侧: {left_count} | 右侧: {right_count} | 活跃ID: {active_ids}")

    # 清理资源
    cap.release()
    out.release()
    
    # 安全关闭窗口
    try:
        cv2.destroyAllWindows()
    except:
        pass
    
    # 最终结果
    print("=" * 70)
    print("🎉 车辆计数完成！")
    print(f"📊 最终统计:")
    print(f"   ⬅️  左侧通过: {left_count:3d} 辆")
    print(f"   ➡️  右侧通过: {right_count:3d} 辆")
    print(f"   🚗 总计车辆: {left_count + right_count:3d} 辆")
    print(f"📹 处理帧数: {frame_count:,} 帧")
    print(f"🆔 最大ID号: {next_clean_id - 1}")
    print(f"💾 输出文件: {save_path}")
    print("=" * 70)
    print("💡 提示: 播放时按 'q' 退出，按 'r' 重置ID")

if __name__ == '__main__':
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_clean_id_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - 清洁ID版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - 清洁ID版本")
    print("✨ 特性:")
    print("   🆔 ID从1开始按顺序分配")
    print("   🧹 清洁界面，减少混乱")
    print("   📱 大字体清晰计数显示")
    print("   🎨 优化的跟踪可视化")
    print("=" * 50)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 50)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)