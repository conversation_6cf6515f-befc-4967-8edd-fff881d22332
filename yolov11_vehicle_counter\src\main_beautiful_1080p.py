#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 1080p美观版本
专门为1080p视频优化UI布局和颜色
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import argparse
from datetime import datetime

# --- Configuration ---
track_history = {}
vehicle_crossed_ids = set()
person_seen_ids = set()
original_to_clean_id = {}
next_clean_id = 1

# COCO类别映射
CLASS_NAMES = {
    0: 'person',
    1: 'bicycle', 
    2: 'car',
    3: 'motorcycle',
    4: 'airplane',
    5: 'bus',
    6: 'train',
    7: 'truck',
    8: 'boat'
}

def get_clean_id(original_id):
    """获取清洁的ID（从1开始按顺序分配）"""
    global next_clean_id, original_to_clean_id
    
    if original_id not in original_to_clean_id:
        original_to_clean_id[original_id] = next_clean_id
        next_clean_id += 1
    
    return original_to_clean_id[original_id]

def is_vehicle(class_id):
    """判断是否为车辆"""
    vehicle_classes = {1, 2, 3, 5, 7}  # bicycle, car, motorcycle, bus, truck
    return int(class_id) in vehicle_classes

def create_beautiful_ui_overlay(frame, vehicle_left, vehicle_right, person_total, line_x):
    """创建1080p优化的美观UI覆盖层"""
    h, w = frame.shape[:2]
    
    # 创建半透明覆盖层
    overlay = frame.copy()
    
    # 左上角 - 车辆穿越统计区域 (适配1080p)
    cv2.rectangle(overlay, (10, 10), (320, 120), (40, 40, 139), -1)  # 深蓝色
    # 右上角 - 人员总数统计区域 (确保在1080p范围内)
    cv2.rectangle(overlay, (w-330, 10), (w-10, 120), (40, 139, 40), -1)  # 深绿色
    
    # 混合原图和覆盖层
    alpha = 0.8
    frame = cv2.addWeighted(frame, alpha, overlay, 1-alpha, 0)
    
    # 字体设置 - 适配1080p
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 1.2
    thickness = 2
    
    # 左上角 - 车辆穿越统计
    cv2.putText(frame, "VEHICLES", (20, 35), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"L:{vehicle_left:02d} R:{vehicle_right:02d}", (20, 65), font, 0.9, (255, 255, 255), 2)
    cv2.putText(frame, f"Total: {vehicle_left + vehicle_right:02d}", (20, 95), font, font_scale, (0, 255, 255), thickness+1)
    
    # 右上角 - 人员总数统计
    cv2.putText(frame, "PEOPLE", (w-310, 35), font, 0.8, (255, 255, 255), 2)
    cv2.putText(frame, f"Total: {person_total:02d}", (w-250, 75), font, font_scale, (255, 255, 255), thickness+1)
    
    # 绘制计数线 - 更明显的颜色
    cv2.line(frame, (line_x, 0), (line_x, h), (0, 255, 0), 5)
    
    # 计数线标签 - 移到顶部中央
    label_y = 10
    cv2.rectangle(frame, (line_x-60, label_y), (line_x+60, label_y+30), (0, 255, 0), -1)
    cv2.putText(frame, "COUNT LINE", (line_x-55, label_y+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
    
    # 添加方向箭头 - 在计数线两侧
    arrow_y = 50
    # 左箭头
    arrow_left = np.array([[line_x-80, arrow_y], [line_x-60, arrow_y-8], [line_x-60, arrow_y+8]], np.int32)
    cv2.fillPoly(frame, [arrow_left], (255, 255, 0))
    cv2.putText(frame, "L", (line_x-90, arrow_y+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
    
    # 右箭头  
    arrow_right = np.array([[line_x+80, arrow_y], [line_x+60, arrow_y-8], [line_x+60, arrow_y+8]], np.int32)
    cv2.fillPoly(frame, [arrow_right], (255, 255, 0))
    cv2.putText(frame, "R", (line_x+85, arrow_y+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
    
    return frame

def draw_beautiful_detections(frame, results, line_x):
    """绘制美观的检测框，使用鲜艳颜色"""
    if results[0].boxes is None or results[0].boxes.id is None:
        return frame
    
    boxes = results[0].boxes.xyxy.cpu().numpy()
    confidences = results[0].boxes.conf.cpu().numpy()
    class_ids = results[0].boxes.cls.cpu().numpy()
    original_track_ids = results[0].boxes.id.int().cpu().tolist()
    
    for box, conf, cls_id, original_id in zip(boxes, confidences, class_ids, original_track_ids):
        x1, y1, x2, y2 = map(int, box)
        
        # 获取清洁的ID
        clean_id = get_clean_id(original_id)
        
        # 获取正确的类别名称
        class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
        
        # 为不同类别选择鲜艳颜色
        if class_name == 'car':
            color = (0, 255, 255)  # 亮黄色
        elif class_name == 'truck':
            color = (255, 0, 255)  # 亮紫色
        elif class_name == 'bus':
            color = (0, 165, 255)  # 亮橙色
        elif class_name == 'motorcycle':
            color = (0, 255, 0)    # 亮绿色
        elif class_name == 'bicycle':
            color = (255, 192, 203)  # 亮粉色
        elif class_name == 'person':
            color = (255, 255, 255)  # 白色
        else:
            color = (128, 128, 255)  # 亮灰色
        
        # 绘制检测框 - 更粗更明显
        thickness = 4 if is_vehicle(cls_id) else 3
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
        
        # 计算中心点
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # 绘制中心点 - 更大更明显
        cv2.circle(frame, (center_x, center_y), 8, color, -1)
        cv2.circle(frame, (center_x, center_y), 8, (0, 0, 0), 2)  # 黑色边框
        
        # 绘制轨迹
        if original_id in track_history:
            prev_x, prev_y = track_history[original_id]
            cv2.line(frame, (prev_x, prev_y), (center_x, center_y), color, 3)
        
        # 绘制标签 - 更大更清晰
        label = f"ID:{clean_id} {class_name}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
        
        # 标签背景 - 更大的背景
        cv2.rectangle(frame, (x1, y1-35), (x1 + label_size[0] + 15, y1), color, -1)
        cv2.rectangle(frame, (x1, y1-35), (x1 + label_size[0] + 15, y1), (0, 0, 0), 2)  # 黑色边框
        
        # 标签文字 - 更大的字体
        cv2.putText(frame, label, (x1+7, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        # 如果是车辆且靠近计数线，高亮显示
        if is_vehicle(cls_id) and abs(center_x - line_x) < 50:
            cv2.rectangle(frame, (x1-8, y1-8), (x2+8, y2+8), (0, 0, 255), 4)
            # 添加闪烁效果
            cv2.putText(frame, "COUNTING!", (x1, y2+25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
    
    return frame

def run_tracker(model_path, video_path, save_path, show_video=True):
    """运行1080p优化的美观车辆跟踪器"""
    global track_history, vehicle_crossed_ids, person_seen_ids, original_to_clean_id, next_clean_id
    
    # 重置全局变量
    vehicle_left_count, vehicle_right_count = 0, 0
    track_history.clear()
    vehicle_crossed_ids.clear()
    person_seen_ids.clear()
    original_to_clean_id.clear()
    next_clean_id = 1

    print(f"🚗 加载模型: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ 无法打开视频文件: {video_path}")
        return

    # 获取视频属性
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 视频信息: {w}x{h}, {fps}fps, {total_frames}帧")
    
    line_x = w // 2
    print(f"📍 计数线位置: x = {line_x}")
    
    # 设置视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"💾 输出视频: {save_path}")

    frame_count = 0
    
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("✅ 视频处理完成")
            break

        frame_count += 1
        
        # YOLO跟踪
        results = model.track(frame, persist=True, verbose=False)
        
        # 绘制美观的检测结果
        annotated_frame = draw_beautiful_detections(frame.copy(), results, line_x)
        
        # 处理跟踪结果
        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            class_ids = results[0].boxes.cls.cpu().numpy()
            original_track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, cls_id, original_id in zip(boxes, class_ids, original_track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                clean_id = get_clean_id(original_id)
                class_name = CLASS_NAMES.get(int(cls_id), f'unknown_{int(cls_id)}')
                
                # 人员统计 - 按ID去重
                if class_name == 'person':
                    if original_id not in person_seen_ids:
                        person_seen_ids.add(original_id)
                        print(f"👤 发现新人员 ID:{clean_id}")
                
                # 车辆穿越统计
                elif is_vehicle(cls_id):
                    if original_id in track_history:
                        prev_x, _ = track_history[original_id]
                        if original_id not in vehicle_crossed_ids:
                            # 从左到右穿越
                            if prev_x < line_x and center_x >= line_x:
                                vehicle_right_count += 1
                                vehicle_crossed_ids.add(original_id)
                                print(f"🚙 车辆 ID:{clean_id} ({class_name}) 从左到右穿越，右侧计数: {vehicle_right_count}")
                            # 从右到左穿越
                            elif prev_x > line_x and center_x <= line_x:
                                vehicle_left_count += 1
                                vehicle_crossed_ids.add(original_id)
                                print(f"🚗 车辆 ID:{clean_id} ({class_name}) 从右到左穿越，左侧计数: {vehicle_left_count}")
                
                # 更新历史位置
                track_history[original_id] = (center_x, center_y)

        # 创建美观UI覆盖层
        annotated_frame = create_beautiful_ui_overlay(
            annotated_frame, 
            vehicle_left_count, 
            vehicle_right_count, 
            len(person_seen_ids), 
            line_x
        )
        
        # 添加底部状态栏 - 适配1080p
        status_y = h - 40
        cv2.rectangle(annotated_frame, (0, status_y), (w, h), (0, 0, 0), -1)
        
        # 状态信息 - 简洁版
        progress = frame_count / total_frames if total_frames > 0 else 0
        status_text = f"Frame: {frame_count}/{total_frames} ({progress*100:.1f}%) | Vehicles: {vehicle_left_count + vehicle_right_count} | People: {len(person_seen_ids)} | IDs: {len(original_to_clean_id)}"
        cv2.putText(annotated_frame, status_text, (10, status_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 显示视频
        if show_video:
            try:
                cv2.imshow("YOLOv11 Beautiful 1080p Vehicle Counter", annotated_frame)
                key = cv2.waitKey(1) & 0xFF
                if key == ord("q"):
                    print("🛑 用户请求退出")
                    break
                elif key == ord("r"):
                    # 重置所有计数
                    original_to_clean_id.clear()
                    next_clean_id = 1
                    vehicle_crossed_ids.clear()
                    person_seen_ids.clear()
                    vehicle_left_count = vehicle_right_count = 0
                    track_history.clear()
                    print("🔄 所有计数已重置")
            except cv2.error:
                print("⚠️ GUI不可用，继续处理但不显示窗口")
                show_video = False

        # 保存帧
        out.write(annotated_frame)
        
        # 进度显示
        if frame_count % 100 == 0:
            print(f"📊 进度: {frame_count}/{total_frames} ({progress*100:.1f}%) | 车辆穿越: {vehicle_left_count + vehicle_right_count} | 人员总数: {len(person_seen_ids)}")

    # 清理资源
    cap.release()
    out.release()
    
    # 安全关闭窗口
    try:
        cv2.destroyAllWindows()
    except:
        pass
    
    # 最终结果
    print("=" * 80)
    print("🎉 1080p美观版本统计完成！")
    print(f"📊 车辆穿越统计:")
    print(f"   ⬅️  左侧穿越: {vehicle_left_count:3d} 辆")
    print(f"   ➡️  右侧穿越: {vehicle_right_count:3d} 辆")
    print(f"   🚗 车辆总计: {vehicle_left_count + vehicle_right_count:3d} 辆")
    print(f"👥 人员检测统计:")
    print(f"   👤 人员总数: {len(person_seen_ids):3d} 人")
    print(f"📹 处理帧数: {frame_count:,} 帧")
    print(f"🆔 最大ID号: {next_clean_id - 1}")
    print(f"💾 输出文件: {save_path}")
    print("=" * 80)

if __name__ == '__main__':
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_beautiful_1080p_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - 1080p美观版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - 1080p美观版本")
    print("✨ 特性:")
    print("   🎨 专为1080p优化的美观UI")
    print("   🌈 鲜艳明显的检测框颜色")
    print("   📱 紧凑的统计显示布局")
    print("   🚗 车辆穿越计数")
    print("   👥 人员总数统计")
    print("   🆔 清洁ID显示")
    print("=" * 60)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 60)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)