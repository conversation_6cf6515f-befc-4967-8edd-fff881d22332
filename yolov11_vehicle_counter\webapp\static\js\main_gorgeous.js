// YOLOv11车辆计数器 - 华丽版JavaScript

class GorgeousVehicleCounter {
    constructor() {
        this.currentTaskId = null;
        this.processingInterval = null;
        this.startTime = null;
        this.originalVideoFile = null;
        
        this.initializeElements();
        this.bindEvents();
        this.createParticles();
        this.updateUI();
    }

    initializeElements() {
        this.elements = {
            // 上传相关
            uploadZone: document.getElementById('uploadZone'),
            videoFile: document.getElementById('videoFile'),
            processBtn: document.getElementById('processBtn'),
            loadingIcon: document.getElementById('loadingIcon'),
            
            // 视频相关
            mainVideoContainer: document.getElementById('mainVideoContainer'),
            mainVideoPlaceholder: document.getElementById('mainVideoPlaceholder'),
            mainVideo: document.getElementById('mainVideo'),
            originalComparison: document.getElementById('originalComparison'),
            processedComparison: document.getElementById('processedComparison'),
            originalVideo: document.getElementById('originalVideo'),
            processedVideo: document.getElementById('processedVideo'),
            originalVideoPlaceholder: document.getElementById('originalVideoPlaceholder'),
            processedVideoPlaceholder: document.getElementById('processedVideoPlaceholder'),
            
            // 统计相关
            vehicleCount: document.getElementById('vehicleCount'),
            currentFps: document.getElementById('currentFps'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            statusMessage: document.getElementById('statusMessage'),
            statusText: document.getElementById('statusText'),
            
            // 结果相关
            resultsSummary: document.getElementById('resultsSummary'),
            leftCount: document.getElementById('leftCount'),
            rightCount: document.getElementById('rightCount'),
            frameCount: document.getElementById('frameCount'),
            processTime: document.getElementById('processTime'),
            avgAccuracy: document.getElementById('avgAccuracy'),
            
            // 下载相关
            downloadVideo: document.getElementById('downloadVideo'),
            downloadReport: document.getElementById('downloadReport'),
            downloadData: document.getElementById('downloadData'),
            shareResults: document.getElementById('shareResults'),
            
            // 设置相关
            confidenceSlider: document.getElementById('confidenceSlider'),
            confidenceValue: document.getElementById('confidenceValue'),
            speedSelect: document.getElementById('speedSelect'),
            modelSelect: document.getElementById('modelSelect'),
            showTrajectory: document.getElementById('showTrajectory'),
            showCountLine: document.getElementById('showCountLine'),
            showBoundingBox: document.getElementById('showBoundingBox')
        };
    }

    bindEvents() {
        // 文件上传事件
        this.elements.videoFile.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // 上传按钮点击事件 - 修复点击没反应的问题
        const uploadBtn = document.getElementById('uploadBtn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🖱️ 点击选择文件按钮');
                this.elements.videoFile.click();
            });
        } else {
            console.error('❌ 找不到上传按钮元素');
        }
        
        // 视频同步播放事件
        this.setupVideoSync();
        
        // 拖拽上传事件
        this.elements.uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.elements.uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.elements.uploadZone.addEventListener('drop', (e) => this.handleDrop(e));
        
        // 处理按钮事件
        this.elements.processBtn.addEventListener('click', () => this.startProcessing());
        
        // 置信度滑块事件
        this.elements.confidenceSlider.addEventListener('input', (e) => {
            this.elements.confidenceValue.textContent = e.target.value;
        });
        
        // 下载按钮事件
        this.elements.downloadVideo.addEventListener('click', () => this.downloadVideo());
        this.elements.downloadReport.addEventListener('click', () => this.downloadReport());
        this.elements.downloadData.addEventListener('click', () => this.downloadData());
        this.elements.shareResults.addEventListener('click', () => this.shareResults());
    }

    createParticles() {
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 15 + 's';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            particlesContainer.appendChild(particle);
        }
    }
    
    setupVideoSync() {
        // 设置视频同步播放
        if (this.elements.originalVideo && this.elements.processedVideo) {
            // 原始视频控制处理后视频
            this.elements.originalVideo.addEventListener('play', () => {
                if (this.elements.processedVideo.paused) {
                    this.elements.processedVideo.play();
                }
            });
            
            this.elements.originalVideo.addEventListener('pause', () => {
                if (!this.elements.processedVideo.paused) {
                    this.elements.processedVideo.pause();
                }
            });
            
            this.elements.originalVideo.addEventListener('seeked', () => {
                this.elements.processedVideo.currentTime = this.elements.originalVideo.currentTime;
            });
            
            // 处理后视频控制原始视频
            this.elements.processedVideo.addEventListener('play', () => {
                if (this.elements.originalVideo.paused) {
                    this.elements.originalVideo.play();
                }
            });
            
            this.elements.processedVideo.addEventListener('pause', () => {
                if (!this.elements.originalVideo.paused) {
                    this.elements.originalVideo.pause();
                }
            });
            
            this.elements.processedVideo.addEventListener('seeked', () => {
                this.elements.originalVideo.currentTime = this.elements.processedVideo.currentTime;
            });
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        this.elements.uploadZone.style.borderColor = 'rgba(255, 255, 255, 0.8)';
        this.elements.uploadZone.style.background = 'rgba(255, 255, 255, 0.15)';
        this.elements.uploadZone.style.transform = 'scale(1.05)';
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.elements.uploadZone.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        this.elements.uploadZone.style.background = 'rgba(255, 255, 255, 0.05)';
        this.elements.uploadZone.style.transform = 'scale(1)';
    }

    handleDrop(e) {
        e.preventDefault();
        this.handleDragLeave(e);
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.handleFile(file);
        }
    }

    handleFile(file) {
        // 验证文件类型
        if (!file.type.startsWith('video/')) {
            this.showMessage('请选择视频文件', 'error');
            return;
        }

        // 验证文件大小 (500MB限制)
        if (file.size > 500 * 1024 * 1024) {
            this.showMessage('文件大小不能超过500MB', 'error');
            return;
        }

        this.originalVideoFile = file;

        // 更新上传区域UI
        this.elements.uploadZone.innerHTML = `
            <div class="upload-icon">✅</div>
            <div class="upload-text">已选择: ${file.name}</div>
            <div style="font-size: 0.9em; color: rgba(255,255,255,0.7); margin-top: 10px;">
                大小: ${this.formatFileSize(file.size)}
            </div>
            <div style="margin-top: 15px;">
                <button class="upload-btn" onclick="document.getElementById('videoFile').click()">
                    重新选择
                </button>
            </div>
        `;
        
        // 显示视频预览
        this.showVideoPreview(file);
        
        this.elements.processBtn.disabled = false;
        this.showMessage('文件上传成功，可以开始AI分析', 'success');
    }

    showVideoPreview(file) {
        const url = URL.createObjectURL(file);
        this.elements.mainVideo.src = url;
        this.elements.mainVideo.style.display = 'block';
        this.elements.mainVideoPlaceholder.style.display = 'none';
        
        // 显示原始视频在对比区域
        this.elements.originalVideo.src = url;
        this.elements.originalVideo.style.display = 'block';
        this.elements.originalVideoPlaceholder.style.display = 'none';
        
        // 重新设置视频同步
        this.setupVideoSync();
    }

    async startProcessing() {
        try {
            this.elements.processBtn.disabled = true;
            this.elements.loadingIcon.style.display = 'inline-block';
            this.elements.processBtn.innerHTML = '<span class="loading-spinner"></span> AI分析中...';
            
            this.startTime = Date.now();
            
            // 上传文件
            const formData = new FormData();
            formData.append('video', this.originalVideoFile);
            
            this.showMessage('正在上传文件到AI服务器...', 'info');
            
            const uploadResponse = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });
            
            if (!uploadResponse.ok) {
                throw new Error('文件上传失败');
            }
            
            const uploadResult = await uploadResponse.json();
            this.currentTaskId = uploadResult.task_id;
            
            // 开始处理
            const settings = {
                model: this.elements.modelSelect.value,
                confidence: parseFloat(this.elements.confidenceSlider.value),
                speed: this.elements.speedSelect.value,
                show_trajectory: this.elements.showTrajectory.checked,
                show_count_line: this.elements.showCountLine.checked,
                show_bounding_box: this.elements.showBoundingBox.checked
            };
            
            const processResponse = await fetch('/api/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    task_id: this.currentTaskId,
                    settings: settings
                })
            });
            
            if (!processResponse.ok) {
                throw new Error('AI处理启动失败');
            }
            
            this.showMessage('AI智能分析已开始...', 'info');
            this.startStatusPolling();
            
        } catch (error) {
            this.showMessage(`错误: ${error.message}`, 'error');
            this.resetProcessButton();
        }
    }

    startStatusPolling() {
        this.processingInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/status/${this.currentTaskId}`);
                if (!response.ok) {
                    throw new Error('获取状态失败');
                }
                
                const status = await response.json();
                this.updateStatus(status);
                
                if (status.status === 'completed') {
                    this.handleProcessingComplete();
                } else if (status.status === 'error') {
                    this.handleProcessingError(status.message);
                }
                
            } catch (error) {
                console.error('状态轮询错误:', error);
            }
        }, 500); // 减少到500ms，提高实时性
    }

    updateStatus(status) {
        // 更新进度条
        this.elements.progressFill.style.width = `${status.progress}%`;
        this.elements.progressText.textContent = `${status.progress.toFixed(1)}% - ${status.message}`;
        
        // 实时更新统计数据 - 优化响应速度
        const vehicleCount = status.vehicle_count || 0;
        const leftCount = status.vehicle_left || 0;
        const rightCount = status.vehicle_right || 0;
        
        // 立即更新显示，添加动画效果
        if (this.elements.vehicleCount.textContent !== vehicleCount.toString()) {
            this.elements.vehicleCount.textContent = vehicleCount;
            this.elements.vehicleCount.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.elements.vehicleCount.style.transform = 'scale(1)';
            }, 200);
        }
        
        // 更新左右计数
        if (this.elements.leftCount) {
            this.elements.leftCount.textContent = leftCount;
        }
        if (this.elements.rightCount) {
            this.elements.rightCount.textContent = rightCount;
        }
        
        this.elements.currentFps.textContent = (status.current_fps || 0).toFixed(1);
        
        // 更新处理时间
        if (this.startTime) {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            this.elements.processTime.textContent = `${elapsed}s`;
        }
        
        // 显示状态消息
        this.elements.statusText.textContent = status.message;
        this.elements.statusMessage.style.display = 'block';
        
        // 添加进度动画效果
        if (status.progress > 0) {
            this.elements.progressFill.style.boxShadow = '0 0 20px rgba(250, 112, 154, 0.6)';
        }
        
        // 保存当前状态，防止数据丢失
        this.lastStatus = status;
    }

    async handleProcessingComplete() {
        clearInterval(this.processingInterval);
        
        try {
            // 获取处理结果
            const response = await fetch(`/api/results/${this.currentTaskId}`);
            if (!response.ok) {
                throw new Error('获取结果失败');
            }
            
            const results = await response.json();
            this.displayResults(results);
            
            this.showMessage('AI分析完成！🎉', 'success');
            this.resetProcessButton();
            
        } catch (error) {
            this.showMessage(`获取结果失败: ${error.message}`, 'error');
        }
    }

    displayResults(results) {
        // 显示结果摘要 - 保持数据不归零
        const leftCount = results.vehicle_left || 0;
        const rightCount = results.vehicle_right || 0;
        const totalCount = leftCount + rightCount;
        
        this.elements.leftCount.textContent = leftCount;
        this.elements.rightCount.textContent = rightCount;
        this.elements.vehicleCount.textContent = totalCount; // 确保总数不归零
        this.elements.frameCount.textContent = results.processed_frames || 0;
        this.elements.avgAccuracy.textContent = ((results.avg_confidence || 0) * 100).toFixed(1) + '%';
        this.elements.resultsSummary.style.display = 'block';
        
        // 显示处理后的视频 - 使用正确的预览端点
        const resultVideoUrl = `/api/preview/${this.currentTaskId}`;
        
        // 设置处理后的视频
        this.elements.processedVideo.src = resultVideoUrl;
        this.elements.processedVideo.style.display = 'block';
        this.elements.processedVideoPlaceholder.style.display = 'none';
        
        // 重新设置视频同步播放
        setTimeout(() => {
            this.setupVideoSync();
        }, 500);
        
        console.log('✅ 处理后视频已加载:', resultVideoUrl);
        console.log('🎯 最终计数结果 - 总计:', totalCount, '左:', leftCount, '右:', rightCount);
        
        // 启用下载按钮
        this.elements.downloadVideo.disabled = false;
        this.elements.downloadReport.disabled = false;
        this.elements.downloadData.disabled = false;
        this.elements.shareResults.disabled = false;
        
        // 添加完成动画效果
        this.elements.resultsSummary.style.animation = 'fadeInUp 0.6s ease-out';
        this.elements.processedComparison.style.animation = 'fadeInRight 0.8s ease-out';
        
        // 添加数字变化动画
        this.animateCounterChange(this.elements.vehicleCount, totalCount);
    }
    
    animateCounterChange(element, finalValue) {
        // 数字变化动画效果
        element.style.transform = 'scale(1.2)';
        element.style.color = '#4facfe';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 300);
    }

    handleProcessingError(message) {
        clearInterval(this.processingInterval);
        this.showMessage(`AI处理失败: ${message}`, 'error');
        this.resetProcessButton();
    }

    resetProcessButton() {
        this.elements.processBtn.disabled = false;
        this.elements.loadingIcon.style.display = 'none';
        this.elements.processBtn.innerHTML = '开始智能分析';
    }

    async downloadVideo() {
        if (!this.currentTaskId) return;
        
        try {
            const link = document.createElement('a');
            link.href = `/api/download/${this.currentTaskId}`;
            link.download = `ai_processed_video_${this.currentTaskId}.mp4`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showMessage('AI处理视频下载已开始', 'success');
        } catch (error) {
            this.showMessage('下载失败', 'error');
        }
    }

    async downloadReport() {
        if (!this.currentTaskId) return;
        
        try {
            const response = await fetch(`/api/results/${this.currentTaskId}`);
            const results = await response.json();
            
            const report = this.generateDetailedReport(results);
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `ai_analysis_report_${this.currentTaskId}.txt`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            this.showMessage('AI分析报告下载已开始', 'success');
        } catch (error) {
            this.showMessage('报告生成失败', 'error');
        }
    }

    async downloadData() {
        if (!this.currentTaskId) return;
        
        try {
            const response = await fetch(`/api/results/${this.currentTaskId}`);
            const results = await response.json();
            
            const dataBlob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `ai_raw_data_${this.currentTaskId}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            this.showMessage('原始数据下载已开始', 'success');
        } catch (error) {
            this.showMessage('数据下载失败', 'error');
        }
    }

    shareResults() {
        if (!this.currentTaskId) return;
        
        const shareUrl = `${window.location.origin}/results/${this.currentTaskId}`;
        const shareText = `查看我的YOLOv11 AI车辆分析结果: 检测到${this.elements.vehicleCount.textContent}辆车辆`;
        
        if (navigator.share) {
            navigator.share({
                title: 'YOLOv11 AI车辆分析结果',
                text: shareText,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(`${shareText} ${shareUrl}`).then(() => {
                this.showMessage('分享链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.showMessage('分享功能不可用', 'error');
            });
        }
    }

    generateDetailedReport(results) {
        const timestamp = new Date().toLocaleString('zh-CN');
        const totalVehicles = (results.vehicle_left || 0) + (results.vehicle_right || 0);
        
        return `
╔══════════════════════════════════════════════════════════════╗
║                    YOLOv11 AI车辆分析报告                      ║
╚══════════════════════════════════════════════════════════════╝

📊 分析概览
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
生成时间: ${timestamp}
任务编号: ${this.currentTaskId}
原始文件: ${this.originalVideoFile ? this.originalVideoFile.name : 'N/A'}

🚗 检测结果
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
车辆总数: ${totalVehicles} 辆
├─ 左侧穿越: ${results.vehicle_left || 0} 辆 (${totalVehicles > 0 ? ((results.vehicle_left || 0) / totalVehicles * 100).toFixed(1) : 0}%)
└─ 右侧穿越: ${results.vehicle_right || 0} 辆 (${totalVehicles > 0 ? ((results.vehicle_right || 0) / totalVehicles * 100).toFixed(1) : 0}%)

⚡ 性能指标
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
处理帧数: ${results.processed_frames || 0} 帧
平均FPS: ${(results.avg_fps || 0).toFixed(2)} fps
处理时长: ${results.processing_time || 'N/A'}
平均精度: ${((results.avg_confidence || 0) * 100).toFixed(1)}%

🔧 技术参数
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
AI模型: ${this.elements.modelSelect.value.toUpperCase()}
置信度阈值: ${this.elements.confidenceSlider.value}
处理模式: ${this.elements.speedSelect.value}
显示轨迹: ${this.elements.showTrajectory.checked ? '✓' : '✗'}
显示计数线: ${this.elements.showCountLine.checked ? '✓' : '✗'}
显示检测框: ${this.elements.showBoundingBox.checked ? '✓' : '✗'}

📝 分析说明
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• 车辆计数基于AI深度学习模型YOLOv11进行目标检测
• 计数统计通过智能轨迹跟踪和穿越线检测实现
• 检测框、轨迹线和计数线已标注在输出视频中
• 所有数据均为AI自动分析结果，准确率受视频质量影响

🎯 系统信息
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
分析引擎: YOLOv11 + OpenCV
开发框架: Python Flask + JavaScript
界面设计: 华丽版现代化UI
数据格式: MP4视频 + JSON数据

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
报告生成完毕 | Powered by YOLOv11 AI车辆计数器
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
`;
    }

    showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessages = document.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());
        
        // 创建新消息
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        
        // 插入到控制面板中
        const controlPanel = document.querySelector('.control-panel');
        controlPanel.appendChild(messageDiv);
        
        // 添加动画效果
        messageDiv.style.animation = 'slideInDown 0.4s ease-out';
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOutUp 0.4s ease-in';
                setTimeout(() => messageDiv.remove(), 400);
            }
        }, 3000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateUI() {
        // 初始化UI状态
        this.elements.processBtn.disabled = true;
        this.elements.resultsSummary.style.display = 'none';
        this.elements.statusMessage.style.display = 'none';
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from { opacity: 0; transform: translateY(30px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes fadeInRight {
                from { opacity: 0; transform: translateX(30px); }
                to { opacity: 1; transform: translateX(0); }
            }
            @keyframes slideInDown {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes slideOutUp {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-20px); }
            }
        `;
        document.head.appendChild(style);
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.gorgeousCounter = new GorgeousVehicleCounter();
    console.log('✅ GorgeousVehicleCounter 已初始化');
});