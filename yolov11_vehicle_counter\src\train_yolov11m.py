import os
from pathlib import Path
from ultralytics import YOLO

# This line MUST be at the top of your script, before any other imports
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

if __name__ == '__main__':
    # --- Dynamic Path Configuration ---
    # Get the absolute path of the project root directory (lkr_yolo)
    # This makes the script portable and work on any machine
    ROOT_DIR = Path(__file__).resolve().parents[2]

    # Define paths to model and data YAML based on the root directory
    model_path = ROOT_DIR / 'yolov11_vehicle_counter/models/weights/yolo11m.pt'
    data_yaml_path = ROOT_DIR / 'yolov11_vehicle_counter/dataset/split_dataset/data.yaml'
    # --- End of Dynamic Path Configuration ---

    # Load a model
    model = YOLO(model_path)  # Use the dynamically constructed path

    # Use the model
    results = model.train(
        data=data_yaml_path, # Use the dynamically constructed path
        epochs=1000,
        patience=200,
        batch=4,
        imgsz=1024,
        project='runs/train',
        name='yolov11m_vehicle_detection_portable', # New name for clarity
        device=0,
        workers=8,
        cache=False
    )