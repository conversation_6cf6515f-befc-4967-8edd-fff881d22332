@echo off
chcp 65001 >nul
echo ======================================================================
echo                    YOLOv11 Vehicle Counter - Gorgeous Edition
echo ======================================================================
echo.
echo ✨ GORGEOUS FEATURES:
echo    🎨 Modern glassmorphism design
echo    🌈 Dynamic gradient backgrounds and particle effects
echo    💫 Smooth animations and transitions
echo    🎯 Professional AI analysis interface
echo    📱 Responsive layout adaptation
echo    🔥 Stunning visual effects
echo    ⚡ High-performance user experience
echo.
echo 🎨 VISUAL EFFECTS:
echo    🌟 Dynamic particle background
echo    🌈 Multi-layer gradient colors
echo    💎 Glassmorphism cards
echo    ⚡ Smooth animation transitions
echo    🎯 Hover interaction effects
echo    📱 Responsive adaptation
echo.
echo 🤖 AI CAPABILITIES:
echo    🔍 YOLOv11 deep learning detection
echo    🚗 Multi-type vehicle recognition
echo    📏 Intelligent trajectory tracking
echo    📊 Real-time statistical analysis
echo    🎨 Visualization annotation output
echo    📹 High-quality video processing
echo.
echo 🎛️ INTERFACE LAYOUT:
echo    Left: Intelligent Control Center
echo    Center: Video Preview and Comparison Analysis
echo    Right: Real-time Statistics and Data Downloads
echo.
echo Access URL: http://localhost:5000
echo.
echo Starting gorgeous web application...
echo Enjoy the modern AI analysis experience!
echo.

python run_webapp_gorgeous.py

echo.
echo Gorgeous web application stopped.
pause