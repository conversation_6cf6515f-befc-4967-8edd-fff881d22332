#!/usr/bin/env python3
"""
测试稳定版Web应用
验证OpenMP冲突修复和错误处理
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_openmp_fix():
    """测试OpenMP冲突修复"""
    print("🔧 测试OpenMP冲突修复...")
    
    # 检查环境变量是否正确设置
    openmp_vars = [
        'KMP_DUPLICATE_LIB_OK',
        'OMP_NUM_THREADS',
        'MKL_NUM_THREADS',
        'NUMEXPR_NUM_THREADS',
        'OPENBLAS_NUM_THREADS'
    ]
    
    fixes_found = 0
    for var in openmp_vars:
        if var in os.environ:
            print(f"✅ 环境变量 {var} = {os.environ[var]}")
            fixes_found += 1
        else:
            print(f"❌ 环境变量 {var} 未设置")
    
    if fixes_found >= 3:
        print(f"✅ OpenMP修复验证通过 ({fixes_found}/{len(openmp_vars)} 个变量)")
        return True
    else:
        print(f"⚠️  OpenMP修复不完整 ({fixes_found}/{len(openmp_vars)} 个变量)")
        return False

def test_stable_backend():
    """测试稳定版后端"""
    print("🛡️  测试稳定版后端...")
    backend_file = Path("webapp/app_stable_ultimate.py")
    
    if not backend_file.exists():
        print("❌ 稳定版后端文件不存在")
        return False
    
    content = backend_file.read_text(encoding='utf-8')
    
    # 检查稳定版特征
    stable_features = 0
    features_to_check = [
        ("OpenMP环境变量设置", "KMP_DUPLICATE_LIB_OK"),
        ("线程数限制", "OMP_NUM_THREADS"),
        ("警告抑制", "warnings.filterwarnings"),
        ("安全帧处理", "safe_process_frame"),
        ("错误恢复机制", "processing_errors"),
        ("成功率统计", "success_rate"),
        ("完整错误处理", "try:.*except Exception"),
        ("资源清理", "cap.release"),
        ("PyTorch线程配置", "torch.set_num_threads"),
        ("OpenCV线程配置", "cv2.setNumThreads")
    ]
    
    for feature_name, feature_code in features_to_check:
        if feature_code in content:
            print(f"✅ {feature_name}")
            stable_features += 1
        else:
            print(f"❌ {feature_name}")
    
    if stable_features >= 8:
        print(f"✅ 稳定版后端验证通过 ({stable_features}/{len(features_to_check)} 项特征)")
        return True
    else:
        print(f"⚠️  稳定版后端不完整 ({stable_features}/{len(features_to_check)} 项特征)")
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("🛡️  测试错误处理机制...")
    
    try:
        # 模拟导入稳定版模块
        sys.path.insert(0, str(Path("webapp").absolute()))
        
        # 测试环境变量设置
        original_env = os.environ.copy()
        
        # 清除环境变量来测试自动设置
        test_vars = ['KMP_DUPLICATE_LIB_OK', 'OMP_NUM_THREADS']
        for var in test_vars:
            if var in os.environ:
                del os.environ[var]
        
        # 导入稳定版应用（应该自动设置环境变量）
        try:
            import app_stable_ultimate
            print("✅ 稳定版模块导入成功")
            
            # 检查环境变量是否被自动设置
            auto_set = 0
            for var in test_vars:
                if var in os.environ:
                    auto_set += 1
                    print(f"✅ 自动设置 {var} = {os.environ[var]}")
            
            if auto_set >= len(test_vars):
                print("✅ 环境变量自动设置功能正常")
                error_handling_ok = True
            else:
                print("⚠️  环境变量自动设置不完整")
                error_handling_ok = False
                
        except Exception as e:
            print(f"❌ 稳定版模块导入失败: {e}")
            error_handling_ok = False
        
        # 恢复原始环境变量
        os.environ.clear()
        os.environ.update(original_env)
        
        return error_handling_ok
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def test_webapp_connection():
    """测试Web应用连接"""
    print("🧪 测试Web应用连接...")
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: run_webapp_stable.bat")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def check_file_completeness():
    """检查文件完整性"""
    print("📋 检查稳定版文件完整性...")
    
    required_files = [
        Path("webapp/app_stable_ultimate.py"),
        Path("run_webapp_stable.py"),
        Path("run_webapp_stable.bat")
    ]
    
    missing_files = []
    for file_path in required_files:
        if file_path.exists():
            print(f"✅ {file_path.name}")
        else:
            print(f"❌ {file_path.name}")
            missing_files.append(file_path.name)
    
    if not missing_files:
        print("✅ 所有必需文件都存在")
        return True
    else:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False

def analyze_stability_improvements():
    """分析稳定性改进"""
    print("📊 分析稳定性改进...")
    
    try:
        original_file = Path("webapp/app_ultimate_perfect.py")
        stable_file = Path("webapp/app_stable_ultimate.py")
        
        if not original_file.exists():
            print("⚠️  原始文件不存在，跳过对比")
            return True
        
        if not stable_file.exists():
            print("❌ 稳定版文件不存在")
            return False
        
        original_content = original_file.read_text(encoding='utf-8')
        stable_content = stable_file.read_text(encoding='utf-8')
        
        # 分析改进点
        improvements = [
            ("环境变量预设置", "os.environ['KMP_DUPLICATE_LIB_OK']", "在导入前设置"),
            ("警告抑制", "warnings.filterwarnings", "抑制OpenMP警告"),
            ("安全处理", "safe_process_frame", "带错误恢复的帧处理"),
            ("错误统计", "processing_errors", "记录处理错误"),
            ("成功率监控", "success_rate", "监控处理成功率"),
            ("资源管理", "cap.release", "确保资源释放"),
            ("线程配置", "set_num_threads", "限制线程数量")
        ]
        
        improvements_found = 0
        for improvement_name, improvement_code, description in improvements:
            if improvement_code in stable_content:
                if improvement_code not in original_content or stable_content.count(improvement_code) > original_content.count(improvement_code):
                    print(f"✅ {improvement_name}: {description}")
                    improvements_found += 1
                else:
                    print(f"ℹ️  {improvement_name}: 已存在")
            else:
                print(f"❌ {improvement_name}: 缺失")
        
        if improvements_found >= 5:
            print(f"✅ 稳定性改进验证通过 ({improvements_found} 项改进)")
            return True
        else:
            print(f"⚠️  稳定性改进不足 ({improvements_found} 项改进)")
            return False
            
    except Exception as e:
        print(f"❌ 稳定性分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🛡️  YOLOv11车辆计数器 - 稳定版Web应用测试")
    print("=" * 80)
    
    tests = [
        ("文件完整性检查", check_file_completeness),
        ("OpenMP修复测试", test_openmp_fix),
        ("稳定版后端验证", test_stable_backend),
        ("错误处理机制测试", test_error_handling),
        ("稳定性改进分析", analyze_stability_improvements),
        ("Web应用连接测试", test_webapp_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 60)
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed >= 5:  # 至少5项通过就算成功
        print("🎉 稳定版Web应用验证通过！")
        print("\n🛡️  稳定版特色:")
        print("   ✅ 解决OpenMP库冲突问题")
        print("   🛡️  完整的错误处理和恢复机制")
        print("   📊 详细的处理统计和成功率监控")
        print("   🎯 基于最佳追踪+撞线计数算法")
        print("   🔍 实时调试信息和错误监控")
        print("   ⚡ 性能优化和资源管理")
        print("   🌐 友好的Web界面，支持大文件上传")
        print("\n🚀 现在可以运行:")
        print("   run_webapp_stable.bat")
        print("\n💡 这个版本应该能解决OpenMP冲突问题！")
    else:
        print("⚠️  部分验证未通过")
        print("\n💡 建议:")
        print("   1. 检查稳定版文件是否正确创建")
        print("   2. 确保环境变量设置正确")
        print("   3. 重新运行 run_webapp_stable.py")

if __name__ == "__main__":
    main()