#!/bin/bash

# SSH访问配置脚本
# 在服务器上运行此脚本来配置SSH密钥认证

echo "配置SSH密钥认证..."

# 创建.ssh目录
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 添加你的公钥到authorized_keys
echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDDx03Z+0lC1lg9lu/IW9Xme/ahLNouUxg6h1f4S4qbNAgk8pNL3GB2Mc/hWmFpKCcjkJuB/95eZsC3PfgI2cp+tWWER02qmcEnrDi6Q3jPMp2bBu66VKaXs8IU0C4pWzD9/gRcUFMwq8GAO/Q/zlzC6DZOu8A/oq94IQEDTyc+U53Ky8IqWH9M0M8aYQuqis3oPPgamSRZM7/s97QZYuKG+xsUbBkfaSY2N1NaiCI8G6eA2uheRUB4FSF2woth7KMUVa2wO8DQv4IVbOspfCQrxvUkXlz4XKYu7prQgTX04gslDqkWHb1wZXImMC5KwWDLqcPcoMaMfNsOTilOv9Y/cB+YRkhVcrxGrwk3L4KQGoDWHHneE0pCOPJz0fO3KKG000WgruUiBO9Yp5uTYJWS2xaRxM29Yv4/mMwKNlo3R2TBMAHI6iJhsR/qMIiGyq0b3mV5EWl8YDsDVdk54O7COjKbn2Ku++EbvxRAAtmH+PZyxeH0Es3HBr0xsChw48NWlt9sg0cgIlJAPwIOBl9D2TAMhuKT7cw0fOKfRjoKPnQ4H4DiNuF8GgEYavWI7k94YzwPl4QMS9BG6zMTHnVUIpAsgN9nuoWEX5IoaDlIbmy3udqI+ffF6ADvMldKARZUdi1oHD8nkLV30GPqJyVKcIuFI+TA7UpvCwnYr6opAQ== liwen@deepmind" >> ~/.ssh/authorized_keys

# 设置正确的权限
chmod 600 ~/.ssh/authorized_keys

# 检查SSH配置，确保允许密钥认证
sudo sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
sudo sed -i 's/PubkeyAuthentication no/PubkeyAuthentication yes/' /etc/ssh/sshd_config

# 重启SSH服务
sudo systemctl restart ssh

echo "SSH密钥认证配置完成！"
echo "现在你可以使用SSH密钥连接服务器了。"
