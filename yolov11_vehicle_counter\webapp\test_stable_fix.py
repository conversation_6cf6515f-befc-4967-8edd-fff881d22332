#!/usr/bin/env python3
"""
测试稳定修复版
"""

import os
from pathlib import Path

def test_stable_fix():
    """测试稳定修复版"""
    print("🧪 测试稳定修复版...")
    
    # 检查文件是否存在
    files_to_check = [
        "app_stable_fix.py",
        "start_stable_fix.py", 
        "start_stable_fix.bat"
    ]
    
    missing_files = []
    for filename in files_to_check:
        if Path(filename).exists():
            print(f"✅ {filename}")
        else:
            print(f"❌ {filename}")
            missing_files.append(filename)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 检查稳定修复版内容
    stable_fix_file = Path("app_stable_fix.py")
    if stable_fix_file.exists():
        content = stable_fix_file.read_text(encoding='utf-8')
        
        # 检查关键修复
        fixes = [
            ("OpenMP环境变量", "KMP_DUPLICATE_LIB_OK"),
            ("线程数限制", "OMP_NUM_THREADS"),
            ("警告抑制", "warnings.filterwarnings"),
            ("PyTorch线程配置", "torch.set_num_threads"),
            ("OpenCV线程配置", "cv2.setNumThreads"),
            ("安全帧处理", "safe_track_frame"),
            ("错误处理", "try:.*except Exception")
        ]
        
        fixes_found = 0
        for fix_name, fix_code in fixes:
            if fix_code in content:
                print(f"✅ {fix_name}")
                fixes_found += 1
            else:
                print(f"❌ {fix_name}")
        
        if fixes_found >= 6:
            print(f"✅ 稳定修复验证通过 ({fixes_found}/{len(fixes)})")
            return True
        else:
            print(f"⚠️  稳定修复不完整 ({fixes_found}/{len(fixes)})")
            return False
    
    return False

def main():
    print("🛡️  稳定修复版测试")
    print("=" * 40)
    
    if test_stable_fix():
        print("\n🎉 测试通过！")
        print("\n🚀 现在可以运行:")
        print("   start_stable_fix.bat")
        print("\n💡 这个版本应该能解决OpenMP冲突问题！")
    else:
        print("\n❌ 测试失败")
        print("💡 请检查文件是否正确创建")

if __name__ == "__main__":
    main()