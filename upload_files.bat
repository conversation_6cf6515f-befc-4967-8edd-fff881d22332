@echo off
REM YOLOv11车辆计数系统文件上传脚本
REM 适用于Windows系统

echo ================================================
echo 🚗 YOLOv11车辆计数系统 - 文件上传
echo ================================================
echo.

REM 配置变量
set SERVER_IP=************
set SERVER_USER=admin
set PROJECT_DIR=/var/www/vehicle-counter
set LOCAL_DIR=yolov11_vehicle_counter\webapp

echo 服务器IP: %SERVER_IP%
echo 用户名: %SERVER_USER%
echo 远程目录: %PROJECT_DIR%
echo 本地目录: %LOCAL_DIR%
echo.

REM 检查本地目录是否存在
if not exist "%LOCAL_DIR%" (
    echo ❌ 错误: 本地目录 %LOCAL_DIR% 不存在
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 📁 检查本地文件...
if not exist "%LOCAL_DIR%\app.py" (
    echo ❌ 错误: app.py 文件不存在
    pause
    exit /b 1
)

if not exist "%LOCAL_DIR%\requirements.txt" (
    echo ❌ 错误: requirements.txt 文件不存在
    pause
    exit /b 1
)

if not exist "%LOCAL_DIR%\index.html" (
    echo ❌ 错误: index.html 文件不存在
    pause
    exit /b 1
)

echo ✅ 本地文件检查通过
echo.

echo 📤 开始上传文件到服务器...
echo 请输入服务器密码 (可能需要多次输入)
echo.

REM 使用scp上传文件
echo 上传 app.py...
scp "%LOCAL_DIR%\app.py" %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/

echo 上传 requirements.txt...
scp "%LOCAL_DIR%\requirements.txt" %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/

echo 上传 index.html...
scp "%LOCAL_DIR%\index.html" %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/

echo 上传 static 目录...
scp -r "%LOCAL_DIR%\static" %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/

if exist "%LOCAL_DIR%\start_webapp.py" (
    echo 上传 start_webapp.py...
    scp "%LOCAL_DIR%\start_webapp.py" %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/
)

if exist "%LOCAL_DIR%\README.md" (
    echo 上传 README.md...
    scp "%LOCAL_DIR%\README.md" %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/
)

echo.
echo ================================================
echo 🎉 文件上传完成！
echo ================================================
echo.
echo 下一步操作:
echo 1. 登录服务器: ssh %SERVER_USER%@%SERVER_IP%
echo 2. 运行部署脚本: ./quick_deploy.sh
echo 3. 配置域名解析: vehicle.lkr666.online
echo.
echo 如果上传失败，请检查:
echo - 网络连接是否正常
echo - 服务器SSH是否可访问
echo - 用户权限是否正确
echo.
pause
