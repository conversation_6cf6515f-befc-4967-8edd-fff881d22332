

import re
import os

# Define class mapping
CLASS_MAPPING = {
    'people': 0,
    'car': 1
}

# Image dimensions (assuming all images are 3840x2160)
IMAGE_WIDTH = 3840
IMAGE_HEIGHT = 2160

# Input and output paths
INPUT_LOG_PATH = r'D:\\lkr_yolo\\yolov11_vehicle_counter\\dataset\\image\\label_teiminal.md'
OUTPUT_LABELS_DIR = r'D:\\lkr_yolo\\yolov11_vehicle_counter\\dataset\\image'

def convert_bbox_to_yolo(xmin, ymin, xmax, ymax, img_width, img_height):
    """Converts bounding box coordinates to YOLO format."""
    center_x = (xmin + xmax) / 2.0
    center_y = (ymin + ymax) / 2.0
    width = xmax - xmin
    height = ymax - ymin

    # Normalize
    center_x /= img_width
    center_y /= img_height
    width /= img_width
    height /= img_height

    return f"{center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"

def parse_labelimg_log(log_path, output_dir, class_mapping, img_width, img_height):
    """Parses LabelImg log and converts annotations to YOLO format."""
    os.makedirs(output_dir, exist_ok=True)

    current_image_name = None
    annotations_for_current_image = []

    with open(log_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()

            # Match lines indicating a new image and its annotation path
            image_match = re.match(r'Image:.*\\(frame_\d{5}\\.jpg) -> Annotation:.*', line)
            if image_match:
                if current_image_name:
                    # Save previous image's annotations
                    save_yolo_annotations(current_image_name, annotations_for_current_image, output_dir)
                
                current_image_name = image_match.group(1)
                annotations_for_current_image = []
                print(f"Processing image: {current_image_name}")
                continue

            # Match lines containing Python list of annotations
            # This regex is more flexible to handle truncated lines
            # It looks for a line starting with '[' and containing '(' and ')]'
            # and tries to capture the content within the outermost brackets.
            annotation_match = re.match(r'^(\[\(.*\)\])$', line)
            if annotation_match:
                try:
                    # Attempt to safely evaluate the string as a Python list
                    # Handle potential truncation by trying to complete the string if needed
                    full_line = line
                    # Read more lines if the current line is truncated and doesn't end with ')]'
                    while not full_line.endswith(')]') and f.tell() < os.fstat(f.fileno()).st_size:
                        next_line = f.readline().strip()
                        full_line += next_line

                    parsed_annotations = eval(full_line)
                    for class_name, coords, _, _, _ in parsed_annotations:
                        if class_name in class_mapping:
                            # Extract xmin, ymin, xmax, ymax from the list of points
                            # Assuming the format is [(xmin, ymin), (xmax, ymin), (xmax, ymax), (xmin, ymax)]
                            # So, coords[0][0] is xmin, coords[0][1] is ymin
                            # and coords[2][0] is xmax, coords[2][1] is ymax
                            xmin = coords[0][0]
                            ymin = coords[0][1]
                            xmax = coords[2][0]
                            ymax = coords[2][1]

                            yolo_bbox = convert_bbox_to_yolo(xmin, ymin, xmax, ymax, img_width, img_height)
                            class_id = class_mapping[class_name]
                            annotations_for_current_image.append(f"{class_id} {yolo_bbox}")
                except Exception as e:
                    print(f"Error parsing annotation line: {line}. Error: {e}")
                    continue

    # Save the last image's annotations
    if current_image_name:
        save_yolo_annotations(current_image_name, annotations_for_current_image, output_dir)


def save_yolo_annotations(image_name, annotations, output_dir):
    """Saves YOLO formatted annotations to a .txt file."""
    label_filename = os.path.splitext(image_name)[0] + '.txt'
    output_path = os.path.join(output_dir, label_filename)
    with open(output_path, 'w', encoding='utf-8') as f:
        for annotation in annotations:
            f.write(annotation + '\n')
    print(f"Generated: {output_path}")

if __name__ == "__main__":
    parse_labelimg_log(INPUT_LOG_PATH, OUTPUT_LABELS_DIR, CLASS_MAPPING, IMAGE_WIDTH, IMAGE_HEIGHT)
