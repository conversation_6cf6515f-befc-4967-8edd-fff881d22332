#!/usr/bin/env python3
"""
测试黄金版Web应用
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_golden_backend():
    """测试黄金版后端"""
    print("🏆 测试黄金版后端...")
    
    backend_file = Path("webapp/app_golden_web.py")
    if backend_file.exists():
        content = backend_file.read_text(encoding='utf-8')
        
        golden_features = 0
        
        # 检查黄金版特征
        if "基于main_golden.py" in content:
            print("✅ 基于黄金版main_golden.py")
            golden_features += 1
        
        if "model.track(frame, persist=True" in content:
            print("✅ 使用YOLO内置跟踪")
            golden_features += 1
        
        if "results[0].plot()" in content:
            print("✅ 使用YOLO内置plot方法")
            golden_features += 1
        
        if "简单直接的计数逻辑" in content:
            print("✅ 简单直接的计数逻辑")
            golden_features += 1
        
        if "track_history" in content and "counted_ids" in content:
            print("✅ 黄金版跟踪变量")
            golden_features += 1
        
        if golden_features >= 4:
            print(f"✅ 黄金版后端验证通过 ({golden_features}/5 项特征)")
            return True
        else:
            print(f"⚠️  黄金版后端不完整 ({golden_features}/5 项特征)")
            return False
    else:
        print("❌ 黄金版后端文件不存在")
        return False

def test_webapp_connection():
    """测试Web应用连接"""
    print("🧪 测试Web应用连接...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正在运行")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先运行: run_webapp_gorgeous.bat")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def compare_with_original():
    """与原始黄金版本对比"""
    print("🔍 与原始黄金版本对比...")
    
    original_file = Path("src/main_golden.py")
    web_file = Path("webapp/app_golden_web.py")
    
    if not original_file.exists():
        print("❌ 原始黄金版本不存在")
        return False
    
    if not web_file.exists():
        print("❌ Web黄金版本不存在")
        return False
    
    original_content = original_file.read_text(encoding='utf-8')
    web_content = web_file.read_text(encoding='utf-8')
    
    # 检查关键算法是否一致
    key_algorithms = [
        "model.track(frame, persist=True",
        "results[0].plot()",
        "prev_x < line_x and center_x >= line_x",
        "prev_x > line_x and center_x <= line_x",
        "track_history[track_id] = (center_x, center_y)"
    ]
    
    consistent = 0
    for algorithm in key_algorithms:
        if algorithm in original_content and algorithm in web_content:
            print(f"✅ 算法一致: {algorithm[:30]}...")
            consistent += 1
        else:
            print(f"⚠️  算法不一致: {algorithm[:30]}...")
    
    if consistent >= 4:
        print(f"✅ 核心算法一致性验证通过 ({consistent}/{len(key_algorithms)})")
        return True
    else:
        print(f"❌ 核心算法一致性验证失败 ({consistent}/{len(key_algorithms)})")
        return False

def main():
    """主函数"""
    print("🏆 YOLOv11车辆计数器 - 黄金版Web应用测试")
    print("=" * 70)
    
    tests = [
        ("黄金版后端验证", test_golden_backend),
        ("算法一致性检查", compare_with_original),
        ("Web应用连接测试", test_webapp_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        print("-" * 50)
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 黄金版Web应用验证通过！")
        print("\n🏆 黄金版特色:")
        print("   ✅ 基于真正有效的main_golden.py")
        print("   🎯 使用YOLO内置跟踪，简单直接")
        print("   📊 经过验证的计数逻辑")
        print("   🎨 YOLO内置plot方法，最佳可视化")
        print("   ⚡ 稳定可靠的算法实现")
        
        print("\n🚀 现在可以运行:")
        print("   run_webapp_gorgeous.bat")
        print("\n💡 这次应该能正确检测和计数车辆了！")
        
    else:
        print("⚠️  部分验证未通过")
        print("\n💡 建议:")
        print("   1. 检查黄金版文件是否正确创建")
        print("   2. 确保算法逻辑与原版一致")
        print("   3. 重新运行 run_webapp_gorgeous.py")

if __name__ == "__main__":
    main()