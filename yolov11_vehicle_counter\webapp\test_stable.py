#!/usr/bin/env python3
"""
测试稳定版环境设置
"""

import os
import sys

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'

print("🔧 环境变量设置:")
print(f"KMP_DUPLICATE_LIB_OK = {os.environ.get('KMP_DUPLICATE_LIB_OK')}")
print(f"OMP_NUM_THREADS = {os.environ.get('OMP_NUM_THREADS')}")

print("\n🧪 测试导入...")

try:
    import warnings
    warnings.filterwarnings("ignore", message=".*libiomp5md.dll.*")
    print("✅ warnings导入成功")
except Exception as e:
    print(f"❌ warnings导入失败: {e}")

try:
    from ultralytics import YOLO
    print("✅ ultralytics导入成功")
except Exception as e:
    print(f"❌ ultralytics导入失败: {e}")

try:
    import torch
    torch.set_num_threads(1)
    print("✅ torch导入成功，线程数设置为1")
except Exception as e:
    print(f"❌ torch导入失败: {e}")

try:
    import cv2
    cv2.setNumThreads(1)
    print("✅ cv2导入成功，线程数设置为1")
except Exception as e:
    print(f"❌ cv2导入失败: {e}")

try:
    from flask import Flask
    app = Flask(__name__)
    print("✅ Flask导入成功")
    
    @app.route('/')
    def hello():
        return "Stable version is working!"
    
    print("🚀 启动Flask测试服务器...")
    print("🌐 访问地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)
    
except Exception as e:
    print(f"❌ Flask测试失败: {e}")
    import traceback
    traceback.print_exc()