#!/usr/bin/env python3
"""
YOLOv11车辆计数系统自动部署脚本
使用Python实现自动化部署
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 配置信息
SERVER_IP = "************"
SERVER_USER = "admin"
PROJECT_DIR = "/var/www/vehicle-counter"
LOCAL_WEBAPP_DIR = "yolov11_vehicle_counter/webapp"
DOMAIN_NAME = "vehicle.smart-traffic.top"

def log_info(message):
    print(f"[INFO] {message}")

def log_success(message):
    print(f"[SUCCESS] {message}")

def log_error(message):
    print(f"[ERROR] {message}")

def log_warning(message):
    print(f"[WARNING] {message}")

def check_local_files():
    """检查本地文件是否存在"""
    log_info("检查本地文件...")
    
    webapp_dir = Path(LOCAL_WEBAPP_DIR)
    if not webapp_dir.exists():
        log_error(f"本地目录不存在: {LOCAL_WEBAPP_DIR}")
        return False
    
    required_files = ["app.py", "requirements.txt", "index.html"]
    for file in required_files:
        file_path = webapp_dir / file
        if not file_path.exists():
            log_error(f"缺少必要文件: {file}")
            return False
    
    static_dir = webapp_dir / "static"
    if not static_dir.exists():
        log_warning("static目录不存在")
    
    log_success("本地文件检查通过")
    return True

def run_ssh_command(command, show_output=True):
    """执行SSH命令"""
    ssh_cmd = f'ssh {SERVER_USER}@{SERVER_IP} "{command}"'
    log_info(f"执行SSH命令: {command}")
    
    try:
        result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True)
        if show_output and result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        log_error(f"SSH命令执行失败: {e}")
        return False

def upload_file(local_path, remote_path):
    """上传单个文件"""
    scp_cmd = f'scp "{local_path}" {SERVER_USER}@{SERVER_IP}:{remote_path}'
    log_info(f"上传文件: {local_path}")
    
    try:
        result = subprocess.run(scp_cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            log_success(f"文件上传成功: {local_path}")
            return True
        else:
            log_error(f"文件上传失败: {result.stderr}")
            return False
    except Exception as e:
        log_error(f"文件上传异常: {e}")
        return False

def upload_directory(local_dir, remote_path):
    """上传目录"""
    scp_cmd = f'scp -r "{local_dir}" {SERVER_USER}@{SERVER_IP}:{remote_path}'
    log_info(f"上传目录: {local_dir}")
    
    try:
        result = subprocess.run(scp_cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            log_success(f"目录上传成功: {local_dir}")
            return True
        else:
            log_error(f"目录上传失败: {result.stderr}")
            return False
    except Exception as e:
        log_error(f"目录上传异常: {e}")
        return False

def create_remote_directory():
    """创建远程项目目录"""
    log_info("创建远程项目目录...")
    
    commands = [
        f"sudo mkdir -p {PROJECT_DIR}",
        f"sudo chown {SERVER_USER}:{SERVER_USER} {PROJECT_DIR}"
    ]
    
    for cmd in commands:
        if not run_ssh_command(cmd):
            log_error(f"命令执行失败: {cmd}")
            return False
    
    log_success("远程目录创建成功")
    return True

def upload_project_files():
    """上传项目文件"""
    log_info("开始上传项目文件...")
    
    webapp_dir = Path(LOCAL_WEBAPP_DIR)
    
    # 上传主要文件
    main_files = ["app.py", "requirements.txt", "index.html"]
    for file in main_files:
        local_file = webapp_dir / file
        if local_file.exists():
            if not upload_file(str(local_file), f"{PROJECT_DIR}/{file}"):
                return False
    
    # 上传static目录
    static_dir = webapp_dir / "static"
    if static_dir.exists():
        if not upload_directory(str(static_dir), PROJECT_DIR):
            return False
    
    log_success("项目文件上传完成")
    return True

def install_system_dependencies():
    """安装系统依赖"""
    log_info("安装系统依赖...")
    
    commands = [
        "sudo apt update",
        "sudo apt install -y python3-pip python3-venv python3-dev build-essential",
        "sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg",
        "sudo apt install -y supervisor apache2",
        "sudo apt install -y certbot python3-certbot-apache"
    ]
    
    for cmd in commands:
        log_info(f"执行: {cmd}")
        if not run_ssh_command(cmd, show_output=False):
            log_warning(f"命令可能失败: {cmd}")
    
    log_success("系统依赖安装完成")
    return True

def setup_python_environment():
    """设置Python环境"""
    log_info("设置Python虚拟环境...")
    
    commands = [
        f"cd {PROJECT_DIR} && python3 -m venv venv",
        f"cd {PROJECT_DIR} && source venv/bin/activate && pip install --upgrade pip",
        f"cd {PROJECT_DIR} && source venv/bin/activate && pip install -r requirements.txt",
        f"cd {PROJECT_DIR} && source venv/bin/activate && pip install gunicorn"
    ]
    
    for cmd in commands:
        if not run_ssh_command(cmd):
            log_error(f"Python环境设置失败: {cmd}")
            return False
    
    log_success("Python环境设置完成")
    return True

def create_app_configs():
    """创建应用配置文件"""
    log_info("创建应用配置文件...")
    
    # 创建Gunicorn配置
    gunicorn_config = f'''
cat > {PROJECT_DIR}/gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF
'''
    
    # 创建启动脚本
    start_script = f'''
cat > {PROJECT_DIR}/start.sh << 'EOF'
#!/bin/bash
cd {PROJECT_DIR}
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF
'''
    
    # 设置执行权限
    chmod_cmd = f"chmod +x {PROJECT_DIR}/start.sh"
    
    commands = [gunicorn_config, start_script, chmod_cmd]
    
    for cmd in commands:
        if not run_ssh_command(cmd):
            log_error("配置文件创建失败")
            return False
    
    log_success("应用配置文件创建完成")
    return True

def configure_apache():
    """配置Apache虚拟主机"""
    log_info("配置Apache虚拟主机...")
    
    # 启用Apache模块
    enable_modules = "sudo a2enmod rewrite proxy proxy_http headers ssl"
    run_ssh_command(enable_modules)
    
    # 创建虚拟主机配置
    vhost_config = f'''
sudo tee /etc/apache2/sites-available/vehicle-counter.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName {DOMAIN_NAME}
    ServerAlias www.{DOMAIN_NAME}
    
    DocumentRoot {PROJECT_DIR}
    
    Alias /static {PROJECT_DIR}/static
    <Directory {PROJECT_DIR}/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    LimitRequestBody 104857600
    
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${{APACHE_LOG_DIR}}/vehicle-counter_error.log
    CustomLog ${{APACHE_LOG_DIR}}/vehicle-counter_access.log combined
</VirtualHost>
EOF
'''
    
    # 启用站点
    enable_site = "sudo a2ensite vehicle-counter.conf"
    reload_apache = "sudo systemctl reload apache2"
    
    commands = [vhost_config, enable_site, reload_apache]
    
    for cmd in commands:
        if not run_ssh_command(cmd):
            log_error("Apache配置失败")
            return False
    
    log_success("Apache虚拟主机配置完成")
    return True

def configure_supervisor():
    """配置Supervisor"""
    log_info("配置Supervisor...")
    
    supervisor_config = f'''
sudo tee /etc/supervisor/conf.d/vehicle-counter.conf > /dev/null << 'EOF'
[program:vehicle-counter]
command={PROJECT_DIR}/start.sh
directory={PROJECT_DIR}
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="{PROJECT_DIR}/venv/bin"
EOF
'''
    
    # 重新加载Supervisor配置
    reload_supervisor = "sudo supervisorctl reread && sudo supervisorctl update"
    start_app = "sudo supervisorctl start vehicle-counter"
    
    commands = [supervisor_config, reload_supervisor, start_app]
    
    for cmd in commands:
        if not run_ssh_command(cmd):
            log_error("Supervisor配置失败")
            return False
    
    log_success("Supervisor配置完成")
    return True

def test_deployment():
    """测试部署"""
    log_info("测试部署...")
    
    # 检查应用状态
    status_cmd = "sudo supervisorctl status vehicle-counter"
    run_ssh_command(status_cmd)
    
    # 测试API
    test_api = "curl -s http://localhost:5000/api/health || echo 'API测试失败'"
    run_ssh_command(test_api)
    
    log_success("部署测试完成")

def main():
    """主函数"""
    print("=" * 60)
    print("🚗 YOLOv11车辆计数系统自动部署")
    print("=" * 60)
    print(f"🌐 域名: {DOMAIN_NAME}")
    print(f"🖥️  服务器: {SERVER_IP}")
    print(f"📁 项目目录: {PROJECT_DIR}")
    print("=" * 60)
    print()
    
    # 检查本地文件
    if not check_local_files():
        sys.exit(1)
    
    # 创建远程目录
    if not create_remote_directory():
        sys.exit(1)
    
    # 上传项目文件
    if not upload_project_files():
        log_error("文件上传失败，请检查SSH连接和权限")
        print("\n手动上传指南:")
        print("1. 使用WinSCP连接服务器")
        print("2. 上传webapp目录下的所有文件到 /var/www/vehicle-counter/")
        print("3. 然后重新运行此脚本")
        sys.exit(1)
    
    # 安装系统依赖
    install_system_dependencies()
    
    # 设置Python环境
    if not setup_python_environment():
        sys.exit(1)
    
    # 创建应用配置
    if not create_app_configs():
        sys.exit(1)
    
    # 配置Apache
    if not configure_apache():
        sys.exit(1)
    
    # 配置Supervisor
    if not configure_supervisor():
        sys.exit(1)
    
    # 测试部署
    test_deployment()
    
    print("\n" + "=" * 60)
    print("🎉 部署完成！")
    print("=" * 60)
    print(f"🌐 网站地址: http://{DOMAIN_NAME}")
    print(f"📊 健康检查: http://{DOMAIN_NAME}/api/health")
    print("=" * 60)
    print("\n下一步:")
    print("1. 配置域名DNS解析")
    print("2. 配置SSL证书: ./ssl_setup.sh")
    print("=" * 60)

if __name__ == "__main__":
    main()
