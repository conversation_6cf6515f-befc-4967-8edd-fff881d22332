#!/usr/bin/env python3
"""
部署配置文件 - 适配云服务器环境
解决路径问题，确保在任何环境下都能正常运行
"""

import os
import sys
from pathlib import Path

class DeployConfig:
    """部署配置类 - 自动适配不同环境"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent  # yolov11_vehicle_counter目录
        self.webapp_dir = Path(__file__).parent       # webapp目录
        
    def get_model_paths(self):
        """获取模型文件路径 - 按优先级搜索"""
        model_search_paths = [
            # 相对于webapp目录的路径
            self.webapp_dir / "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
            self.base_dir / "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
            
            # 相对于项目根目录的路径
            self.base_dir.parent / "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
            
            # 当前工作目录的相对路径
            Path("runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"),
            Path("../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"),
            Path("../../runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"),
            
            # 备用模型路径
            self.base_dir / "runs/train/yolov11m_vehicle_detection/weights/best.pt",
            Path("runs/train/yolov11m_vehicle_detection/weights/best.pt"),
        ]
        
        return [str(path) for path in model_search_paths]
    
    def get_upload_folder(self):
        """获取上传文件夹路径"""
        upload_dir = self.webapp_dir / "uploads"
        upload_dir.mkdir(exist_ok=True)
        return str(upload_dir)
    
    def get_output_folder(self):
        """获取输出文件夹路径"""
        output_dir = self.webapp_dir / "outputs"
        output_dir.mkdir(exist_ok=True)
        return str(output_dir)
    
    def get_static_folder(self):
        """获取静态文件夹路径"""
        return str(self.webapp_dir / "static")
    
    def get_template_folder(self):
        """获取模板文件夹路径"""
        return str(self.webapp_dir / "templates")
    
    def validate_environment(self):
        """验证环境配置"""
        print("🔍 验证部署环境...")
        
        # 检查关键目录
        dirs_to_check = [
            ("Webapp目录", self.webapp_dir),
            ("Base目录", self.base_dir),
            ("Static目录", self.webapp_dir / "static"),
            ("Templates目录", self.webapp_dir / "templates"),
        ]
        
        for name, path in dirs_to_check:
            if path.exists():
                print(f"✅ {name}: {path}")
            else:
                print(f"❌ {name}: {path} (不存在)")
        
        # 检查模型文件
        model_paths = self.get_model_paths()
        model_found = False
        for model_path in model_paths:
            if Path(model_path).exists():
                print(f"✅ 找到模型文件: {model_path}")
                model_found = True
                break
        
        if not model_found:
            print("⚠️  未找到自定义模型文件，将使用预训练模型")
        
        # 检查关键文件
        key_files = [
            self.webapp_dir / "app.py",
            self.webapp_dir / "templates/index.html",
            self.webapp_dir / "static/js/main.js",
        ]
        
        for file_path in key_files:
            if file_path.exists():
                print(f"✅ 关键文件: {file_path.name}")
            else:
                print(f"❌ 缺少关键文件: {file_path}")
        
        return model_found
    
    def get_server_config(self):
        """获取服务器配置"""
        return {
            'host': '0.0.0.0',  # 允许外部访问
            'port': 5000,
            'debug': False,     # 生产环境关闭调试
            'threaded': True,   # 启用多线程
        }
    
    def print_deployment_info(self):
        """打印部署信息"""
        print("=" * 70)
        print("🚀 YOLOv11车辆计数器 - 云服务器部署版")
        print("=" * 70)
        print(f"📁 项目根目录: {self.base_dir}")
        print(f"📁 Web应用目录: {self.webapp_dir}")
        print(f"📁 上传目录: {self.get_upload_folder()}")
        print(f"📁 输出目录: {self.get_output_folder()}")
        print("=" * 70)
        print("🌟 功能特性:")
        print("   🎯 AI车辆检测和计数")
        print("   📊 实时统计显示 (Car & People)")
        print("   📹 视频处理和预览")
        print("   📱 响应式UI设计")
        print("   ☁️  云服务器优化")
        print("=" * 70)

# 全局配置实例
deploy_config = DeployConfig()

if __name__ == "__main__":
    # 验证部署环境
    deploy_config.validate_environment()
    deploy_config.print_deployment_info()
