#!/usr/bin/env python3
"""
YOLOv11车辆计数器 - 黄金版本
基于备份文件夹中真正有效的main_upgraded.py
使用YOLO内置跟踪，简单直接，效果最佳
"""

import os
# This line MUST be at the top of your script
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
from ultralytics import YOL<PERSON>
from pathlib import Path
import argparse

# --- Configuration ---
track_history = {}
counted_ids = set()

def run_tracker(model_path, video_path, save_path, show_video=True):
    """Runs YOLOv11 object tracking, saves the output, and optionally displays it."""
    global track_history, counted_ids
    left_count, right_count = 0, 0
    track_history.clear()
    counted_ids.clear()

    print(f"Loading model from: {model_path}")
    model = YOLO(model_path)

    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))
    line_x = w // 2
    line_start, line_end = (line_x, 0), (line_x, h)

    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(save_path), fourcc, fps, (w, h))
    print(f"Output video will be saved to: {save_path}")

    frame_count = 0
    while cap.isOpened():
        success, frame = cap.read()
        if not success:
            print("Video processing completed.")
            break

        frame_count += 1
        
        # 使用YOLO内置跟踪 - 这是关键！
        results = model.track(frame, persist=True, verbose=False)
        
        # 使用YOLO内置的plot方法获得最佳可视化效果
        annotated_frame = results[0].plot()
        
        # 绘制计数线
        cv2.line(annotated_frame, line_start, line_end, (0, 255, 0), 2)

        # 处理跟踪结果
        if results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, track_id in zip(boxes, track_ids):
                center_x, center_y = int(box[0]), int(box[1])
                
                # 简单直接的计数逻辑
                if track_id in track_history:
                    prev_x, _ = track_history[track_id]
                    if track_id not in counted_ids:
                        # 从左到右穿越
                        if prev_x < line_x and center_x >= line_x:
                            right_count += 1
                            counted_ids.add(track_id)
                            print(f"车辆 {track_id} 从左到右穿越，右侧计数: {right_count}")
                        # 从右到左穿越
                        elif prev_x > line_x and center_x <= line_x:
                            left_count += 1
                            counted_ids.add(track_id)
                            print(f"车辆 {track_id} 从右到左穿越，左侧计数: {left_count}")
                
                # 更新历史位置
                track_history[track_id] = (center_x, center_y)

        # 绘制计数信息
        cv2.putText(annotated_frame, f"Left Count: {left_count}", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
        cv2.putText(annotated_frame, f"Right Count: {right_count}", (w - 300, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        # 显示视频
        if show_video:
            try:
                cv2.imshow("YOLOv11 Tracking and Counting", annotated_frame)
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    break
            except cv2.error:
                print("(GUI not available) Cannot display video. Continuing to process and save the video.")
                show_video = False

        # 保存帧
        out.write(annotated_frame)
        
        # 进度显示
        if frame_count % 100 == 0:
            print(f"处理进度: {frame_count} 帧, 左侧: {left_count}, 右侧: {right_count}")

    cap.release()
    out.release()
    cv2.destroyAllWindows()
    
    print("=" * 50)
    print("🎉 处理完成！")
    print(f"📊 最终计数结果:")
    print(f"   左侧: {left_count}")
    print(f"   右侧: {right_count}")
    print(f"   总计: {left_count + right_count}")
    print(f"💾 输出视频: {save_path}")
    print("=" * 50)

if __name__ == '__main__':
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    default_output = "D:/lkr_yolo/yolov11_vehicle_counter/video/output_golden.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - 黄金版本")
    parser.add_argument('--model', type=str, default=default_model,
                       help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video,
                       help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output,
                       help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', 
                       help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - 黄金版本")
    print("基于真正有效的main_upgraded.py")
    print("=" * 50)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 50)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)