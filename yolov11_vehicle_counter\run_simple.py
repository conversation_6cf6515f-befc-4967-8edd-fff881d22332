#!/usr/bin/env python3
"""
简单的运行脚本 - 基于原版main.py的UI优化版本
只需要运行这个脚本，自动检测环境并使用合适的参数
"""

import os
import sys
from pathlib import Path
import subprocess

def detect_environment():
    """自动检测环境并返回相应的路径"""
    # 本地环境路径
    local_model = Path('D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt')
    local_video = Path('D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    # 服务器环境路径
    server_model = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection/weights/best.pt')
    server_video = Path('C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4')
    
    if local_model.exists():
        print("✓ 检测到本地环境")
        return {
            'model': str(local_model),
            'video': str(local_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'local'
        }
    elif server_model.exists():
        print("✓ 检测到服务器环境")
        return {
            'model': str(server_model),
            'video': str(server_video),
            'reid': 'tracker/deep/osnet模型/osnet_x1_0_imagenet.pth',
            'env': 'server'
        }
    else:
        print("❌ 未找到有效的环境配置")
        print("请确保以下路径之一存在模型文件：")
        print(f"  本地: {local_model}")
        print(f"  服务器: {server_model}")
        return None

def main():
    print("=" * 50)
    print("    Vehicle Counter - UI Optimized (Simple)")
    print("=" * 50)
    print()
    
    # 检测环境
    config = detect_environment()
    if not config:
        input("按回车键退出...")
        return
    
    print(f"环境类型: {config['env']}")
    print(f"YOLO模型: {config['model']}")
    print(f"视频文件: {config['video']}")
    print(f"ReID模型: {config['reid']}")
    print()
    
    # 切换到src目录
    src_dir = Path(__file__).parent / 'src'
    os.chdir(src_dir)
    
    # 使用我们改进的main_final_version.py
    cmd = [
        sys.executable, 'main_final_version.py'
    ]
    
    print("开始处理...")
    print("命令:", ' '.join(cmd))
    print()
    
    try:
        # 运行命令
        result = subprocess.run(cmd, check=True)
        print()
        print("✓ 处理完成！")
        print("📁 检查 runs/track 目录查看结果")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 处理失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    input("按回车键退出...")

if __name__ == '__main__':
    main()