#!/usr/bin/env python3
"""
运行1080p美观版本的车辆计数器
专门优化UI布局和颜色显示
"""

import sys
from pathlib import Path

# 添加src目录到路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

if __name__ == "__main__":
    # 导入主模块并执行
    from main_beautiful_1080p import *
    
    # 直接调用主程序逻辑
    import argparse
    from datetime import datetime
    
    # 默认路径配置
    default_model = "D:/lkr_yolo/yolov11_vehicle_counter/runs/train/yolov11m_vehicle_detection_portable/weights/best.pt"
    default_video = "D:/lkr_yolo/yolov11_vehicle_counter/video/3007580-uhd_3840_2160_30fps.mp4"
    
    # 生成带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_output = f"D:/lkr_yolo/yolov11_vehicle_counter/video/output_beautiful_1080p_{timestamp}.mp4"
    
    parser = argparse.ArgumentParser(description="YOLOv11车辆计数器 - 1080p美观版本")
    parser.add_argument('--model', type=str, default=default_model, help='YOLOv11模型路径')
    parser.add_argument('--source', type=str, default=default_video, help='输入视频路径')
    parser.add_argument('--save-path', type=str, default=default_output, help='输出视频路径')
    parser.add_argument('--no-show', action='store_true', help='不显示视频窗口')
    
    args = parser.parse_args()
    
    print("🚗 YOLOv11车辆计数器 - 1080p美观版本")
    print("✨ 特性:")
    print("   🎨 专为1080p优化的美观UI")
    print("   🌈 鲜艳明显的检测框颜色")
    print("   📱 紧凑的统计显示布局")
    print("   🚗 车辆穿越计数")
    print("   👥 人员总数统计")
    print("   🆔 清洁ID显示")
    print("=" * 60)
    print(f"🤖 模型: {args.model}")
    print(f"🎥 视频: {args.source}")
    print(f"💾 输出: {args.save_path}")
    print("=" * 60)
    
    run_tracker(args.model, args.source, args.save_path, show_video=not args.no_show)