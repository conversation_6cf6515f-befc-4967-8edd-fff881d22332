# YOLOv11车辆计数系统 - 完整技术文档

## 📋 目录
1. [系统概述](#系统概述)
2. [技术架构](#技术架构)
3. [核心算法原理](#核心算法原理)
4. [文件结构详解](#文件结构详解)
5. [训练模块](#训练模块)
6. [追踪计数模块](#追踪计数模块)
7. [Web应用模块](#web应用模块)
8. [部署架构](#部署架构)
9. [系统工程思维](#系统工程思维)
10. [AI架构师视角](#ai架构师视角)

---

## 🎯 系统概述

### 项目背景
YOLOv11车辆计数系统是一个基于深度学习的智能交通监控解决方案，能够实时检测、追踪和统计视频中的车辆和行人数量。

### 核心功能
- **实时目标检测**：基于YOLOv11模型检测车辆和行人
- **多目标追踪**：使用DeepSORT算法进行目标追踪
- **智能计数**：通过虚拟线圈实现精确计数
- **Web可视化**：实时展示检测结果和统计数据
- **云端部署**：支持服务器部署和远程访问

### 技术栈
```
前端：HTML5 + CSS3 + JavaScript + WebSocket
后端：Python Flask + OpenCV + Ultralytics
AI模型：YOLOv11 + DeepSORT
部署：Apache + Gunicorn + Supervisor
服务器：Ubuntu + SSL证书
```

---

## 🏗️ 技术架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Flask后端     │    │   AI推理引擎    │
│  - 视频显示     │◄──►│  - 路由处理     │◄──►│  - YOLOv11     │
│  - 实时统计     │    │  - WebSocket    │    │  - DeepSORT    │
│  - 控制面板     │    │  - 文件处理     │    │  - 计数逻辑    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据存储      │
                    │  - 视频文件     │
                    │  - 模型权重     │
                    │  - 配置文件     │
                    └─────────────────┘
```

### 数据流向
1. **输入**：用户上传视频文件
2. **预处理**：视频解码、帧提取
3. **检测**：YOLOv11目标检测
4. **追踪**：DeepSORT多目标追踪
5. **计数**：虚拟线圈计数逻辑
6. **输出**：实时结果展示

---

## 🧠 核心算法原理

### 1. YOLOv11目标检测

#### 算法原理
YOLOv11是"You Only Look Once"系列的最新版本，采用单阶段检测方法：

```python
# 核心检测流程
def detect_objects(image):
    # 1. 图像预处理
    input_tensor = preprocess(image)  # 640x640, 归一化
    
    # 2. 模型推理
    predictions = model(input_tensor)  # [batch, 84, 8400]
    
    # 3. 后处理
    boxes, scores, classes = postprocess(predictions)
    
    # 4. NMS非极大值抑制
    final_detections = nms(boxes, scores, iou_threshold=0.5)
    
    return final_detections
```

#### 网络结构特点
- **Backbone**: CSPDarknet53 - 特征提取
- **Neck**: PANet - 特征融合
- **Head**: YOLO Head - 检测输出

#### 损失函数
```python
total_loss = λ₁ × box_loss + λ₂ × obj_loss + λ₃ × cls_loss
```

### 2. DeepSORT多目标追踪

#### 算法原理
DeepSORT结合了卡尔曼滤波和深度学习特征：

```python
class DeepSORT:
    def __init__(self):
        self.kalman_filter = KalmanFilter()  # 运动预测
        self.feature_extractor = ReIDModel()  # 外观特征
        
    def update(self, detections):
        # 1. 预测阶段
        predicted_tracks = self.predict()
        
        # 2. 数据关联
        matches = self.associate(predicted_tracks, detections)
        
        # 3. 更新追踪
        self.update_tracks(matches)
        
        return self.tracks
```

#### 关联策略
1. **运动信息**：基于位置和速度的马氏距离
2. **外观信息**：基于ReID网络的余弦距离
3. **级联匹配**：优先匹配高置信度目标

### 3. 智能计数算法

#### 虚拟线圈原理
```python
class VirtualLine:
    def __init__(self, start_point, end_point):
        self.line = (start_point, end_point)
        self.crossed_tracks = set()
        
    def check_crossing(self, track_id, current_pos, previous_pos):
        # 判断轨迹是否穿越虚拟线
        if self.line_intersection(previous_pos, current_pos, self.line):
            if track_id not in self.crossed_tracks:
                self.crossed_tracks.add(track_id)
                return True
        return False
```

#### 计数逻辑
1. **轨迹追踪**：记录每个目标的运动轨迹
2. **线段相交**：判断轨迹是否与计数线相交
3. **方向判断**：根据相交方向确定进入/离开
4. **去重处理**：避免同一目标重复计数

---

## 📁 文件结构详解

### 项目目录结构
```
YOLOv11_Vehicle_Counter/
├── 📁 models/                    # 模型相关
│   ├── yolo11n.pt               # YOLOv11预训练权重
│   ├── best.pt                  # 自定义训练权重
│   └── deepsort_weights/        # DeepSORT权重
├── 📁 training/                 # 训练模块
│   ├── train_yolo.py           # YOLO训练脚本
│   ├── data_preparation.py     # 数据预处理
│   └── config/                 # 训练配置
├── 📁 tracking/                # 追踪模块
│   ├── deep_sort.py           # DeepSORT实现
│   ├── kalman_filter.py       # 卡尔曼滤波
│   └── reid_model.py          # ReID特征提取
├── 📁 counting/               # 计数模块
│   ├── counter.py            # 计数逻辑
│   ├── virtual_line.py       # 虚拟线圈
│   └── statistics.py         # 统计分析
├── 📁 web/                   # Web应用
│   ├── app.py               # Flask主应用
│   ├── templates/           # HTML模板
│   ├── static/             # 静态资源
│   └── config/             # 配置文件
├── 📁 deployment/           # 部署相关
│   ├── docker/             # Docker配置
│   ├── nginx/              # Nginx配置
│   └── scripts/            # 部署脚本
└── 📁 utils/               # 工具函数
    ├── video_utils.py      # 视频处理
    ├── image_utils.py      # 图像处理
    └── logger.py           # 日志系统
```

### 核心文件说明

#### 1. app.py - Flask主应用
```python
# 主要功能模块
class VehicleCounterApp:
    def __init__(self):
        self.model = YOLO('models/yolo11n.pt')
        self.tracker = DeepSORT()
        self.counter = VehicleCounter()
        
    def process_video(self, video_path):
        # 视频处理主流程
        pass
        
    def websocket_handler(self):
        # 实时数据推送
        pass
```

#### 2. deep_sort.py - 追踪算法
```python
class DeepSORT:
    def __init__(self):
        self.tracks = []
        self.track_id_counter = 0
        
    def update(self, detections):
        # 追踪更新逻辑
        pass
```

#### 3. counter.py - 计数逻辑
```python
class VehicleCounter:
    def __init__(self):
        self.car_count = 0
        self.people_count = 0
        self.counting_line = VirtualLine()
        
    def count_objects(self, tracks):
        # 计数主逻辑
        pass
```

---

## 🎓 训练模块

### 数据准备
```python
# data_preparation.py
class DatasetPreparator:
    def __init__(self, data_path):
        self.data_path = data_path
        
    def prepare_yolo_format(self):
        """
        转换数据为YOLO格式
        - images/: 图像文件
        - labels/: 标注文件(.txt)
        """
        pass
        
    def split_dataset(self, train_ratio=0.8):
        """
        划分训练集和验证集
        """
        pass
```

### 训练配置
```yaml
# config/train_config.yaml
model: yolo11n.pt
data: dataset.yaml
epochs: 100
batch_size: 16
img_size: 640
device: 0
workers: 8
```

### 训练脚本
```python
# train_yolo.py
from ultralytics import YOLO

def train_model():
    # 1. 加载预训练模型
    model = YOLO('yolo11n.pt')
    
    # 2. 开始训练
    results = model.train(
        data='config/dataset.yaml',
        epochs=100,
        batch=16,
        device=0
    )
    
    # 3. 验证模型
    metrics = model.val()
    
    return model, metrics
```

---

## 🎯 追踪计数模块

### DeepSORT实现细节
```python
class Track:
    def __init__(self, detection, track_id):
        self.track_id = track_id
        self.hits = 1
        self.age = 1
        self.time_since_update = 0
        
        # 卡尔曼滤波器
        self.kf = KalmanFilter()
        self.kf.predict()
        self.kf.update(detection.to_xyah())
        
    def predict(self):
        """预测下一帧位置"""
        self.kf.predict()
        self.age += 1
        self.time_since_update += 1
        
    def update(self, detection):
        """更新追踪状态"""
        self.time_since_update = 0
        self.hits += 1
        self.kf.update(detection.to_xyah())
```

### 计数算法实现
```python
class LineCounter:
    def __init__(self, line_coords):
        self.line = line_coords
        self.crossed_ids = set()
        
    def is_crossed(self, track):
        """判断是否穿越计数线"""
        current_center = track.get_center()
        previous_center = track.get_previous_center()
        
        # 线段相交算法
        return self.line_intersect(
            previous_center, current_center, self.line
        )
        
    def line_intersect(self, p1, p2, line):
        """线段相交判断"""
        # 使用向量叉积判断
        def ccw(A, B, C):
            return (C[1]-A[1]) * (B[0]-A[0]) > (B[1]-A[1]) * (C[0]-A[0])
        
        A, B = line
        return ccw(A, p1, p2) != ccw(B, p1, p2) and ccw(p1, A, B) != ccw(p2, A, B)
```

---

## 🌐 Web应用模块

### Flask应用架构
```python
# app.py 核心结构
class VehicleCounterApp:
    def __init__(self):
        self.app = Flask(__name__)
        self.socketio = SocketIO(self.app)
        self.setup_routes()
        
    def setup_routes(self):
        @self.app.route('/')
        def index():
            return render_template('index.html')
            
        @self.app.route('/upload', methods=['POST'])
        def upload_video():
            # 视频上传处理
            pass
            
        @self.socketio.on('start_processing')
        def handle_processing(data):
            # 开始视频处理
            self.process_video_async(data['video_path'])
```

### 前端实现
```javascript
// static/js/main.js
class VehicleCounterUI {
    constructor() {
        this.socket = io();
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // WebSocket事件监听
        this.socket.on('detection_result', (data) => {
            this.updateDisplay(data);
        });
        
        this.socket.on('count_update', (data) => {
            this.updateCounters(data);
        });
    }
    
    updateDisplay(data) {
        // 更新检测结果显示
        const canvas = document.getElementById('result-canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制检测框
        data.detections.forEach(det => {
            this.drawBoundingBox(ctx, det);
        });
    }
}
```

### 实时通信机制
```python
# WebSocket实时推送
def process_video_stream(video_path):
    cap = cv2.VideoCapture(video_path)
    
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
            
        # 1. 目标检测
        detections = model.predict(frame)
        
        # 2. 目标追踪
        tracks = tracker.update(detections)
        
        # 3. 计数更新
        counts = counter.update(tracks)
        
        # 4. 实时推送
        socketio.emit('detection_result', {
            'frame': encode_frame(frame),
            'detections': detections,
            'counts': counts
        })
```

---

## 🚀 部署架构

### 生产环境架构
```
Internet
    │
    ▼
┌─────────────┐
│   Nginx     │ ← SSL终止，负载均衡
│  (Port 80)  │
└─────────────┘
    │
    ▼
┌─────────────┐
│   Apache    │ ← 反向代理
│  (Port 443) │
└─────────────┘
    │
    ▼
┌─────────────┐
│  Gunicorn   │ ← WSGI服务器
│  (Port 5000)│
└─────────────┘
    │
    ▼
┌─────────────┐
│ Flask App   │ ← Python应用
│   + AI模型  │
└─────────────┘
```

### 部署配置文件
```python
# gunicorn.conf.py
bind = "127.0.0.1:5000"
workers = 2
worker_class = "eventlet"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
```

```apache
# Apache虚拟主机配置
<VirtualHost *:443>
    ServerName vehicle.smart-traffic.top
    
    ProxyPreserveHost On
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # WebSocket支持
    ProxyPass /socket.io/ http://127.0.0.1:5000/socket.io/
    ProxyPassReverse /socket.io/ http://127.0.0.1:5000/socket.io/
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/cert.pem
    SSLCertificateKeyFile /path/to/private.key
</VirtualHost>
```

---

## 🧩 系统工程思维

### 1. 模块化设计原则
```
系统分层架构：
┌─────────────────┐
│   表现层        │ ← Web界面，用户交互
├─────────────────┤
│   业务逻辑层    │ ← 计数逻辑，数据处理
├─────────────────┤
│   算法层        │ ← AI模型，追踪算法
├─────────────────┤
│   数据访问层    │ ← 文件I/O，数据存储
└─────────────────┘
```

### 2. 可扩展性设计
```python
# 插件化架构示例
class DetectionPlugin:
    def detect(self, frame):
        raise NotImplementedError
        
class YOLOv11Plugin(DetectionPlugin):
    def detect(self, frame):
        return self.model.predict(frame)
        
class YOLOv8Plugin(DetectionPlugin):
    def detect(self, frame):
        return self.model(frame)

# 插件管理器
class PluginManager:
    def __init__(self):
        self.plugins = {}
        
    def register_plugin(self, name, plugin):
        self.plugins[name] = plugin
        
    def get_plugin(self, name):
        return self.plugins.get(name)
```

### 3. 错误处理与容错
```python
class RobustVideoProcessor:
    def __init__(self):
        self.retry_count = 3
        self.fallback_model = None
        
    def process_with_retry(self, frame):
        for attempt in range(self.retry_count):
            try:
                return self.model.predict(frame)
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == self.retry_count - 1:
                    return self.fallback_process(frame)
                    
    def fallback_process(self, frame):
        # 降级处理逻辑
        return self.fallback_model.predict(frame)
```

### 4. 性能优化策略
```python
# 多线程处理
class AsyncVideoProcessor:
    def __init__(self):
        self.frame_queue = Queue(maxsize=30)
        self.result_queue = Queue(maxsize=30)
        
    def start_processing(self):
        # 帧读取线程
        threading.Thread(target=self.frame_reader).start()
        
        # AI推理线程
        threading.Thread(target=self.ai_processor).start()
        
        # 结果处理线程
        threading.Thread(target=self.result_handler).start()
```

---

## 🎨 AI架构师视角

### 1. 技术选型决策
```
决策矩阵：
                 YOLOv11  YOLOv8   RCNN    SSD
准确性            ⭐⭐⭐⭐⭐  ⭐⭐⭐⭐   ⭐⭐⭐⭐⭐  ⭐⭐⭐
速度              ⭐⭐⭐⭐⭐  ⭐⭐⭐⭐   ⭐⭐     ⭐⭐⭐⭐
资源消耗          ⭐⭐⭐    ⭐⭐⭐    ⭐      ⭐⭐⭐
易用性            ⭐⭐⭐⭐⭐  ⭐⭐⭐⭐   ⭐⭐     ⭐⭐⭐
社区支持          ⭐⭐⭐⭐⭐  ⭐⭐⭐⭐⭐  ⭐⭐⭐⭐   ⭐⭐⭐

选择：YOLOv11 - 综合性能最佳
```

### 2. 架构演进路径
```
Phase 1: MVP版本
├── 基础检测功能
├── 简单计数逻辑
└── 基本Web界面

Phase 2: 功能增强
├── 多目标追踪
├── 实时统计
└── 数据可视化

Phase 3: 性能优化
├── 模型量化
├── 并行处理
└── 缓存机制

Phase 4: 企业级
├── 微服务架构
├── 容器化部署
└── 监控告警
```

### 3. 质量保证体系
```python
# 单元测试
class TestVehicleCounter(unittest.TestCase):
    def setUp(self):
        self.counter = VehicleCounter()
        
    def test_counting_accuracy(self):
        # 测试计数准确性
        pass
        
    def test_tracking_consistency(self):
        # 测试追踪一致性
        pass

# 集成测试
class TestSystemIntegration(unittest.TestCase):
    def test_end_to_end_processing(self):
        # 端到端测试
        pass
```

### 4. 监控与运维
```python
# 性能监控
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'fps': 0,
            'detection_time': 0,
            'tracking_time': 0,
            'memory_usage': 0
        }
        
    def log_metrics(self):
        logger.info(f"Performance: {self.metrics}")
        
    def alert_if_degraded(self):
        if self.metrics['fps'] < 10:
            self.send_alert("FPS too low")
```

---

## 📚 学习建议

### 初学者路径
1. **基础知识**：Python、OpenCV、深度学习基础
2. **模型理解**：YOLO系列算法原理
3. **实践项目**：从简单检测开始
4. **系统集成**：Web开发、部署运维

### 进阶方向
1. **算法优化**：模型压缩、加速推理
2. **架构设计**：微服务、分布式系统
3. **产品化**：用户体验、商业化考虑
4. **技术管理**：团队协作、项目管理

### 推荐资源
- **论文**：YOLOv11、DeepSORT原始论文
- **代码**：Ultralytics、DeepSORT官方实现
- **课程**：深度学习、计算机视觉相关课程
- **社区**：GitHub、Stack Overflow、技术博客

---

## 🎯 总结

这个YOLOv11车辆计数系统展现了现代AI应用开发的完整流程：

1. **算法层面**：深度学习模型的应用与优化
2. **工程层面**：系统架构设计与实现
3. **产品层面**：用户体验与功能完善
4. **运维层面**：部署、监控与维护

通过这个项目，可以培养：
- **技术深度**：AI算法理解与应用
- **工程能力**：系统设计与开发
- **产品思维**：用户需求与体验
- **架构视野**：可扩展、可维护的系统设计

这正是现代AI工程师和架构师需要具备的综合能力！

---

## 📝 详细代码实现

### 1. 核心检测类实现

```python
# detection/yolo_detector.py
import cv2
import numpy as np
from ultralytics import YOLO
import torch

class YOLOv11Detector:
    """YOLOv11目标检测器"""

    def __init__(self, model_path='models/yolo11n.pt', device='auto'):
        """
        初始化检测器
        Args:
            model_path: 模型权重路径
            device: 计算设备 ('cpu', 'cuda', 'auto')
        """
        self.model = YOLO(model_path)
        self.device = self._setup_device(device)
        self.model.to(self.device)

        # 类别映射 (COCO数据集)
        self.class_names = {
            0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle',
            5: 'bus', 7: 'truck'  # 只关注这些类别
        }

        # 感兴趣的类别ID
        self.target_classes = [0, 2, 3, 5, 7]  # person, car, motorcycle, bus, truck

    def _setup_device(self, device):
        """设置计算设备"""
        if device == 'auto':
            return 'cuda' if torch.cuda.is_available() else 'cpu'
        return device

    def detect(self, frame, conf_threshold=0.5):
        """
        检测单帧图像
        Args:
            frame: 输入图像 (numpy array)
            conf_threshold: 置信度阈值
        Returns:
            detections: 检测结果列表
        """
        # 模型推理
        results = self.model(frame, conf=conf_threshold, verbose=False)

        detections = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # 提取检测信息
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf = box.conf[0].cpu().numpy()
                    cls = int(box.cls[0].cpu().numpy())

                    # 只保留感兴趣的类别
                    if cls in self.target_classes:
                        detection = {
                            'bbox': [x1, y1, x2, y2],
                            'confidence': conf,
                            'class_id': cls,
                            'class_name': self.class_names.get(cls, 'unknown')
                        }
                        detections.append(detection)

        return detections

    def batch_detect(self, frames, conf_threshold=0.5):
        """批量检测多帧图像"""
        batch_results = []
        for frame in frames:
            detections = self.detect(frame, conf_threshold)
            batch_results.append(detections)
        return batch_results

    def visualize_detections(self, frame, detections):
        """可视化检测结果"""
        vis_frame = frame.copy()

        for det in detections:
            x1, y1, x2, y2 = map(int, det['bbox'])
            conf = det['confidence']
            class_name = det['class_name']

            # 绘制边界框
            color = self._get_class_color(det['class_id'])
            cv2.rectangle(vis_frame, (x1, y1), (x2, y2), color, 2)

            # 绘制标签
            label = f"{class_name}: {conf:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(vis_frame, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(vis_frame, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

        return vis_frame

    def _get_class_color(self, class_id):
        """获取类别对应的颜色"""
        colors = {
            0: (255, 0, 0),    # person - 红色
            2: (0, 255, 0),    # car - 绿色
            3: (0, 0, 255),    # motorcycle - 蓝色
            5: (255, 255, 0),  # bus - 黄色
            7: (255, 0, 255)   # truck - 紫色
        }
        return colors.get(class_id, (128, 128, 128))
```

### 2. DeepSORT追踪实现

```python
# tracking/deep_sort_tracker.py
import numpy as np
from collections import OrderedDict
import cv2

class Track:
    """单个追踪目标"""

    def __init__(self, detection, track_id):
        self.track_id = track_id
        self.class_id = detection['class_id']
        self.class_name = detection['class_name']

        # 状态管理
        self.hits = 1
        self.age = 1
        self.time_since_update = 0
        self.state = 'Tentative'  # Tentative, Confirmed, Deleted

        # 位置历史
        self.positions = []
        self.current_position = self._bbox_to_center(detection['bbox'])
        self.positions.append(self.current_position)

        # 卡尔曼滤波器 (简化版)
        self.kf = SimpleKalmanFilter()
        self.kf.initiate(detection['bbox'])

    def predict(self):
        """预测下一帧位置"""
        self.age += 1
        self.time_since_update += 1
        self.kf.predict()

    def update(self, detection):
        """更新追踪状态"""
        self.time_since_update = 0
        self.hits += 1

        # 更新位置历史
        self.current_position = self._bbox_to_center(detection['bbox'])
        self.positions.append(self.current_position)

        # 保持位置历史长度
        if len(self.positions) > 30:
            self.positions.pop(0)

        # 更新卡尔曼滤波器
        self.kf.update(detection['bbox'])

        # 状态转换
        if self.state == 'Tentative' and self.hits >= 3:
            self.state = 'Confirmed'

    def mark_missed(self):
        """标记为丢失"""
        if self.state == 'Tentative':
            self.state = 'Deleted'
        elif self.time_since_update > 30:
            self.state = 'Deleted'

    def is_confirmed(self):
        return self.state == 'Confirmed'

    def is_deleted(self):
        return self.state == 'Deleted'

    def get_current_bbox(self):
        """获取当前边界框"""
        return self.kf.get_current_bbox()

    def get_trajectory(self):
        """获取运动轨迹"""
        return self.positions.copy()

    def _bbox_to_center(self, bbox):
        """边界框转中心点"""
        x1, y1, x2, y2 = bbox
        return ((x1 + x2) / 2, (y1 + y2) / 2)

class SimpleKalmanFilter:
    """简化的卡尔曼滤波器"""

    def __init__(self):
        self.bbox = None
        self.velocity = [0, 0, 0, 0]  # dx, dy, dw, dh

    def initiate(self, bbox):
        self.bbox = bbox.copy()

    def predict(self):
        # 简单的线性预测
        self.bbox[0] += self.velocity[0]  # x
        self.bbox[1] += self.velocity[1]  # y
        self.bbox[2] += self.velocity[2]  # w
        self.bbox[3] += self.velocity[3]  # h

    def update(self, bbox):
        # 计算速度
        if self.bbox is not None:
            self.velocity[0] = bbox[0] - self.bbox[0]
            self.velocity[1] = bbox[1] - self.bbox[1]
            self.velocity[2] = bbox[2] - self.bbox[2]
            self.velocity[3] = bbox[3] - self.bbox[3]

        self.bbox = bbox.copy()

    def get_current_bbox(self):
        return self.bbox.copy() if self.bbox is not None else None

class DeepSORTTracker:
    """DeepSORT多目标追踪器"""

    def __init__(self, max_age=30, min_hits=3, iou_threshold=0.3):
        """
        初始化追踪器
        Args:
            max_age: 最大丢失帧数
            min_hits: 确认追踪的最小命中数
            iou_threshold: IoU匹配阈值
        """
        self.max_age = max_age
        self.min_hits = min_hits
        self.iou_threshold = iou_threshold

        self.tracks = []
        self.track_id_counter = 0

    def update(self, detections):
        """
        更新追踪器
        Args:
            detections: 当前帧检测结果
        Returns:
            confirmed_tracks: 确认的追踪结果
        """
        # 1. 预测所有追踪的下一帧位置
        for track in self.tracks:
            track.predict()

        # 2. 数据关联 - 匹配检测和追踪
        matched_pairs, unmatched_detections, unmatched_tracks = \
            self._associate_detections_to_tracks(detections)

        # 3. 更新匹配的追踪
        for track_idx, det_idx in matched_pairs:
            self.tracks[track_idx].update(detections[det_idx])

        # 4. 处理未匹配的追踪 (标记为丢失)
        for track_idx in unmatched_tracks:
            self.tracks[track_idx].mark_missed()

        # 5. 为未匹配的检测创建新追踪
        for det_idx in unmatched_detections:
            self._create_new_track(detections[det_idx])

        # 6. 删除过期的追踪
        self.tracks = [t for t in self.tracks if not t.is_deleted()]

        # 7. 返回确认的追踪结果
        confirmed_tracks = [t for t in self.tracks if t.is_confirmed()]
        return confirmed_tracks

    def _associate_detections_to_tracks(self, detections):
        """数据关联算法"""
        if len(self.tracks) == 0:
            return [], list(range(len(detections))), []

        # 计算IoU矩阵
        iou_matrix = self._compute_iou_matrix(detections)

        # 匈牙利算法匹配 (简化版 - 贪心匹配)
        matched_pairs = []
        unmatched_detections = list(range(len(detections)))
        unmatched_tracks = list(range(len(self.tracks)))

        # 贪心匹配
        for i, track in enumerate(self.tracks):
            if i not in unmatched_tracks:
                continue

            best_match = -1
            best_iou = self.iou_threshold

            for j in unmatched_detections:
                iou = iou_matrix[i][j]
                if iou > best_iou:
                    best_iou = iou
                    best_match = j

            if best_match != -1:
                matched_pairs.append((i, best_match))
                unmatched_tracks.remove(i)
                unmatched_detections.remove(best_match)

        return matched_pairs, unmatched_detections, unmatched_tracks

    def _compute_iou_matrix(self, detections):
        """计算IoU矩阵"""
        iou_matrix = np.zeros((len(self.tracks), len(detections)))

        for i, track in enumerate(self.tracks):
            track_bbox = track.get_current_bbox()
            if track_bbox is None:
                continue

            for j, detection in enumerate(detections):
                det_bbox = detection['bbox']
                iou = self._compute_iou(track_bbox, det_bbox)
                iou_matrix[i][j] = iou

        return iou_matrix

    def _compute_iou(self, bbox1, bbox2):
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _create_new_track(self, detection):
        """创建新的追踪"""
        track = Track(detection, self.track_id_counter)
        self.track_id_counter += 1
        self.tracks.append(track)

    def get_track_count_by_class(self):
        """按类别统计追踪数量"""
        count_dict = {}
        for track in self.tracks:
            if track.is_confirmed():
                class_name = track.class_name
                count_dict[class_name] = count_dict.get(class_name, 0) + 1
        return count_dict
```

### 3. 智能计数系统

```python
# counting/smart_counter.py
import cv2
import numpy as np
from collections import defaultdict, deque
import time

class VirtualLine:
    """虚拟计数线"""

    def __init__(self, start_point, end_point, direction='both'):
        """
        初始化虚拟线
        Args:
            start_point: 起始点 (x, y)
            end_point: 结束点 (x, y)
            direction: 计数方向 ('up', 'down', 'left', 'right', 'both')
        """
        self.start_point = start_point
        self.end_point = end_point
        self.direction = direction

        # 计算线的参数
        self.line_vector = np.array(end_point) - np.array(start_point)
        self.line_length = np.linalg.norm(self.line_vector)
        self.line_normal = np.array([-self.line_vector[1], self.line_vector[0]])
        self.line_normal = self.line_normal / np.linalg.norm(self.line_normal)

    def check_crossing(self, prev_pos, curr_pos):
        """
        检查轨迹是否穿越虚拟线
        Args:
            prev_pos: 前一帧位置 (x, y)
            curr_pos: 当前帧位置 (x, y)
        Returns:
            crossed: 是否穿越
            direction: 穿越方向
        """
        if prev_pos is None or curr_pos is None:
            return False, None

        # 检查线段相交
        if not self._line_segments_intersect(prev_pos, curr_pos):
            return False, None

        # 计算穿越方向
        cross_direction = self._get_crossing_direction(prev_pos, curr_pos)

        # 检查是否符合设定的计数方向
        if self.direction == 'both' or cross_direction == self.direction:
            return True, cross_direction

        return False, None

    def _line_segments_intersect(self, p1, p2):
        """检查两条线段是否相交"""
        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])

        A, B = self.start_point, self.end_point
        C, D = p1, p2

        return ccw(A, C, D) != ccw(B, C, D) and ccw(A, B, C) != ccw(A, B, D)

    def _get_crossing_direction(self, prev_pos, curr_pos):
        """获取穿越方向"""
        # 计算位置相对于线的方向
        prev_side = np.dot(np.array(prev_pos) - np.array(self.start_point), self.line_normal)
        curr_side = np.dot(np.array(curr_pos) - np.array(self.start_point), self.line_normal)

        if prev_side < 0 and curr_side > 0:
            return 'up' if self.line_normal[1] > 0 else 'right'
        elif prev_side > 0 and curr_side < 0:
            return 'down' if self.line_normal[1] > 0 else 'left'
        else:
            return 'unknown'

    def draw(self, frame, color=(0, 255, 255), thickness=3):
        """在图像上绘制虚拟线"""
        cv2.line(frame, self.start_point, self.end_point, color, thickness)

        # 绘制方向箭头
        if self.direction != 'both':
            mid_point = ((self.start_point[0] + self.end_point[0]) // 2,
                        (self.start_point[1] + self.end_point[1]) // 2)
            arrow_length = 20

            if self.direction in ['up', 'down']:
                arrow_end = (mid_point[0],
                           mid_point[1] - arrow_length if self.direction == 'up' else mid_point[1] + arrow_length)
            else:  # left, right
                arrow_end = (mid_point[0] - arrow_length if self.direction == 'left' else mid_point[0] + arrow_length,
                           mid_point[1])

            cv2.arrowedLine(frame, mid_point, arrow_end, color, thickness)

class SmartCounter:
    """智能计数系统"""

    def __init__(self, counting_lines=None):
        """
        初始化计数器
        Args:
            counting_lines: 计数线列表
        """
        self.counting_lines = counting_lines or []

        # 计数统计
        self.counts = defaultdict(lambda: defaultdict(int))  # {class_name: {direction: count}}
        self.total_counts = defaultdict(int)  # {class_name: total_count}

        # 追踪历史
        self.track_histories = defaultdict(deque)  # {track_id: deque of positions}
        self.crossed_tracks = defaultdict(set)  # {line_idx: set of track_ids}

        # 统计历史
        self.count_history = deque(maxlen=1000)  # 保存最近1000次计数记录

    def add_counting_line(self, start_point, end_point, direction='both'):
        """添加计数线"""
        line = VirtualLine(start_point, end_point, direction)
        self.counting_lines.append(line)
        return len(self.counting_lines) - 1  # 返回线的索引

    def update(self, tracks):
        """
        更新计数器
        Args:
            tracks: 当前帧的追踪结果
        Returns:
            count_updates: 新增的计数信息
        """
        count_updates = []

        for track in tracks:
            track_id = track.track_id
            current_pos = track.current_position
            class_name = track.class_name

            # 更新追踪历史
            self.track_histories[track_id].append(current_pos)
            if len(self.track_histories[track_id]) > 30:  # 保持最近30帧
                self.track_histories[track_id].popleft()

            # 检查是否穿越计数线
            if len(self.track_histories[track_id]) >= 2:
                prev_pos = self.track_histories[track_id][-2]

                for line_idx, counting_line in enumerate(self.counting_lines):
                    # 避免重复计数
                    if track_id in self.crossed_tracks[line_idx]:
                        continue

                    crossed, direction = counting_line.check_crossing(prev_pos, current_pos)

                    if crossed:
                        # 记录穿越
                        self.crossed_tracks[line_idx].add(track_id)

                        # 更新计数
                        self.counts[class_name][direction] += 1
                        self.total_counts[class_name] += 1

                        # 记录计数事件
                        count_event = {
                            'timestamp': time.time(),
                            'track_id': track_id,
                            'class_name': class_name,
                            'direction': direction,
                            'line_idx': line_idx,
                            'position': current_pos
                        }
                        self.count_history.append(count_event)
                        count_updates.append(count_event)

        # 清理过期的追踪历史
        self._cleanup_expired_tracks(tracks)

        return count_updates

    def _cleanup_expired_tracks(self, current_tracks):
        """清理过期的追踪历史"""
        current_track_ids = {track.track_id for track in current_tracks}

        # 清理追踪历史
        expired_track_ids = set(self.track_histories.keys()) - current_track_ids
        for track_id in expired_track_ids:
            del self.track_histories[track_id]

        # 清理穿越记录
        for line_idx in self.crossed_tracks:
            self.crossed_tracks[line_idx] &= current_track_ids

    def get_current_counts(self):
        """获取当前计数统计"""
        return {
            'detailed_counts': dict(self.counts),
            'total_counts': dict(self.total_counts),
            'timestamp': time.time()
        }

    def get_count_history(self, limit=100):
        """获取计数历史"""
        return list(self.count_history)[-limit:]

    def reset_counts(self):
        """重置计数"""
        self.counts.clear()
        self.total_counts.clear()
        self.crossed_tracks.clear()
        self.count_history.clear()

    def draw_counting_lines(self, frame):
        """在图像上绘制所有计数线"""
        for i, line in enumerate(self.counting_lines):
            color = (0, 255, 255)  # 黄色
            line.draw(frame, color)

            # 绘制线的编号
            mid_point = ((line.start_point[0] + line.end_point[0]) // 2,
                        (line.start_point[1] + line.end_point[1]) // 2)
            cv2.putText(frame, f"Line {i+1}",
                       (mid_point[0] - 30, mid_point[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    def draw_statistics(self, frame):
        """在图像上绘制统计信息"""
        y_offset = 30

        # 绘制总计数
        for class_name, count in self.total_counts.items():
            text = f"{class_name}: {count}"
            cv2.putText(frame, text, (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y_offset += 30

        # 绘制详细计数
        y_offset += 10
        for class_name, direction_counts in self.counts.items():
            for direction, count in direction_counts.items():
                if count > 0:
                    text = f"{class_name} ({direction}): {count}"
                    cv2.putText(frame, text, (10, y_offset),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
                    y_offset += 20

### 4. Flask Web应用完整实现

```python
# web/app.py
from flask import Flask, render_template, request, jsonify, send_file
from flask_socketio import SocketIO, emit
import cv2
import base64
import threading
import time
import os
import uuid
from werkzeug.utils import secure_filename
import json

# 导入自定义模块
from detection.yolo_detector import YOLOv11Detector
from tracking.deep_sort_tracker import DeepSORTTracker
from counting.smart_counter import SmartCounter

class VehicleCounterWebApp:
    """车辆计数Web应用"""

    def __init__(self):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'your-secret-key-here'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # 初始化AI组件
        self.detector = YOLOv11Detector('models/yolo11n.pt')
        self.tracker = DeepSORTTracker()
        self.counter = SmartCounter()

        # 应用状态
        self.processing_active = False
        self.current_video_path = None
        self.processing_thread = None

        # 配置文件上传
        self.app.config['UPLOAD_FOLDER'] = 'uploads'
        self.app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB

        # 确保上传目录存在
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)

        self._setup_routes()
        self._setup_socketio_events()

    def _setup_routes(self):
        """设置路由"""

        @self.app.route('/')
        def index():
            return render_template('index.html')

        @self.app.route('/upload', methods=['POST'])
        def upload_video():
            """视频上传接口"""
            try:
                if 'video' not in request.files:
                    return jsonify({'error': '没有选择文件'}), 400

                file = request.files['video']
                if file.filename == '':
                    return jsonify({'error': '没有选择文件'}), 400

                if file and self._allowed_file(file.filename):
                    # 生成安全的文件名
                    filename = secure_filename(file.filename)
                    unique_filename = f"{uuid.uuid4()}_{filename}"
                    file_path = os.path.join(self.app.config['UPLOAD_FOLDER'], unique_filename)

                    # 保存文件
                    file.save(file_path)

                    return jsonify({
                        'success': True,
                        'filename': unique_filename,
                        'file_path': file_path
                    })
                else:
                    return jsonify({'error': '不支持的文件格式'}), 400

            except Exception as e:
                return jsonify({'error': f'上传失败: {str(e)}'}), 500

        @self.app.route('/api/start_processing', methods=['POST'])
        def start_processing():
            """开始处理视频"""
            try:
                data = request.get_json()
                video_path = data.get('video_path')

                if not video_path or not os.path.exists(video_path):
                    return jsonify({'error': '视频文件不存在'}), 400

                if self.processing_active:
                    return jsonify({'error': '正在处理其他视频'}), 400

                # 设置计数线 (示例)
                self.counter.add_counting_line((300, 200), (300, 400), 'both')

                # 开始处理
                self.current_video_path = video_path
                self.processing_active = True

                self.processing_thread = threading.Thread(
                    target=self._process_video_async,
                    args=(video_path,)
                )
                self.processing_thread.start()

                return jsonify({'success': True, 'message': '开始处理视频'})

            except Exception as e:
                return jsonify({'error': f'处理失败: {str(e)}'}), 500

        @self.app.route('/api/stop_processing', methods=['POST'])
        def stop_processing():
            """停止处理视频"""
            self.processing_active = False
            return jsonify({'success': True, 'message': '停止处理'})

        @self.app.route('/api/get_statistics', methods=['GET'])
        def get_statistics():
            """获取统计信息"""
            stats = self.counter.get_current_counts()
            return jsonify(stats)

        @self.app.route('/api/reset_counter', methods=['POST'])
        def reset_counter():
            """重置计数器"""
            self.counter.reset_counts()
            return jsonify({'success': True, 'message': '计数器已重置'})

    def _setup_socketio_events(self):
        """设置WebSocket事件"""

        @self.socketio.on('connect')
        def handle_connect():
            print('客户端已连接')
            emit('status', {'message': '连接成功'})

        @self.socketio.on('disconnect')
        def handle_disconnect():
            print('客户端已断开连接')

        @self.socketio.on('request_frame')
        def handle_frame_request():
            """客户端请求帧数据"""
            if hasattr(self, 'current_frame_data'):
                emit('frame_data', self.current_frame_data)

    def _allowed_file(self, filename):
        """检查文件格式是否允许"""
        allowed_extensions = {'mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv'}
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in allowed_extensions

    def _process_video_async(self, video_path):
        """异步处理视频"""
        try:
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                self.socketio.emit('error', {'message': '无法打开视频文件'})
                return

            # 获取视频信息
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            self.socketio.emit('video_info', {
                'fps': fps,
                'total_frames': total_frames,
                'duration': total_frames / fps if fps > 0 else 0
            })

            frame_count = 0
            start_time = time.time()

            while cap.isOpened() and self.processing_active:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 处理帧
                processed_frame, detection_data = self._process_frame(frame)

                # 编码帧为base64
                _, buffer = cv2.imencode('.jpg', processed_frame,
                                       [cv2.IMWRITE_JPEG_QUALITY, 80])
                frame_base64 = base64.b64encode(buffer).decode('utf-8')

                # 准备发送数据
                self.current_frame_data = {
                    'frame': frame_base64,
                    'frame_number': frame_count,
                    'detections': detection_data['detections'],
                    'tracks': detection_data['tracks'],
                    'counts': detection_data['counts'],
                    'processing_fps': frame_count / (time.time() - start_time)
                }

                # 发送到客户端
                self.socketio.emit('frame_update', self.current_frame_data)

                # 控制发送频率
                time.sleep(1.0 / min(fps, 10))  # 最大10fps发送

            cap.release()
            self.processing_active = False
            self.socketio.emit('processing_complete', {'message': '视频处理完成'})

        except Exception as e:
            self.processing_active = False
            self.socketio.emit('error', {'message': f'处理错误: {str(e)}'})

    def _process_frame(self, frame):
        """处理单帧"""
        # 1. 目标检测
        detections = self.detector.detect(frame, conf_threshold=0.5)

        # 2. 目标追踪
        tracks = self.tracker.update(detections)

        # 3. 计数更新
        count_updates = self.counter.update(tracks)

        # 4. 可视化
        vis_frame = frame.copy()

        # 绘制检测结果
        vis_frame = self.detector.visualize_detections(vis_frame, detections)

        # 绘制追踪轨迹
        for track in tracks:
            trajectory = track.get_trajectory()
            if len(trajectory) > 1:
                points = np.array(trajectory, dtype=np.int32)
                cv2.polylines(vis_frame, [points], False, (255, 0, 0), 2)

                # 绘制追踪ID
                if len(trajectory) > 0:
                    last_point = trajectory[-1]
                    cv2.putText(vis_frame, f"ID:{track.track_id}",
                               (int(last_point[0]), int(last_point[1]) - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

        # 绘制计数线和统计信息
        self.counter.draw_counting_lines(vis_frame)
        self.counter.draw_statistics(vis_frame)

        # 准备返回数据
        detection_data = {
            'detections': [
                {
                    'bbox': det['bbox'],
                    'class_name': det['class_name'],
                    'confidence': float(det['confidence'])
                } for det in detections
            ],
            'tracks': [
                {
                    'track_id': track.track_id,
                    'class_name': track.class_name,
                    'bbox': track.get_current_bbox(),
                    'trajectory': track.get_trajectory()
                } for track in tracks
            ],
            'counts': self.counter.get_current_counts(),
            'count_updates': count_updates
        }

        return vis_frame, detection_data

    def run(self, host='0.0.0.0', port=5000, debug=False):
        """运行应用"""
        self.socketio.run(self.app, host=host, port=port, debug=debug)

# 应用入口
if __name__ == '__main__':
    app = VehicleCounterWebApp()
    app.run(debug=True)
```

### 5. 前端界面实现

```html
<!-- templates/index.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLOv11车辆计数系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #video-display {
            max-width: 100%;
            max-height: 500px;
            border-radius: 15px;
        }

        .stats-card {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected { background-color: #2ecc71; }
        .status-processing { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-idle { background-color: #95a5a6; }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background-color: rgba(102, 126, 234, 0.05);
        }

        .upload-area.dragover {
            border-color: #667eea;
            background-color: rgba(102, 126, 234, 0.1);
        }

        .progress-container {
            margin-top: 20px;
            display: none;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .detection-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .track-item {
            background: rgba(0, 123, 255, 0.1);
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 标题栏 -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="text-center mb-3">
                        <i class="fas fa-car text-primary"></i>
                        YOLOv11车辆计数系统
                    </h1>
                    <div class="text-center">
                        <span class="status-indicator status-idle" id="status-indicator"></span>
                        <span id="status-text">系统就绪</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 左侧：视频显示区域 -->
                <div class="col-lg-8">
                    <div class="video-container mb-4">
                        <img id="video-display" src="" alt="视频显示区域" style="display: none;">
                        <div id="upload-prompt" class="text-center text-white">
                            <i class="fas fa-video fa-3x mb-3"></i>
                            <h4>请上传视频文件开始分析</h4>
                        </div>
                    </div>

                    <!-- 控制面板 -->
                    <div class="control-panel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="upload-area" id="upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-3"></i>
                                    <p class="mb-2">点击或拖拽上传视频文件</p>
                                    <small class="text-muted">支持 MP4, AVI, MOV 等格式</small>
                                    <input type="file" id="video-input" accept="video/*" style="display: none;">
                                </div>

                                <div class="progress-container" id="progress-container">
                                    <div class="progress">
                                        <div class="progress-bar" id="upload-progress" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted mt-2" id="progress-text">准备上传...</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary-custom btn-custom" id="start-btn" disabled>
                                        <i class="fas fa-play"></i> 开始分析
                                    </button>
                                    <button class="btn btn-warning btn-custom" id="pause-btn" disabled>
                                        <i class="fas fa-pause"></i> 暂停
                                    </button>
                                    <button class="btn btn-danger btn-custom" id="stop-btn" disabled>
                                        <i class="fas fa-stop"></i> 停止
                                    </button>
                                    <button class="btn btn-secondary btn-custom" id="reset-btn">
                                        <i class="fas fa-redo"></i> 重置计数
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：统计信息 -->
                <div class="col-lg-4">
                    <!-- 实时统计 -->
                    <div class="stats-card">
                        <h5><i class="fas fa-chart-bar"></i> 实时统计</h5>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stats-number" id="car-count">0</div>
                                <div>车辆</div>
                            </div>
                            <div class="col-6">
                                <div class="stats-number" id="people-count">0</div>
                                <div>行人</div>
                            </div>
                        </div>
                    </div>

                    <!-- 处理信息 -->
                    <div class="detection-info">
                        <h6><i class="fas fa-info-circle"></i> 处理信息</h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">处理帧数</small>
                                <div id="frame-count">0</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">处理速度</small>
                                <div id="processing-fps">0 FPS</div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <small class="text-muted">检测目标</small>
                                <div id="detection-count">0</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">追踪目标</small>
                                <div id="track-count">0</div>
                            </div>
                        </div>
                    </div>

                    <!-- 当前追踪目标 -->
                    <div class="detection-info">
                        <h6><i class="fas fa-crosshairs"></i> 当前追踪</h6>
                        <div id="current-tracks" style="max-height: 200px; overflow-y: auto;">
                            <p class="text-muted text-center">暂无追踪目标</p>
                        </div>
                    </div>

                    <!-- 系统日志 -->
                    <div class="mt-3">
                        <h6><i class="fas fa-terminal"></i> 系统日志</h6>
                        <div class="log-container" id="system-log">
                            <div>系统初始化完成</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
```

### 6. JavaScript前端逻辑

```javascript
// static/js/main.js
class VehicleCounterApp {
    constructor() {
        this.socket = io();
        this.isProcessing = false;
        this.currentVideoPath = null;

        this.initializeElements();
        this.setupEventListeners();
        this.setupSocketEvents();
        this.setupDragAndDrop();

        this.log('系统初始化完成');
    }

    initializeElements() {
        // 获取DOM元素
        this.elements = {
            videoDisplay: document.getElementById('video-display'),
            uploadPrompt: document.getElementById('upload-prompt'),
            videoInput: document.getElementById('video-input'),
            uploadArea: document.getElementById('upload-area'),
            progressContainer: document.getElementById('progress-container'),
            uploadProgress: document.getElementById('upload-progress'),
            progressText: document.getElementById('progress-text'),

            startBtn: document.getElementById('start-btn'),
            pauseBtn: document.getElementById('pause-btn'),
            stopBtn: document.getElementById('stop-btn'),
            resetBtn: document.getElementById('reset-btn'),

            statusIndicator: document.getElementById('status-indicator'),
            statusText: document.getElementById('status-text'),

            carCount: document.getElementById('car-count'),
            peopleCount: document.getElementById('people-count'),
            frameCount: document.getElementById('frame-count'),
            processingFps: document.getElementById('processing-fps'),
            detectionCount: document.getElementById('detection-count'),
            trackCount: document.getElementById('track-count'),
            currentTracks: document.getElementById('current-tracks'),
            systemLog: document.getElementById('system-log')
        };
    }

    setupEventListeners() {
        // 文件上传
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.videoInput.click();
        });

        this.elements.videoInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.uploadVideo(e.target.files[0]);
            }
        });

        // 控制按钮
        this.elements.startBtn.addEventListener('click', () => this.startProcessing());
        this.elements.pauseBtn.addEventListener('click', () => this.pauseProcessing());
        this.elements.stopBtn.addEventListener('click', () => this.stopProcessing());
        this.elements.resetBtn.addEventListener('click', () => this.resetCounter());
    }

    setupSocketEvents() {
        this.socket.on('connect', () => {
            this.updateStatus('connected', '已连接到服务器');
            this.log('WebSocket连接成功');
        });

        this.socket.on('disconnect', () => {
            this.updateStatus('error', '与服务器断开连接');
            this.log('WebSocket连接断开', 'error');
        });

        this.socket.on('frame_update', (data) => {
            this.updateFrame(data);
        });

        this.socket.on('video_info', (data) => {
            this.log(`视频信息: ${data.total_frames}帧, ${data.fps}FPS, 时长${data.duration.toFixed(1)}秒`);
        });

        this.socket.on('processing_complete', (data) => {
            this.updateStatus('idle', '处理完成');
            this.log('视频处理完成');
            this.isProcessing = false;
            this.updateButtonStates();
        });

        this.socket.on('error', (data) => {
            this.updateStatus('error', '处理错误');
            this.log(`错误: ${data.message}`, 'error');
            this.isProcessing = false;
            this.updateButtonStates();
        });
    }

    setupDragAndDrop() {
        const uploadArea = this.elements.uploadArea;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('dragover');
            }, false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.uploadVideo(files[0]);
            }
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    async uploadVideo(file) {
        // 检查文件类型
        if (!file.type.startsWith('video/')) {
            this.log('请选择视频文件', 'error');
            return;
        }

        // 检查文件大小 (500MB)
        if (file.size > 500 * 1024 * 1024) {
            this.log('文件大小超过500MB限制', 'error');
            return;
        }

        this.log(`开始上传: ${file.name} (${(file.size / 1024 / 1024).toFixed(1)}MB)`);

        // 显示进度条
        this.elements.progressContainer.style.display = 'block';
        this.elements.uploadProgress.style.width = '0%';
        this.elements.progressText.textContent = '准备上传...';

        const formData = new FormData();
        formData.append('video', file);

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                this.currentVideoPath = result.file_path;
                this.elements.startBtn.disabled = false;
                this.log(`上传成功: ${result.filename}`);
                this.elements.progressContainer.style.display = 'none';
            } else {
                const error = await response.json();
                this.log(`上传失败: ${error.error}`, 'error');
            }
        } catch (error) {
            this.log(`上传错误: ${error.message}`, 'error');
        }
    }

    async startProcessing() {
        if (!this.currentVideoPath) {
            this.log('请先上传视频文件', 'error');
            return;
        }

        try {
            const response = await fetch('/api/start_processing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    video_path: this.currentVideoPath
                })
            });

            if (response.ok) {
                this.isProcessing = true;
                this.updateStatus('processing', '正在处理视频');
                this.log('开始处理视频');
                this.updateButtonStates();
            } else {
                const error = await response.json();
                this.log(`启动失败: ${error.error}`, 'error');
            }
        } catch (error) {
            this.log(`请求错误: ${error.message}`, 'error');
        }
    }

    async stopProcessing() {
        try {
            const response = await fetch('/api/stop_processing', {
                method: 'POST'
            });

            if (response.ok) {
                this.isProcessing = false;
                this.updateStatus('idle', '已停止处理');
                this.log('停止处理视频');
                this.updateButtonStates();
            }
        } catch (error) {
            this.log(`停止失败: ${error.message}`, 'error');
        }
    }

    async resetCounter() {
        try {
            const response = await fetch('/api/reset_counter', {
                method: 'POST'
            });

            if (response.ok) {
                this.elements.carCount.textContent = '0';
                this.elements.peopleCount.textContent = '0';
                this.log('计数器已重置');
            }
        } catch (error) {
            this.log(`重置失败: ${error.message}`, 'error');
        }
    }

    updateFrame(data) {
        // 更新视频显示
        if (data.frame) {
            this.elements.videoDisplay.src = `data:image/jpeg;base64,${data.frame}`;
            this.elements.videoDisplay.style.display = 'block';
            this.elements.uploadPrompt.style.display = 'none';
        }

        // 更新统计信息
        if (data.counts && data.counts.total_counts) {
            this.elements.carCount.textContent = data.counts.total_counts.car || 0;
            this.elements.peopleCount.textContent = data.counts.total_counts.person || 0;
        }

        // 更新处理信息
        this.elements.frameCount.textContent = data.frame_number || 0;
        this.elements.processingFps.textContent = `${(data.processing_fps || 0).toFixed(1)} FPS`;
        this.elements.detectionCount.textContent = data.detections ? data.detections.length : 0;
        this.elements.trackCount.textContent = data.tracks ? data.tracks.length : 0;

        // 更新当前追踪目标
        this.updateCurrentTracks(data.tracks || []);
    }

    updateCurrentTracks(tracks) {
        const container = this.elements.currentTracks;

        if (tracks.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">暂无追踪目标</p>';
            return;
        }

        const trackHtml = tracks.map(track => `
            <div class="track-item">
                <div class="d-flex justify-content-between">
                    <span><strong>ID ${track.track_id}</strong></span>
                    <span class="badge bg-primary">${track.class_name}</span>
                </div>
                <small class="text-muted">
                    轨迹点: ${track.trajectory ? track.trajectory.length : 0}
                </small>
            </div>
        `).join('');

        container.innerHTML = trackHtml;
    }

    updateStatus(status, message) {
        const indicator = this.elements.statusIndicator;
        const text = this.elements.statusText;

        // 移除所有状态类
        indicator.className = 'status-indicator';

        // 添加新状态类
        switch (status) {
            case 'connected':
                indicator.classList.add('status-connected');
                break;
            case 'processing':
                indicator.classList.add('status-processing');
                break;
            case 'error':
                indicator.classList.add('status-error');
                break;
            default:
                indicator.classList.add('status-idle');
        }

        text.textContent = message;
    }

    updateButtonStates() {
        this.elements.startBtn.disabled = !this.currentVideoPath || this.isProcessing;
        this.elements.pauseBtn.disabled = !this.isProcessing;
        this.elements.stopBtn.disabled = !this.isProcessing;
    }

    log(message, type = 'info') {
        const logContainer = this.elements.systemLog;
        const timestamp = new Date().toLocaleTimeString();

        const logEntry = document.createElement('div');
        logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;

        if (type === 'error') {
            logEntry.style.color = '#e74c3c';
        } else if (type === 'success') {
            logEntry.style.color = '#2ecc71';
        }

        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;

        // 保持日志数量限制
        while (logContainer.children.length > 100) {
            logContainer.removeChild(logContainer.firstChild);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new VehicleCounterApp();
});
```

### 7. 部署配置文件

```python
# deployment/gunicorn_config.py
"""Gunicorn配置文件"""

import multiprocessing
import os

# 服务器套接字
bind = "127.0.0.1:5000"
backlog = 2048

# 工作进程
workers = min(2, multiprocessing.cpu_count())  # 限制工作进程数
worker_class = "eventlet"  # 支持WebSocket
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 超时设置
timeout = 300  # 5分钟超时
keepalive = 2
graceful_timeout = 30

# 日志设置
accesslog = "/var/log/vehicle-counter/access.log"
errorlog = "/var/log/vehicle-counter/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程命名
proc_name = "vehicle-counter"

# 安全设置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# 预加载应用
preload_app = True

# 临时目录
tmp_upload_dir = "/tmp"

# SSL设置 (如果需要)
# keyfile = "/path/to/private.key"
# certfile = "/path/to/certificate.crt"

def when_ready(server):
    """服务器启动时的回调"""
    server.log.info("Vehicle Counter Server is ready. Listening on: %s", server.address)

def worker_int(worker):
    """工作进程中断时的回调"""
    worker.log.info("worker received INT or QUIT signal")

def pre_fork(server, worker):
    """工作进程fork前的回调"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    """工作进程fork后的回调"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def worker_abort(worker):
    """工作进程异常终止时的回调"""
    worker.log.info("worker received SIGABRT signal")
```

```apache
# deployment/apache_vhost.conf
<VirtualHost *:443>
    ServerName vehicle.smart-traffic.top
    DocumentRoot /var/www/vehicle-counter

    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/vehicle.smart-traffic.top.crt
    SSLCertificateKeyFile /etc/ssl/private/vehicle.smart-traffic.top.key
    SSLCertificateChainFile /etc/ssl/certs/vehicle.smart-traffic.top.ca-bundle

    # 安全头
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # 代理设置
    ProxyPreserveHost On
    ProxyRequests Off

    # 主应用代理
    ProxyPass /static/ !
    ProxyPass /uploads/ !
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/

    # WebSocket支持
    RewriteEngine On
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/?(.*) "ws://127.0.0.1:5000/$1" [P,L]

    # 静态文件服务
    Alias /static /var/www/vehicle-counter/static
    <Directory "/var/www/vehicle-counter/static">
        Require all granted
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </Directory>

    # 上传文件服务
    Alias /uploads /var/www/vehicle-counter/uploads
    <Directory "/var/www/vehicle-counter/uploads">
        Require all granted
        Options -Indexes
    </Directory>

    # 日志设置
    ErrorLog /var/log/apache2/vehicle-counter-error.log
    CustomLog /var/log/apache2/vehicle-counter-access.log combined
    LogLevel info

    # 文件上传限制
    LimitRequestBody 524288000  # 500MB

    # Gzip压缩
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png|ico|mp4|avi|mov)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
</VirtualHost>

# HTTP重定向到HTTPS
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    Redirect permanent / https://vehicle.smart-traffic.top/
</VirtualHost>
```

### 8. 自动化部署脚本

```bash
#!/bin/bash
# deployment/deploy.sh - 自动化部署脚本

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_NAME="vehicle-counter"
PROJECT_DIR="/var/www/$PROJECT_NAME"
BACKUP_DIR="/var/backups/$PROJECT_NAME"
LOG_DIR="/var/log/$PROJECT_NAME"
VENV_DIR="$PROJECT_DIR/venv"
REPO_URL="https://github.com/your-username/vehicle-counter.git"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."

    apt-get update
    apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        apache2 \
        libapache2-mod-wsgi-py3 \
        supervisor \
        git \
        curl \
        wget \
        unzip \
        build-essential \
        cmake \
        pkg-config \
        libjpeg-dev \
        libpng-dev \
        libtiff-dev \
        libavcodec-dev \
        libavformat-dev \
        libswscale-dev \
        libv4l-dev \
        libxvidcore-dev \
        libx264-dev \
        libgtk-3-dev \
        libatlas-base-dev \
        gfortran

    log_info "系统依赖安装完成"
}

# 创建项目目录结构
create_directories() {
    log_info "创建项目目录结构..."

    mkdir -p $PROJECT_DIR
    mkdir -p $BACKUP_DIR
    mkdir -p $LOG_DIR
    mkdir -p $PROJECT_DIR/uploads
    mkdir -p $PROJECT_DIR/models
    mkdir -p $PROJECT_DIR/static
    mkdir -p $PROJECT_DIR/templates

    # 设置权限
    chown -R www-data:www-data $PROJECT_DIR
    chown -R www-data:www-data $LOG_DIR
    chmod -R 755 $PROJECT_DIR
    chmod -R 755 $LOG_DIR

    log_info "目录结构创建完成"
}

# 备份现有部署
backup_existing() {
    if [ -d "$PROJECT_DIR" ]; then
        log_info "备份现有部署..."

        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        cp -r $PROJECT_DIR $BACKUP_DIR/$BACKUP_NAME

        log_info "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# 部署代码
deploy_code() {
    log_info "部署应用代码..."

    cd $PROJECT_DIR

    # 如果是Git仓库，则拉取更新
    if [ -d ".git" ]; then
        git pull origin main
    else
        # 否则克隆仓库
        git clone $REPO_URL .
    fi

    log_info "代码部署完成"
}

# 设置Python虚拟环境
setup_python_env() {
    log_info "设置Python虚拟环境..."

    cd $PROJECT_DIR

    # 创建虚拟环境
    python3 -m venv $VENV_DIR
    source $VENV_DIR/bin/activate

    # 升级pip
    pip install --upgrade pip

    # 安装Python依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    else
        # 手动安装核心依赖
        pip install \
            flask \
            flask-socketio \
            opencv-python \
            ultralytics \
            numpy \
            pillow \
            gunicorn \
            eventlet
    fi

    # 下载YOLO模型
    if [ ! -f "models/yolo11n.pt" ]; then
        log_info "下载YOLOv11模型..."
        python -c "from ultralytics import YOLO; YOLO('yolo11n.pt')"
        mv yolo11n.pt models/
    fi

    deactivate

    log_info "Python环境设置完成"
}

# 配置Apache
configure_apache() {
    log_info "配置Apache..."

    # 启用必要的模块
    a2enmod ssl
    a2enmod rewrite
    a2enmod headers
    a2enmod proxy
    a2enmod proxy_http
    a2enmod proxy_wstunnel

    # 复制虚拟主机配置
    if [ -f "deployment/apache_vhost.conf" ]; then
        cp deployment/apache_vhost.conf /etc/apache2/sites-available/$PROJECT_NAME.conf
        a2ensite $PROJECT_NAME.conf
    fi

    # 测试配置
    apache2ctl configtest

    log_info "Apache配置完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor..."

    cat > /etc/supervisor/conf.d/$PROJECT_NAME.conf << EOF
[program:$PROJECT_NAME]
command=$VENV_DIR/bin/gunicorn -c deployment/gunicorn_config.py app:app
directory=$PROJECT_DIR
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$LOG_DIR/gunicorn.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PATH="$VENV_DIR/bin"
EOF

    # 重新加载Supervisor配置
    supervisorctl reread
    supervisorctl update

    log_info "Supervisor配置完成"
}

# 设置日志轮转
setup_log_rotation() {
    log_info "设置日志轮转..."

    cat > /etc/logrotate.d/$PROJECT_NAME << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        supervisorctl restart $PROJECT_NAME
    endscript
}
EOF

    log_info "日志轮转设置完成"
}

# 设置防火墙
setup_firewall() {
    log_info "配置防火墙..."

    # 如果使用ufw
    if command -v ufw &> /dev/null; then
        ufw allow 22/tcp
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw --force enable
    fi

    log_info "防火墙配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."

    # 启动Supervisor
    systemctl enable supervisor
    systemctl start supervisor

    # 启动应用
    supervisorctl start $PROJECT_NAME

    # 重启Apache
    systemctl restart apache2

    log_info "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."

    # 检查应用进程
    if supervisorctl status $PROJECT_NAME | grep -q "RUNNING"; then
        log_info "应用进程运行正常"
    else
        log_error "应用进程未运行"
        return 1
    fi

    # 检查端口监听
    if netstat -tlnp | grep -q ":5000"; then
        log_info "应用端口监听正常"
    else
        log_error "应用端口未监听"
        return 1
    fi

    # 检查HTTP响应
    if curl -f -s http://localhost:5000/ > /dev/null; then
        log_info "HTTP响应正常"
    else
        log_error "HTTP响应异常"
        return 1
    fi

    log_info "健康检查通过"
}

# 主函数
main() {
    log_info "开始部署 $PROJECT_NAME..."

    check_root
    backup_existing
    install_system_dependencies
    create_directories
    deploy_code
    setup_python_env
    configure_apache
    configure_supervisor
    setup_log_rotation
    setup_firewall
    start_services

    sleep 5  # 等待服务启动

    if health_check; then
        log_info "部署成功完成！"
        log_info "应用访问地址: https://your-domain.com"
        log_info "日志目录: $LOG_DIR"
        log_info "项目目录: $PROJECT_DIR"
    else
        log_error "部署完成但健康检查失败，请检查日志"
        exit 1
    fi
}

# 脚本入口
case "${1:-deploy}" in
    deploy)
        main
        ;;
    backup)
        backup_existing
        ;;
    health)
        health_check
        ;;
    restart)
        supervisorctl restart $PROJECT_NAME
        systemctl restart apache2
        ;;
    logs)
        tail -f $LOG_DIR/gunicorn.log
        ;;
    *)
        echo "用法: $0 {deploy|backup|health|restart|logs}"
        exit 1
        ;;
esac
```

### 9. 监控和维护脚本

```python
# deployment/monitor.py
"""系统监控脚本"""

import psutil
import requests
import time
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json
import os
from datetime import datetime

class SystemMonitor:
    """系统监控器"""

    def __init__(self, config_file='monitor_config.json'):
        self.config = self.load_config(config_file)
        self.setup_logging()

    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            'app_url': 'http://localhost:5000',
            'check_interval': 60,  # 检查间隔(秒)
            'cpu_threshold': 80,   # CPU使用率阈值
            'memory_threshold': 80, # 内存使用率阈值
            'disk_threshold': 90,   # 磁盘使用率阈值
            'email': {
                'enabled': False,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'to_addresses': []
            },
            'log_file': '/var/log/vehicle-counter/monitor.log'
        }

        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)

        return default_config

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config['log_file']),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def check_system_resources(self):
        """检查系统资源"""
        alerts = []

        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > self.config['cpu_threshold']:
            alerts.append(f"CPU使用率过高: {cpu_percent:.1f}%")

        # 内存使用率
        memory = psutil.virtual_memory()
        if memory.percent > self.config['memory_threshold']:
            alerts.append(f"内存使用率过高: {memory.percent:.1f}%")

        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent > self.config['disk_threshold']:
            alerts.append(f"磁盘使用率过高: {disk_percent:.1f}%")

        return alerts

    def check_application_health(self):
        """检查应用健康状态"""
        try:
            response = requests.get(
                self.config['app_url'],
                timeout=10
            )

            if response.status_code == 200:
                return True, "应用响应正常"
            else:
                return False, f"应用响应异常: HTTP {response.status_code}"

        except requests.exceptions.RequestException as e:
            return False, f"应用无法访问: {str(e)}"

    def check_process_status(self):
        """检查进程状态"""
        alerts = []

        # 检查Gunicorn进程
        gunicorn_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'gunicorn' in proc.info['name']:
                    gunicorn_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if not gunicorn_processes:
            alerts.append("Gunicorn进程未运行")
        elif len(gunicorn_processes) < 2:
            alerts.append(f"Gunicorn工作进程数量不足: {len(gunicorn_processes)}")

        return alerts

    def send_alert_email(self, subject, message):
        """发送告警邮件"""
        if not self.config['email']['enabled']:
            return

        try:
            msg = MIMEMultipart()
            msg['From'] = self.config['email']['username']
            msg['To'] = ', '.join(self.config['email']['to_addresses'])
            msg['Subject'] = subject

            msg.attach(MIMEText(message, 'plain', 'utf-8'))

            server = smtplib.SMTP(
                self.config['email']['smtp_server'],
                self.config['email']['smtp_port']
            )
            server.starttls()
            server.login(
                self.config['email']['username'],
                self.config['email']['password']
            )

            server.send_message(msg)
            server.quit()

            self.logger.info("告警邮件发送成功")

        except Exception as e:
            self.logger.error(f"发送告警邮件失败: {str(e)}")

    def run_monitoring(self):
        """运行监控"""
        self.logger.info("开始系统监控")

        while True:
            try:
                all_alerts = []

                # 检查系统资源
                resource_alerts = self.check_system_resources()
                all_alerts.extend(resource_alerts)

                # 检查应用健康状态
                app_healthy, app_message = self.check_application_health()
                if not app_healthy:
                    all_alerts.append(app_message)

                # 检查进程状态
                process_alerts = self.check_process_status()
                all_alerts.extend(process_alerts)

                # 处理告警
                if all_alerts:
                    alert_message = "\n".join(all_alerts)
                    self.logger.warning(f"发现告警: {alert_message}")

                    # 发送邮件告警
                    self.send_alert_email(
                        "车辆计数系统告警",
                        f"时间: {datetime.now()}\n\n告警信息:\n{alert_message}"
                    )
                else:
                    self.logger.info("系统状态正常")

                time.sleep(self.config['check_interval'])

            except KeyboardInterrupt:
                self.logger.info("监控程序被用户中断")
                break
            except Exception as e:
                self.logger.error(f"监控过程中发生错误: {str(e)}")
                time.sleep(self.config['check_interval'])

if __name__ == '__main__':
    monitor = SystemMonitor()
    monitor.run_monitoring()
```

---

## 🎯 项目总结与学习指南

### 技术栈总览

```mermaid
graph TB
    A[YOLOv11车辆计数系统] --> B[AI算法层]
    A --> C[后端服务层]
    A --> D[前端界面层]
    A --> E[部署运维层]

    B --> B1[YOLOv11目标检测]
    B --> B2[DeepSORT多目标追踪]
    B --> B3[智能计数算法]

    C --> C1[Flask Web框架]
    C --> C2[WebSocket实时通信]
    C --> C3[文件上传处理]

    D --> D1[响应式HTML5界面]
    D --> D2[JavaScript交互逻辑]
    D --> D3[实时数据可视化]

    E --> E1[Apache反向代理]
    E --> E2[Gunicorn WSGI服务器]
    E --> E3[Supervisor进程管理]
    E --> E4[系统监控告警]
```

### 核心学习要点

#### 1. **AI算法理解**
- **目标检测**: YOLO系列算法的演进和原理
- **多目标追踪**: 卡尔曼滤波、数据关联、轨迹管理
- **计数逻辑**: 虚拟线圈、轨迹分析、去重策略

#### 2. **系统架构设计**
- **分层架构**: 表现层、业务层、数据层的职责分离
- **模块化设计**: 高内聚、低耦合的组件设计
- **接口设计**: RESTful API和WebSocket的合理使用

#### 3. **工程实践**
- **代码组织**: 清晰的目录结构和命名规范
- **错误处理**: 异常捕获、日志记录、优雅降级
- **性能优化**: 多线程处理、资源管理、缓存策略

#### 4. **部署运维**
- **服务器配置**: Apache、Gunicorn、Supervisor的协同工作
- **监控告警**: 系统资源监控、应用健康检查
- **自动化部署**: 脚本化部署、版本管理、回滚策略

### 进阶学习路径

#### 🎓 **初学者 (0-6个月)**
1. **基础知识**
   - Python编程基础
   - OpenCV图像处理
   - 深度学习概念
   - Web开发基础

2. **实践项目**
   - 简单的图像分类
   - 基础的目标检测
   - 静态网页开发

#### 🚀 **进阶者 (6-18个月)**
1. **深入算法**
   - YOLO系列算法详解
   - 目标追踪算法研究
   - 模型训练和优化

2. **系统开发**
   - Flask/Django框架
   - 数据库设计
   - API设计和开发

#### 🏆 **专家级 (18个月+)**
1. **架构设计**
   - 微服务架构
   - 分布式系统
   - 高并发处理

2. **产品化**
   - 用户体验设计
   - 性能优化
   - 商业化考虑

### 实际应用场景

#### 🚦 **智能交通**
- 交通流量统计
- 违章行为检测
- 智能信号控制

#### 🏪 **零售分析**
- 客流统计
- 顾客行为分析
- 商品热度分析

#### 🏭 **工业监控**
- 生产线监控
- 质量检测
- 安全监控

### 技术发展趋势

#### 🔮 **未来方向**
1. **边缘计算**: 在设备端直接处理，减少延迟
2. **联邦学习**: 保护隐私的分布式训练
3. **多模态融合**: 结合视觉、声音、传感器数据
4. **实时优化**: 动态调整模型参数

#### 💡 **创新机会**
1. **算法优化**: 更快、更准确的检测算法
2. **应用拓展**: 新的应用场景和商业模式
3. **用户体验**: 更直观、更智能的交互方式
4. **生态建设**: 开发者工具、社区建设

---

## 🎉 结语

这个YOLOv11车辆计数系统项目展现了现代AI应用开发的完整生命周期，从算法研究到产品部署，从技术实现到用户体验，涵盖了AI工程师需要掌握的各个方面。

通过这个项目，你可以学到：

✅ **技术深度**: 深度学习算法的实际应用
✅ **工程能力**: 完整系统的设计和实现
✅ **产品思维**: 用户需求驱动的功能设计
✅ **架构视野**: 可扩展、可维护的系统架构
✅ **运维实践**: 生产环境的部署和监控

这正是成为优秀AI架构师和系统工程师的必经之路！

**记住**: 技术是手段，解决实际问题才是目标。保持学习热情，持续实践创新，你一定能在AI领域取得成功！🚀
```
