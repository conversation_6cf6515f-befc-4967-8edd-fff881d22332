#!/usr/bin/env python3
"""
配置管理 - 集中管理系统设置和参数
"""

from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class TrackerConfig:
    """跟踪器配置"""
    max_dist: float = 0.3
    max_iou_distance: float = 0.7
    max_age: int = 150
    n_init: int = 3
    nn_budget: int = 100
    device: str = 'cpu'

class Settings:
    """系统设置"""
    
    # 环境路径配置
    LOCAL_BASE_PATH = "D:/lkr_yolo/yolov11_vehicle_counter"
    SERVER_BASE_PATH = "C:/lkr_yolo/lkr_yolo/yolov11_vehicle_counter"
    
    # 模型配置
    YOLO_MODEL_NAMES = [
        "yolov11m_vehicle_detection_portable",
        "yolov11m_vehicle_detection",
        "yolov11m.pt"  # 预训练模型作为最后备选
    ]
    
    # YOLO推理参数 - 优化性能，解决NMS超时问题
    YOLO_INFERENCE_PARAMS = {
        'conf': 0.5,        # 置信度阈值
        'iou': 0.7,         # NMS IoU阈值
        'max_det': 300,     # 最大检测数量
        'verbose': False,   # 关闭详细输出
        'persist': True     # 持续跟踪
    }
    
    # 跟踪器配置
    TRACKER_CONFIG = TrackerConfig()
    
    # 视频处理配置
    VIDEO_CONFIG = {
        'fourcc': 'mp4v',
        'progress_interval': 100,  # 每100帧显示一次进度
        'max_frames': None,        # 最大处理帧数，None表示处理全部
    }
    
    # 计数线配置
    COUNTING_LINE_CONFIG = {
        'position_ratio': 0.5,     # 计数线位置（视频宽度的比例）
        'color': (0, 255, 0),      # 计数线颜色 (BGR)
        'thickness': 2             # 计数线粗细
    }
    
    # 显示配置
    DISPLAY_CONFIG = {
        'font': 'cv2.FONT_HERSHEY_SIMPLEX',
        'font_scale': 1,
        'font_thickness': 2,
        'left_count_pos': (50, 50),
        'right_count_color': (255, 0, 0),  # 蓝色
        'left_count_color': (0, 0, 255),   # 红色
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_handler': True,
        'console_handler': True,
        'log_file': 'vehicle_counter.log'
    }
    
    # 错误处理配置
    ERROR_HANDLING_CONFIG = {
        'max_consecutive_errors': 10,  # 最大连续错误数
        'error_recovery_delay': 1,     # 错误恢复延迟（秒）
        'enable_fallback': True,       # 启用备用机制
    }
    
    @classmethod
    def get_model_path_priority(cls) -> List[str]:
        """获取模型路径优先级列表"""
        return [
            "runs/train/yolov11m_vehicle_detection_portable/weights/best.pt",
            "runs/train/yolov11m_vehicle_detection/weights/best.pt",
            "yolov11m.pt"
        ]
    
    @classmethod
    def get_video_extensions(cls) -> List[str]:
        """获取支持的视频格式"""
        return ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
    
    @classmethod
    def validate_config(cls) -> Dict[str, bool]:
        """验证配置有效性"""
        validation_results = {}
        
        # 验证YOLO推理参数
        inference_params = cls.YOLO_INFERENCE_PARAMS
        validation_results['conf_valid'] = 0.0 <= inference_params['conf'] <= 1.0
        validation_results['iou_valid'] = 0.0 <= inference_params['iou'] <= 1.0
        validation_results['max_det_valid'] = inference_params['max_det'] > 0
        
        # 验证跟踪器参数
        tracker_config = cls.TRACKER_CONFIG
        validation_results['tracker_valid'] = all([
            tracker_config.max_dist > 0,
            tracker_config.max_iou_distance > 0,
            tracker_config.max_age > 0,
            tracker_config.n_init > 0,
            tracker_config.nn_budget > 0
        ])
        
        return validation_results