#!/bin/bash

# ========================================
# 服务器部署脚本 - 在服务器上运行
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示欢迎信息
show_welcome() {
    clear
    echo "========================================"
    echo "🚗 YOLOv11车辆计数系统 - 服务器部署"
    echo "========================================"
    echo "此脚本在服务器上运行"
    echo "域名: vehicle.smart-traffic.top"
    echo "项目目录: /var/www/vehicle-counter"
    echo "========================================"
    echo
}

# 检查上传的文件
check_uploaded_files() {
    log_info "检查上传的文件..."
    
    # 检查webapp目录中的文件
    if [ -d "$HOME/webapp" ]; then
        cd "$HOME/webapp"
        local files=("app.py" "deploy_config.py" "start_server.py" "requirements.txt")
        local dirs=("templates" "static")
        local missing_files=()

        for file in "${files[@]}"; do
            if [ ! -f "$file" ]; then
                missing_files+=("$file")
            fi
        done

        for dir in "${dirs[@]}"; do
            if [ ! -d "$dir" ]; then
                missing_files+=("${dir}目录")
            fi
        done
    else
        # 检查旧格式的文件
        local files=("app.py" "requirements.txt")
        local missing_files=()

        for file in "${files[@]}"; do
            if [ ! -f "$HOME/$file" ]; then
                missing_files+=("$file")
            fi
        done

        if [ ! -d "$HOME/static" ]; then
            missing_files+=("static目录")
        fi
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少以下文件: ${missing_files[*]}"
        log_error "请先运行本地上传脚本上传文件"
        exit 1
    fi
    
    log_success "上传文件检查通过"
}

# 创建项目目录并移动文件
setup_project_directory() {
    log_info "设置项目目录..."
    
    # 创建项目目录
    sudo mkdir -p /var/www/vehicle-counter
    sudo chown admin:admin /var/www/vehicle-counter
    
    # 移动文件
    mv ~/app.py /var/www/vehicle-counter/
    mv ~/index.html /var/www/vehicle-counter/
    mv ~/requirements.txt /var/www/vehicle-counter/
    mv ~/static /var/www/vehicle-counter/
    
    # 验证文件
    ls -la /var/www/vehicle-counter/
    
    log_success "项目目录设置完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新系统
    sudo apt update
    
    # 安装Python环境
    sudo apt install -y python3-pip python3-venv python3-dev build-essential
    
    # 安装OpenCV依赖
    sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 ffmpeg
    
    # 安装Web服务器和进程管理
    sudo apt install -y supervisor apache2
    
    # 安装SSL证书工具
    sudo apt install -y certbot python3-certbot-apache
    
    log_success "系统依赖安装完成"
}

# 设置Python环境
setup_python_environment() {
    log_info "设置Python虚拟环境..."
    
    cd /var/www/vehicle-counter
    
    # 创建虚拟环境
    python3 -m venv venv
    
    # 激活虚拟环境并安装依赖
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    pip install gunicorn
    
    log_success "Python环境设置完成"
}

# 创建应用配置文件
create_app_configs() {
    log_info "创建应用配置文件..."
    
    # 创建Gunicorn配置
    cat > /var/www/vehicle-counter/gunicorn.conf.py << 'EOF'
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF
    
    # 创建启动脚本
    cat > /var/www/vehicle-counter/start.sh << 'EOF'
#!/bin/bash
cd /var/www/vehicle-counter
source venv/bin/activate
export FLASK_ENV=production
exec gunicorn --config gunicorn.conf.py app:app
EOF
    
    # 设置执行权限
    chmod +x /var/www/vehicle-counter/start.sh
    
    log_success "应用配置文件创建完成"
}

# 配置Apache虚拟主机
configure_apache() {
    log_info "配置Apache虚拟主机..."
    
    # 启用Apache模块
    sudo a2enmod rewrite proxy proxy_http headers ssl
    
    # 创建虚拟主机配置
    sudo tee /etc/apache2/sites-available/vehicle-counter.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerName vehicle.smart-traffic.top
    ServerAlias www.vehicle.smart-traffic.top
    
    DocumentRoot /var/www/vehicle-counter
    
    Alias /static /var/www/vehicle-counter/static
    <Directory /var/www/vehicle-counter/static>
        Require all granted
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </Directory>
    
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:5000/api/
    ProxyPassReverse /api/ http://127.0.0.1:5000/api/
    
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    LimitRequestBody 104857600
    
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    ErrorLog ${APACHE_LOG_DIR}/vehicle-counter_error.log
    CustomLog ${APACHE_LOG_DIR}/vehicle-counter_access.log combined
</VirtualHost>
EOF
    
    # 启用站点
    sudo a2ensite vehicle-counter.conf
    
    # 重新加载Apache配置
    sudo systemctl reload apache2
    
    log_success "Apache虚拟主机配置完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor进程管理..."
    
    # 创建Supervisor配置
    sudo tee /etc/supervisor/conf.d/vehicle-counter.conf > /dev/null << 'EOF'
[program:vehicle-counter]
command=/var/www/vehicle-counter/start.sh
directory=/var/www/vehicle-counter
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/vehicle-counter.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/var/www/vehicle-counter/venv/bin"
EOF
    
    # 重新加载Supervisor配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    # 启动应用
    sudo supervisorctl start vehicle-counter
    
    log_success "Supervisor配置完成"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 等待应用启动
    sleep 5
    
    # 检查应用状态
    echo "应用状态:"
    sudo supervisorctl status vehicle-counter
    
    echo
    echo "测试本地API:"
    curl -s http://localhost:5000/api/health || echo "API测试失败"
    
    echo
    echo "Apache状态:"
    sudo systemctl status apache2 --no-pager -l
    
    log_success "部署测试完成"
}

# 显示完成信息
show_completion() {
    echo
    echo "========================================"
    echo "🎉 服务器部署完成！"
    echo "========================================"
    echo "🌐 网站地址: http://vehicle.smart-traffic.top"
    echo "📊 健康检查: http://vehicle.smart-traffic.top/api/health"
    echo "📁 项目目录: /var/www/vehicle-counter"
    echo "📝 应用日志: /var/log/vehicle-counter.log"
    echo "========================================"
    echo
    echo "🔧 常用管理命令:"
    echo "重启应用: sudo supervisorctl restart vehicle-counter"
    echo "查看状态: sudo supervisorctl status vehicle-counter"
    echo "查看日志: sudo tail -f /var/log/vehicle-counter.log"
    echo
    echo "🌐 下一步操作:"
    echo "1. 配置域名DNS解析: vehicle -> 47.236.30.93"
    echo "2. 配置SSL证书: sudo certbot --apache -d vehicle.smart-traffic.top"
    echo "========================================"
}

# 主函数
main() {
    show_welcome
    check_uploaded_files
    setup_project_directory
    install_system_dependencies
    setup_python_environment
    create_app_configs
    configure_apache
    configure_supervisor
    test_deployment
    show_completion
}

# 运行主函数
main "$@"
