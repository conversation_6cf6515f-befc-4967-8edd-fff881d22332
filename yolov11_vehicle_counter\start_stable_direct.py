#!/usr/bin/env python3
"""
直接启动稳定版Web应用
"""

import os
import sys
from pathlib import Path

# 设置环境变量（在导入任何库之前）
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'

print("🛡️  YOLOv11车辆计数器 - 稳定版直接启动")
print("=" * 60)
print("🔧 环境变量已设置:")
print(f"   KMP_DUPLICATE_LIB_OK = {os.environ.get('KMP_DUPLICATE_LIB_OK')}")
print(f"   OMP_NUM_THREADS = {os.environ.get('OMP_NUM_THREADS')}")
print("=" * 60)

# 切换到webapp目录
webapp_dir = Path(__file__).parent / "webapp"
os.chdir(webapp_dir)

print(f"📁 当前目录: {os.getcwd()}")

# 导入并运行稳定版应用
try:
    print("🚀 启动稳定版Web应用...")
    import app_stable_ultimate
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")